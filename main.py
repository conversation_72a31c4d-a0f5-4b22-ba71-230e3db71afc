# main.py
"""
加密货币多周期预测程序主入口
提供机器学习模型训练、实时预测、GUI界面等功能
"""

# === 标准库导入 ===
import argparse
import glob
import importlib
import json
import os
import threading
import time
import traceback
import warnings
from datetime import datetime, timezone, timedelta

# === 时区处理 ===
try:
    from zoneinfo import ZoneInfo
except ImportError:
    try:
        import pytz
        ZoneInfo = pytz.timezone
        print("Using pytz for timezone.")
    except ImportError:
        ZoneInfo = None
        print("!!! No zoneinfo or pytz found for timezone.")

# === 第三方库导入 ===
import numpy as np
import pandas as pd
import requests
import joblib
import functools

# === GUI相关导入 ===
import tkinter as tk
from tkinter import ttk, messagebox

# === 机器学习库导入 ===
import lightgbm as lgb
from lightgbm import LGBMClassifier, early_stopping
import optuna
from sklearn.metrics import (
    classification_report, accuracy_score, log_loss, f1_score,
    roc_auc_score, average_precision_score, brier_score_loss,
    precision_score, recall_score
)
from sklearn.feature_selection import RFECV
from sklearn.model_selection import StratifiedKFold, TimeSeriesSplit
from sklearn.calibration import CalibratedClassifierCV
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from imblearn.over_sampling import SMOTE

# === Binance API导入 ===
from binance import ThreadedWebsocketManager
from binance.client import Client
from binance.exceptions import BinanceAPIException, BinanceRequestException

# === 调度器导入 ===
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
import pytz

# === 项目内部导入 ===
import config
from SimMain import SimApplication
import src.core.data_utils as data_utils
import src.core.prediction as prediction
import src.core.gui as gui
import src.core.realtime_data_manager as realtime_data_manager
import configs.auto_retrain_scheduler as auto_retrain_scheduler
from src.analysis.analysis_logger import initialize_loggers
from src.core.config_validator import (
    validate_and_load_config, ValidationLevel, RobustConfigManager,
    ConfigurationError, TargetConfigNotFoundError
)
from src.core.application_state import ApplicationState, get_app_state, PredictionResult
from src.utils.dynamic_config_manager import DynamicConfigManager
from src.utils.DataValidator import DataValidator
# 屏蔽LightGBM的警告信息
warnings.filterwarnings('ignore', category=UserWarning, module='lightgbm')
warnings.filterwarnings('ignore', message='.*No further splits with positive gain.*')
warnings.filterwarnings('ignore', message='.*best gain.*')

import lightgbm as lgb
from lightgbm import LGBMClassifier
from lightgbm import early_stopping
# ... (您现有的其他 sklearn 等导入)
from sklearn.metrics import (classification_report, # <--- 加入这个
                             accuracy_score, log_loss, f1_score, roc_auc_score,
                             average_precision_score, brier_score_loss,
                             precision_score, recall_score)
from sklearn.feature_selection import RFECV
from sklearn.model_selection import StratifiedKFold, TimeSeriesSplit
from sklearn.calibration import CalibratedClassifierCV
from imblearn.over_sampling import SMOTE  # 添加SMOTE导入
import optuna
import pandas as pd
import json
import functools # Optuna 可能用到
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from SimMain import SimApplication

import config
import src.core.data_utils as data_utils
import src.core.prediction as prediction
import src.core.gui as gui
import src.core.realtime_data_manager as realtime_data_manager
import configs.auto_retrain_scheduler as auto_retrain_scheduler
from src.analysis.analysis_logger import initialize_loggers # <--- 导入日志初始化函数
from src.core.config_validator import (
    validate_and_load_config,
    ValidationLevel,
    RobustConfigManager,
    ConfigurationError,
    TargetConfigNotFoundError
)
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.date import DateTrigger
import pytz # APScheduler 可能需要 pytz 来处理时区
from src.utils.DataValidator import DataValidator




try:
    import shap
except ImportError:
    shap = None # 如果导入失败，将 shap 设置为 None
    print("警告: 'shap' 库未找到。SHAP分析将不可用。")

try:
    import matplotlib       # <--- 首先导入 matplotlib 自身
    matplotlib.use('Agg')  # <--- 然后设置后端为 'Agg'
    import matplotlib.pyplot as plt # <--- 最后导入 pyplot
except ImportError:
    plt = None # 如果导入失败，将 plt 设置为 None
    print("警告: 'matplotlib' 或 'matplotlib.pyplot' 库未找到，或设置后端失败。SHAP绘图将不可用。")
    # 你也可以选择在这里将 matplotlib 也设为 None，如果后续代码依赖它
    if 'matplotlib' in locals() or 'matplotlib' in globals(): # 检查matplotlib是否已部分导入
        matplotlib = None


# --- 导入新的状态管理系统 ---
from src.core.application_state import ApplicationState, get_app_state, PredictionResult

# --- TWM 初始化参数 (可以放在这里，或从 config.py 获取) ---
# 默认值，如果你的 config.py 中没有定义这些，可以使用这些
DEFAULT_PROXY_URL = 'http://127.0.0.1:7897'
DEFAULT_TWM_RECONNECT_ATTEMPTS = 5
DEFAULT_TWM_RECONNECT_DELAY_MS = 5000

# --- 获取全局状态管理器实例 ---
app_state = get_app_state()

class ApplicationGlobals:
    """
    应用程序全局状态管理类
    封装全局变量，提供类型安全的访问接口
    """
    def __init__(self):
        # 核心组件
        self.shared_binance_twm = None
        self.scheduler = None
        self.binance_client = None
        self.main_root = None

        # 配置管理器
        self.dynamic_config_manager = None
        self.robust_config_manager = None

        # 状态变量
        self.last_ticker_price = None
        self.app_timezone = None
        self.simulator_url_from_cmd = None

        # 状态管理器引用
        self.app_state = get_app_state()
        self.stop_event = self.app_state.stop_event
        self.training_in_progress_flag = self.app_state.training_in_progress_flag

    def cleanup(self):
        """清理资源"""
        if self.shared_binance_twm:
            try:
                self.shared_binance_twm.stop()
            except Exception as e:
                print(f"清理TWM时出错: {e}")

        if self.scheduler:
            try:
                self.scheduler.shutdown()
            except Exception as e:
                print(f"清理调度器时出错: {e}")

        if self.dynamic_config_manager:
            try:
                self.dynamic_config_manager.stop_observer()
            except Exception as e:
                print(f"清理配置管理器时出错: {e}")

# 全局实例
app_globals = ApplicationGlobals()

# 向后兼容的全局变量（逐步废弃）
shared_binance_twm = None
scheduler = None
stop_event = app_globals.stop_event
training_in_progress_flag = app_globals.training_in_progress_flag
binance_client = None
_main_root = None
dynamic_config_manager = None
robust_config_manager = None
last_ticker_price = None
APP_TIMEZONE = None
# ... (APP_TIMEZONE 初始化逻辑不变) ...
if ZoneInfo:
    try: APP_TIMEZONE = ZoneInfo("Asia/Shanghai")
    except Exception: APP_TIMEZONE = timezone.utc
else: APP_TIMEZONE = timezone.utc
if APP_TIMEZONE is None: APP_TIMEZONE = timezone.utc

SIMULATOR_URL_FROM_CMD = None # 如果你通过命令行传递模拟盘URL

VALID_LGBM_PARAM_KEYS = []
try:
    _temp_lgbm_for_keys = LGBMClassifier()
    VALID_LGBM_PARAM_KEYS = list(_temp_lgbm_for_keys.get_params().keys())
    del _temp_lgbm_for_keys
except Exception as e_lgbm_keys:
    print(f"警告: 无法动态获取LGBM参数键列表: {e_lgbm_keys}. 将使用预定义列表。")

VALID_LGBM_PARAM_KEYS = [
    'boosting_type', 'num_leaves', 'max_depth', 'learning_rate', 'n_estimators',
    'subsample_for_bin', 'objective', 'class_weight', 'min_split_gain',
    'min_child_weight', 'min_child_samples', 'subsample', 'subsample_freq',
    'colsample_bytree', 'reg_alpha', 'reg_lambda', 'random_state', 'n_jobs',
    'importance_type', 'metric', 'device_type', 'verbose', 'verbosity', 'seed', # 确保 verbosity 也在这里
    'callbacks', 'eval_set', 'eval_names', 'eval_metric', 'feature_name',
    'categorical_feature', 'early_stopping_rounds', 'first_metric_only', 'num_class' # 如果元模型OOF也可能用到num_class
]




def initialize_shared_twm(proxy_url=None, reconnect_attempts=None, reconnect_delay_ms=None): # reconnect_attempts, reconnect_delay_ms 目前不直接用于TWM.__init__
    """初始化并启动共享的 ThreadedWebsocketManager 实例"""
    global shared_binance_twm # 声明我们要修改全局变量

    # 检查状态管理器中是否已有TWM实例
    existing_twm = app_state.get_shared_twm()
    if existing_twm and getattr(existing_twm, 'is_alive', lambda: False)():
        print("Main: 共享TWM已在运行。")
        shared_binance_twm = existing_twm  # 同步到全局变量
        return True

    # 确定代理URL
    effective_proxy_url = proxy_url
    if effective_proxy_url is None: # 如果没有从参数传入
        if hasattr(config, 'PROXY_URL') and getattr(config, 'PROXY_URL', None): # 尝试从config模块获取
            effective_proxy_url = config.PROXY_URL
        else: # 如果config模块也没有，使用文件顶部的默认值
            effective_proxy_url = DEFAULT_PROXY_URL 
            if effective_proxy_url: # 只有当默认值也有效时才打印这条信息
                 print(f"Main: 使用默认代理 URL: {effective_proxy_url}")


    # TWM的重连由其内部机制处理，这里获取的参数暂时不直接传递给TWM的构造函数
    # 但保留获取逻辑，以防将来用于配置AsyncClient或有其他用途
    effective_reconnect_attempts = reconnect_attempts
    if effective_reconnect_attempts is None and hasattr(config, 'TWM_RECONNECT_ATTEMPTS'):
        effective_reconnect_attempts = getattr(config, 'TWM_RECONNECT_ATTEMPTS', DEFAULT_TWM_RECONNECT_ATTEMPTS)
    elif effective_reconnect_attempts is None:
        effective_reconnect_attempts = DEFAULT_TWM_RECONNECT_ATTEMPTS

    effective_reconnect_delay_ms = reconnect_delay_ms
    if effective_reconnect_delay_ms is None and hasattr(config, 'TWM_RECONNECT_DELAY_MS'):
        effective_reconnect_delay_ms = getattr(config, 'TWM_RECONNECT_DELAY_MS', DEFAULT_TWM_RECONNECT_DELAY_MS)
    elif effective_reconnect_delay_ms is None:
        effective_reconnect_delay_ms = DEFAULT_TWM_RECONNECT_DELAY_MS
        
    try:
        import aiohttp # 确保 aiohttp 已导入
        # 配置 aiohttp 会话参数，这些会传递给 TWM 内部的 AsyncClient
        session_timeout_config = aiohttp.ClientTimeout(
            total=getattr(config, 'AIOHTTP_TOTAL_TIMEOUT', 60), 
            connect=getattr(config, 'AIOHTTP_CONNECT_TIMEOUT', 25)
        )
        session_init_params = {
            "timeout": session_timeout_config,
            "trust_env": True, # 允许 aiohttp 从环境变量读取代理（例如 HTTP_PROXY, HTTPS_PROXY）
            "connector": None, # 使用默认的 aiohttp.TCPConnector
        }
        
        twm_init_kwargs = {
            "api_key": None, # 公共流通常不需要API密钥
            "api_secret": None,
            "requests_params": {"timeout": session_timeout_config}, # 主要影响AsyncClient的HTTP请求超时
            "session_params": session_init_params
        }
        
        # 如果配置了代理URL，显式传递给TWM，它会设置给AsyncClient
        # 注意：如果 trust_env=True 并且系统环境变量也设置了代理，可能会有优先级问题，
        # 通常显式传递的 https_proxy 会覆盖环境变量。
        if effective_proxy_url:
            twm_init_kwargs["https_proxy"] = effective_proxy_url
            print(f"Main: 正在初始化共享TWM，使用显式代理: {effective_proxy_url}")
        else:
            print(f"Main: 正在初始化共享TWM，不使用显式代理 (将依赖环境变量或直接连接)。")
        
        new_twm = ThreadedWebsocketManager(**twm_init_kwargs)
        new_twm.start() # 启动TWM的工作线程和事件循环

        # 使用可配置的等待时间或根据实际情况动态判断
        init_wait_time = getattr(config, 'TWM_INIT_WAIT_TIME', 2)
        print(f"Main: 共享TWM已请求启动，等待约{init_wait_time}秒进行内部初始化...")

        # 分段等待，每0.5秒检查一次TWM状态
        waited_time = 0
        check_interval = 0.5
        while waited_time < init_wait_time:
            time.sleep(check_interval)
            waited_time += check_interval
            # 如果TWM已经准备好，提前结束等待
            if getattr(new_twm, 'is_alive', lambda: False)():
                print(f"Main: TWM已准备就绪，用时{waited_time:.1f}秒")
                break

        if getattr(new_twm, 'is_alive', lambda: False)():
            print("Main: 共享TWM初始化成功并正在运行。")
            # 同时设置到状态管理器和全局变量
            app_state.set_shared_twm(new_twm)
            shared_binance_twm = new_twm
            return True
        else:
            print("!!! Main: 共享TWM启动后未能保持活动状态！请检查网络和代理配置。")
            if new_twm: # 如果对象已创建但未成功启动，尝试停止
                try:
                    new_twm.stop() # 尝试停止以清理资源
                except Exception as e_stop_fail:
                    print(f"!!! Main: 尝试停止未成功启动的TWM时出错: {e_stop_fail}")
            return False
            
    except ImportError:
        print("!!! Main: 严重错误 - 未找到 'aiohttp' 模块，无法配置TWM会话参数。")
        traceback.print_exc()
        shared_binance_twm = None
        return False
    except (AttributeError, TypeError) as e_config:
        print(f"!!! Main: TWM配置错误: {e_config}")
        traceback.print_exc()
        shared_binance_twm = None
        return False
    except (ConnectionError, TimeoutError) as e_network:
        print(f"!!! Main: TWM网络连接错误: {e_network}")
        traceback.print_exc()
        shared_binance_twm = None
        return False
    except Exception as e_twm_init:
        print(f"!!! Main: 初始化共享TWM时发生未知错误: {type(e_twm_init).__name__}: {e_twm_init}")
        traceback.print_exc()
        if shared_binance_twm:
            try:
                shared_binance_twm.stop()
            except Exception as e_stop_generic_fail:
                print(f"!!! Main: 尝试停止出错的TWM时出错: {e_stop_generic_fail}")
        shared_binance_twm = None
        return False


class ResourceManager:
    """资源管理器，负责安全地管理和清理系统资源"""

    @staticmethod
    def stop_twm_safely(twm_instance, timeout: int = 5) -> bool:
        """
        安全地停止ThreadedWebsocketManager实例

        Args:
            twm_instance: TWM实例
            timeout: 等待超时时间（秒）

        Returns:
            bool: 是否成功停止
        """
        if not twm_instance:
            print("ResourceManager: TWM实例为None，无需停止")
            return True

        if not getattr(twm_instance, 'is_alive', lambda: False)():
            print("ResourceManager: TWM实例未激活，无需停止")
            return True

        print("ResourceManager: 正在停止ThreadedWebsocketManager...")

        try:
            twm_instance.stop()
        except Exception as e_stop:
            print(f"!!! ResourceManager: 停止TWM时出错: {type(e_stop).__name__}: {e_stop}")
            return False

        # 等待线程优雅退出
        waited_time = 0
        check_interval = 0.5

        while getattr(twm_instance, 'is_alive', lambda: False)() and waited_time < timeout:
            print(f"ResourceManager: 等待TWM线程退出... ({waited_time:.1f}s / {timeout}s)")
            time.sleep(check_interval)
            waited_time += check_interval

        if getattr(twm_instance, 'is_alive', lambda: False)():
            print("!!! ResourceManager: TWM未能在超时时间内停止")
            return False
        else:
            print("ResourceManager: TWM已成功停止")
            return True

def stop_shared_twm():
    """停止共享的 ThreadedWebsocketManager 实例（向后兼容接口）"""
    global shared_binance_twm

    success = ResourceManager.stop_twm_safely(shared_binance_twm)
    shared_binance_twm = None

    return success




def job_wrapper_meta_model(p_binance_client, p_app_timezone, p_simulator_url):
    """APScheduler 作业的包装函数，用于调用元模型预测。"""
    print(f"APScheduler Job: 触发元模型预测 at {datetime.now(p_app_timezone).strftime('%Y-%m-%d %H:%M:%S')}")
    if training_in_progress_flag.is_set():
        print("APScheduler Job (Meta): 训练进行中，跳过本次计划预测。")
        return
    if not prediction.global_prediction_state_manager.is_meta_model_loaded_successfully():
        print("APScheduler Job (Meta): 元模型未加载，尝试加载...")
        if not prediction.load_meta_model_if_needed():
            print("!!! APScheduler Job (Meta): 元模型加载失败，跳过预测。")
            return
    try:
        prediction.run_meta_prediction_for_current_trigger(p_binance_client, p_app_timezone, p_simulator_url)
    except Exception as e_job_meta:
        print(f"!!! APScheduler Job (Meta) 执行出错: {e_job_meta}")
        traceback.print_exc(limit=1)

def job_wrapper_base_model(target_name_for_job, p_binance_client, p_app_timezone, p_simulator_url):
    """APScheduler 作业的包装函数，用于调用单个基础模型的完整预测。"""
    print(f"APScheduler Job: 触发基础模型 '{target_name_for_job}' 预测 at {datetime.now(p_app_timezone).strftime('%Y-%m-%d %H:%M:%S')}")
    if training_in_progress_flag.is_set():
        print(f"APScheduler Job (Base '{target_name_for_job}'): 训练进行中，跳过。")
        return
    try:
        base_cfg_to_run = safe_get_target_config(target_name_for_job)
        prediction.run_prediction_cycle_for_target(
            base_cfg_to_run, p_binance_client, p_app_timezone,
            return_core_prediction_only=False,
            simulator_actual_url=p_simulator_url
        )
    except ValueError as e_get_cfg:
        print(f"!!! APScheduler Job (Base '{target_name_for_job}'): 获取配置失败: {e_get_cfg}")
    except Exception as e_job_base:
        print(f"!!! APScheduler Job (Base '{target_name_for_job}') 执行出错: {e_job_base}")
        traceback.print_exc(limit=1)


def setup_and_start_apscheduler():
    global scheduler, binance_client, APP_TIMEZONE # 确保能访问到这些全局变量
    
    aps_timezone_str = getattr(config, 'APScheduler_TIMEZONE', str(APP_TIMEZONE))
    try:
        # 尝试使用 pytz 获取时区对象，APScheduler推荐
        aps_tz = pytz.timezone(aps_timezone_str)
    except Exception:
        print(f"警告: APScheduler 未能加载时区 '{aps_timezone_str}' from pytz. 将使用本地系统时区或UTC。")
        aps_tz = None # 或者 datetime.timezone.utc

    scheduler = BackgroundScheduler(timezone=aps_tz)
    # 同时设置到状态管理器
    app_state.set_scheduler(scheduler)
    print(f"APScheduler: 使用时区 {scheduler.timezone}")

    simulator_url = getattr(config, 'SIMULATOR_API_URL', None) # 获取模拟盘URL

    if not hasattr(config, 'PREDICTION_TARGETS') or not config.PREDICTION_TARGETS:
        print("APScheduler: config.PREDICTION_TARGETS 未配置或为空，无作业添加。")
        return

    for target_config_item in config.PREDICTION_TARGETS:
        target_name = target_config_item.get('name')
        if not target_name: continue

        if target_config_item.get("apscheduler_job_enabled", False): # 检查作业是否启用
            trigger_type = target_config_item.get("apscheduler_trigger_type")
            job_id = f"job_{target_name.replace(' ', '_')}" # 创建唯一的作业ID
            
            # 确定要调用的函数及其参数
            func_to_call = None
            args_for_call = []
            
            is_meta_target_entry = (target_config_item.get('target_variable_type') == "META_MODEL_DISPLAY" or \
                                   target_name == getattr(config, 'META_MODEL_VIRTUAL_TARGET_NAME_FOR_DYNAMIC_PARAMS', None) or \
                                   target_name == getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', None))

            if is_meta_target_entry:
                if getattr(config, 'ENABLE_META_MODEL_PREDICTION', True):
                    func_to_call = job_wrapper_meta_model
                    args_for_call = [binance_client, APP_TIMEZONE, simulator_url]
                else:
                    print(f"  APScheduler: 元模型 '{target_name}' 的作业已配置但元模型预测被禁用，跳过添加。")
                    continue
            else: # 是一个基础模型
                func_to_call = job_wrapper_base_model
                args_for_call = [target_name, binance_client, APP_TIMEZONE, simulator_url] # 第一个参数是target_name

            if not func_to_call:
                print(f"  APScheduler: 未能为 '{target_name}' 确定要调度的函数，跳过。")
                continue

            try:
                if trigger_type == "cron" and target_config_item.get("apscheduler_cron_config"):
                    cron_args = target_config_item.get("apscheduler_cron_config")
                    scheduler.add_job(func_to_call, CronTrigger.from_crontab(cron_args) if isinstance(cron_args, str) else CronTrigger(**cron_args),
                                      args=args_for_call, id=job_id, name=target_name, replace_existing=True, misfire_grace_time=120)
                    print(f"  APScheduler: 已为 '{target_name}' 添加 CRON 作业 (ID: {job_id}), 配置: {cron_args}")
                
                elif trigger_type == "interval" and target_config_item.get("apscheduler_interval_config"):
                    interval_args = target_config_item.get("apscheduler_interval_config")
                    scheduler.add_job(func_to_call, IntervalTrigger(**interval_args),
                                      args=args_for_call, id=job_id, name=target_name, replace_existing=True, misfire_grace_time=120)
                    print(f"  APScheduler: 已为 '{target_name}' 添加 INTERVAL 作业 (ID: {job_id}), 配置: {interval_args}")

                elif trigger_type == "date" and target_config_item.get("apscheduler_date_config"):
                    date_arg = target_config_item.get("apscheduler_date_config")
                    scheduler.add_job(func_to_call, DateTrigger(**date_arg) if isinstance(date_arg, dict) else DateTrigger(run_date=date_arg),
                                      args=args_for_call, id=job_id, name=target_name, replace_existing=True)
                    print(f"  APScheduler: 已为 '{target_name}' 添加 DATE 作业 (ID: {job_id}), 配置: {date_arg}")
                
                elif trigger_type: # 如果指定了类型但配置不匹配
                     print(f"  APScheduler: 目标 '{target_name}' 指定了触发类型 '{trigger_type}' 但缺少对应的配置 (e.g., apscheduler_cron_config)，跳过。")
            
            except Exception as e_add_job:
                print(f"!!! APScheduler: 为 '{target_name}' 添加作业失败: {e_add_job}")
                traceback.print_exc(limit=1)

    if scheduler.get_jobs():
        try:
            scheduler.start()
            print("APScheduler 已启动并开始运行计划作业。")
        except Exception as e_start_scheduler:
            print(f"!!! APScheduler 启动失败: {e_start_scheduler}")
            scheduler = None # 标记为未成功启动
    else:
        print("APScheduler: 没有找到有效的作业进行调度。")





class PredictionApp:
    def __init__(self, master_root, display_symbol_for_gui):
        self.master = master_root
        self.gui_display_symbol = display_symbol_for_gui
        self.master.title(f"加密货币预测 ({self.gui_display_symbol} - WebSocket)")

        gui.build_gui(
            self.master,
            train_callback_all=start_training_all,       # <--- 绑定到新函数
            train_callback_up=start_training_up_only,    # <--- 新绑定
            train_callback_down=start_training_down_only,# <--- 新绑定
            train_meta_callback=start_training_meta_model_only,  # <--- 新增元模型训练按钮
            predict_callback=start_prediction,
            close_callback=on_closing
        )



# --- Initialization & Background Threads ---
def initialize_binance_client():
    global binance_client
    print("正在初始化 Binance 客户端...")
    client_ready = False
    # API密钥从config.py中获取
    api_key = getattr(config, 'API_KEY', None)
    api_secret = getattr(config, 'API_SECRET', None)
    client_instance = None # 先声明

    if not api_key or not api_secret:
        print("警告: config.py 中未完整配置API_KEY或API_SECRET。客户端将以非认证模式运行（如果支持），或初始化可能失败。")
        # 对于 python-binance, Client() 仍然可以匿名访问公共端点
        try:
            client_instance = Client() # 尝试匿名初始化
            # 匿名客户端无法ping或访问私有数据，但可以用于TWM的公共流
            print("  Binance 客户端已匿名模式初始化 (仅限公共数据访问)。")
            # 对于TWM的公共流，匿名client通常不是必需的，TWM可以自己处理
            # 但如果某些 fetch_binance_history 调用期望一个 client 对象，即使是匿名的，也比None好
            binance_client = client_instance # 即使是匿名，也赋值
            client_ready = True # 标记为“准备好”，但功能受限
        except Exception as e_anon_init:
            print(f"!!! 匿名 Binance Client 初始化失败: {e_anon_init}")
            binance_client = None # 确保是None
            client_ready = False
        # return client_ready # 即使匿名也返回，因为某些公共调用可能仍想尝试
    else: # 有API密钥
        try:
            print("  [调试] 准备创建认证的 Client...")
            # 增加连接和读取超时，例如各10秒
            client_instance = Client(api_key, api_secret, requests_params={"timeout": (10, 10)})
            
            if client_instance:
                print("  [调试] Client 对象已创建。")
                print("  [调试] 测试与服务器的连通性 (get_server_time)...")
                start_ping = time.time()
                server_time = client_instance.get_server_time() # 使用这个权重较低的请求测试连接
                ping_dur = time.time() - start_ping
                print(f"  [调试] 服务器时间获取成功 (耗时:{ping_dur:.3f}s). 服务器时间: {server_time}")
                binance_client = client_instance
                # 同时设置到状态管理器
                app_state.set_binance_client(client_instance)
                client_ready = True
                print("认证的 Binance 客户端初始化成功并连接测试通过。")
            else:
                print("!!! 严重错误: 认证的 Client 未能创建!")
        except requests.exceptions.ConnectionError as e_conn: # requests底层可能抛出
            print(f"!!! Binance客户端初始化连接错误: {e_conn}")
        except BinanceAPIException as e_api:
            print(f"!!! Binance客户端初始化API错误 (代码 {e_api.code}): {e_api.message}")
            if e_api.code == -1003: # IP被封的特定处理
                print("!!! 警告: 您的IP地址当前可能被币安API禁止访问。请检查网络或等待解封。")
        except Exception as e_fatal:
            print(f"!!! 致命的Binance客户端初始化错误: {type(e_fatal).__name__}: {e_fatal}")
            traceback.print_exc(limit=2)

    if not client_ready:
        print("--- Binance 客户端初始化失败！某些功能（如历史数据获取、交易执行）可能不可用。---")
    return client_ready


def start_time_price_update_thread():
    thread = threading.Thread(target=time_price_update_loop, daemon=True); thread.start()
    print("时间与价格更新线程已启动。")

def time_price_update_loop():
    global last_ticker_price
    print("时间与GUI价格更新线程已启动 (从WebSocket缓存获取价格)。")
    # 确定GUI主要显示哪个交易对的价格，这个应该在主线程中配置好
    # SimApplication.__init__ 中会设置 self.gui_display_symbol
    # 这里我们先假设它能被访问，或者从config读取
    default_gui_symbol = getattr(config, 'SIMULATOR_TARGET_SYMBOL', getattr(config, 'SYMBOL', 'BTCUSDT')).upper()


    while not stop_event.is_set():
        try:
            now = datetime.now(APP_TIMEZONE)
            now_str = now.strftime('%H:%M:%S %Z') # 添加日期 %Y-%m-%d 
            gui.update_gui_safe(gui.update_time_label, now_str)

            # 从 realtime_data_manager 获取价格
            # 如果 app_instance 存在并且有 gui_display_symbol 属性
            symbol_to_display_price = default_gui_symbol
            if 'app_instance' in globals() and app_instance and hasattr(app_instance, 'gui_display_symbol'):
                 symbol_to_display_price = app_instance.gui_display_symbol

            new_price = realtime_data_manager.get_latest_price(symbol_to_display_price)
            color = config.NEUTRAL_COLOR
            price_str = f"${symbol_to_display_price[:len(symbol_to_display_price)-4]} ----.--" # 例如 BTC ----.--

            if new_price is not None:
                price_str = f"${new_price:.2f}"
                if last_ticker_price is not None: # 确保 last_ticker_price 是数字
                    if new_price > last_ticker_price: color = config.UP_COLOR
                    elif new_price < last_ticker_price: color = config.DOWN_COLOR
                last_ticker_price = new_price # 更新上一次价格
            else: # WebSocket还没收到价格，或者流中断
                color = config.ERROR_COLOR # 或者用NEUTRAL_COLOR和等待提示
            
            # 更新GUI上的价格标签，这里的 price_label_widget 可能在 gui 模块中
            # 或者 SimGUI 实例有一个 update_price_label 方法
            # 我们之前用的是 gui.update_price_label
            gui.update_gui_safe(gui.update_price_label, price_str, color)

        except Exception as e_loop:
            print(f"!!! 时间/价格更新循环错误: {e_loop}")
            # traceback.print_exc(limit=1) # 调试时可以打开
            stop_event.wait(3) # 出错时等待时间可以略长
            continue
        stop_event.wait(1) # 每秒从缓存获取价格并更新GUI时间
    print("时间与GUI价格更新线程已停止。")


def trigger_prediction_on_new_kline(symbol, interval, kline_data_dict):
    global binance_client, APP_TIMEZONE, training_in_progress_flag

    if stop_event.is_set(): return
    if training_in_progress_flag.is_set():
        print(f"MainCB: 训练进行中，跳过为 {symbol}@{interval} 的新K线预测。")
        if _main_root and hasattr(gui, 'update_status'):
            gui.update_gui_safe(gui.update_status, f"训练中，跳过 {symbol}@{interval} 预测", "warning")
        return

    print(f"MainCB: 检测到 {symbol}@{interval} 新K线 (收盘时间: {kline_data_dict.get('close_time', 'N/A')})")

    # 优化后的逻辑：检查传入的symbol和interval是否与config.BASE_MODELS_FOR_META中任一基础模型匹配
    should_trigger_meta = False
    matching_base_model = None
    
    if hasattr(config, 'BASE_MODELS_FOR_META') and config.BASE_MODELS_FOR_META:
        for base_model_name in config.BASE_MODELS_FOR_META:
            try:
                base_cfg = safe_get_target_config(base_model_name)
                if symbol.upper() == base_cfg.get('symbol', '').upper() and \
                   interval.lower() == base_cfg.get('interval', '').lower():
                    should_trigger_meta = True
                    matching_base_model = base_model_name
                    break
            except ValueError:
                print(f"!!! MainCB: 获取基础模型 '{base_model_name}' 配置失败，跳过匹配检查。")
                continue
            
    enable_meta_prediction_live = getattr(config, 'ENABLE_META_MODEL_PREDICTION', True)

    if should_trigger_meta and enable_meta_prediction_live:
        print(f"MainCB: 匹配元模型触发条件 ({symbol}@{interval}，匹配基础模型: {matching_base_model})。准备启动元模型预测线程。")
        
        if not prediction.global_prediction_state_manager.is_meta_model_loaded_successfully():
            print("!!! MainCB: 元模型尚未成功加载。将尝试加载。")
            if not prediction.load_meta_model_if_needed():
                print("!!! MainCB: 再次尝试加载元模型失败。元预测跳过。")
                if hasattr(gui, 'update_status') and gui._gui_root and gui._gui_root.winfo_exists():
                     gui.update_gui_safe(gui.update_status, "元模型加载失败，跳过K线预测", "error")
                return # 中止，不运行基础模型

        # --- 实际启动元模型预测线程 ---
        def meta_prediction_task_wrapper():
            prediction.run_meta_prediction_for_current_trigger(
                binance_client,
                APP_TIMEZONE,
                simulator_actual_url=getattr(config, 'SIMULATOR_API_URL', None)
            )
        
        thread = threading.Thread(target=meta_prediction_task_wrapper, daemon=True)
        thread.start()
        print(f"MainCB: 元模型预测线程已为 {symbol}@{interval} 启动。")
        return # <--- 重要：如果元模型被触发，则不再为这个事件运行独立的基础模型

    # --- 如果元模型没有被触发（或被禁用），则按原计划执行各个基础模型的预测 ---
    print(f"MainCB: 元模型未触发或禁用。检查独立基础模型触发条件 for {symbol}@{interval}...")
    triggered_model_types_for_this_event = set()
    any_base_model_triggered = False
    for t_cfg_base in config.PREDICTION_TARGETS:
        if not (isinstance(t_cfg_base, dict) and t_cfg_base.get('name')):
            continue
        # 跳过元模型的GUI显示条目
        if t_cfg_base.get('target_variable_type') == "META_MODEL_DISPLAY":
            continue
        
        try:
            full_cfg = safe_get_target_config(t_cfg_base['name'])
            cfg_symbol = str(full_cfg.get('symbol', '')).upper()
            cfg_interval = str(full_cfg.get('interval', '')).lower()
            target_name_from_cfg = full_cfg.get('name', 'UnnamedTarget')

            # 检查当前K线事件是否匹配这个基础模型的配置
            if cfg_symbol == symbol.upper() and cfg_interval == interval.lower():
                # 并且这个基础模型的 prediction_trigger_type 暗示它应该响应K线关闭
                # (您可以增加一个更明确的检查，例如要求 type 是 'interval' 或 'kline_close')
                
                model_signature = target_name_from_cfg 
                if model_signature in triggered_model_types_for_this_event:
                    print(f"警告 MainCB: 基础模型 '{model_signature}' 在此K线事件中已触发过，本次跳过。")
                    continue
                
                triggered_model_types_for_this_event.add(model_signature)
                any_base_model_triggered = True
                print(f"MainCB: 基础目标 '{target_name_from_cfg}' 匹配K线，启动独立完整预测。")
                
                def base_prediction_task_wrapper(passed_target_config_base, passed_binance_client_base, passed_app_timezone_base):
                    prediction.run_prediction_cycle_for_target(
                        passed_target_config_base,
                        passed_binance_client_base,
                        passed_app_timezone_base,
                        return_core_prediction_only=False, # 运行完整流程
                        simulator_actual_url=getattr(config, 'SIMULATOR_API_URL', None)
                    )
                thread_base = threading.Thread(
                    target=base_prediction_task_wrapper,
                    args=(full_cfg, binance_client, APP_TIMEZONE),
                    daemon=True
                )
                thread_base.start()
        except ValueError as e_cfg_loop_base:
            print(f"MainCB BaseLoop: 获取 '{t_cfg_base.get('name')}' 配置时出错: {e_cfg_loop_base}")
        except Exception as e_loop_inner_base:
            print(f"MainCB BaseLoop: 处理基础目标 '{t_cfg_base.get('name')}' 时发生未知错误: {e_loop_inner_base}")
            traceback.print_exc(limit=1)

    if not any_base_model_triggered and not (should_trigger_meta and enable_meta_prediction_live):
        print(f"MainCB: K线事件 {symbol}@{interval} 未匹配任何元模型或可独立触发的基础模型。")



# --- calculate_optuna_metric --- (保持不变, 已包含新增指标)
def calculate_optuna_metric(metric_name, y_true, y_pred, y_proba):
    try:
        # 统一处理 y_proba，确保是预测为正类 (通常是类别1) 的概率
        # 并且是一维数组
        y_proba_positive_class = None
        if hasattr(y_proba, 'ndim') and y_proba.ndim > 1 and hasattr(y_proba, 'shape') and y_proba.shape[1] >= 2:
            y_proba_positive_class = y_proba[:, 1] # 假设第1列是正类概率
        elif hasattr(y_proba, 'ndim') and y_proba.ndim == 1:
            y_proba_positive_class = y_proba # 已经是1D概率数组
        # 如果 y_proba_positive_class 仍然是 None，某些指标（如roc_auc, brier_score）会出问题

        # 统一处理 y_true，确保是 NumPy 数组，并且是0/1的二分类标签
        # （对于多分类，某些指标如 accuracy, f1, precision, recall 可以处理，但 brier_score_loss 和 roc_auc 通常期望二分类的0/1标签）
        y_true_np = np.array(y_true).ravel() # 确保是1D NumPy 数组

        # 对于需要 y_pred 的指标，也确保是 NumPy 数组
        y_pred_np = np.array(y_pred).ravel() if y_pred is not None else None


        if metric_name == 'accuracy':
            if y_pred_np is None: raise ValueError("Accuracy score requires y_pred.")
            return accuracy_score(y_true_np, y_pred_np)
        elif metric_name == 'f1':
            if y_pred_np is None: raise ValueError("F1 score requires y_pred.")
            return f1_score(y_true_np, y_pred_np, average='macro', zero_division=0)
        elif metric_name == 'roc_auc':
            if y_proba_positive_class is None: raise ValueError("ROC AUC score requires y_proba for positive class.")
            # ROC AUC 需要 y_true 是 0/1
            if len(np.unique(y_true_np)) < 2: # 如果只有一个类别，AUC未定义或无意义
                print(f"    Optuna Metric Debug: ROC AUC not well-defined for single class in y_true. Returning 0.0.")
                return 0.0
            return roc_auc_score(y_true_np, y_proba_positive_class)
        elif metric_name == 'neg_log_loss':
            if y_proba_positive_class is None: raise ValueError("Log loss requires y_proba for positive class.")
            eps = 1e-15
            y_proba_clipped = np.clip(y_proba_positive_class, eps, 1 - eps)
            return -log_loss(y_true_np, y_proba_clipped) # log_loss 也期望 y_true 是 0/1
        elif metric_name == 'binary_logloss': # 通常用于最小化
            if y_proba_positive_class is None: raise ValueError("Log loss requires y_proba for positive class.")
            eps = 1e-15
            y_proba_clipped = np.clip(y_proba_positive_class, eps, 1 - eps)
            return log_loss(y_true_np, y_proba_clipped)
        elif metric_name == 'macro_avg_precision':
            if y_pred_np is None: raise ValueError("Macro Average Precision requires y_pred.")
            return precision_score(y_true_np, y_pred_np, average='macro', zero_division=0)
        elif metric_name == 'average_precision': # 这是 AUC-PR
            if y_proba_positive_class is None: raise ValueError("Average Precision score requires y_proba for positive class.")
            # Average Precision Score 也期望 y_true 是 0/1
            if len(np.unique(y_true_np)) < 2:
                 print(f"    Optuna Metric Debug: Average Precision (AUC-PR) not well-defined for single class in y_true. Returning 0.0.")
                 return 0.0
            return average_precision_score(y_true_np, y_proba_positive_class, pos_label=1) # 假设1是正类
        elif metric_name == 'custom_precision_recall_mix':
            if y_pred_np is None: raise ValueError("Custom Precision-Recall mix requires y_pred.")
            precision = precision_score(y_true_np, y_pred_np, average='macro', zero_division=0)
            recall = recall_score(y_true_np, y_pred_np, average='macro', zero_division=0)
            # print(f"    Optuna Metric Debug: Precision (macro) = {precision:.4f}, Recall (macro) = {recall:.4f}")
            return (precision * 0.7) + (recall * 0.3)
        elif metric_name == 'precision_macro_focused': # 与 macro_avg_precision 相同
            if y_pred_np is None: raise ValueError("Precision Macro requires y_pred.")
            precision_val = precision_score(y_true_np, y_pred_np, average='macro', zero_division=0)
            # print(f"    Optuna Metric (precision_macro_focused): {precision_val:.4f}")
            return precision_val
        elif metric_name == 'precision_positive_focused': # 精确关注正类（假设为1）的精确率
            if y_pred_np is None: raise ValueError("Precision Positive Focused requires y_pred.")
            # 确保 y_true_np 中确实有正类1，否则 pos_label=1 会报错
            if 1 not in np.unique(y_true_np) and len(np.unique(y_pred_np)) > 0 and 1 in np.unique(y_pred_np):
                 # 如果真实标签中没有1，但预测中有1，则精确率为0
                 return 0.0
            precision_val = precision_score(y_true_np, y_pred_np, pos_label=1, average='binary', zero_division=0)
            # print(f"    Optuna Metric (precision_positive_focused for label 1): {precision_val:.4f}")
            return precision_val
        
        # +++ 新增 Brier Score 计算逻辑 +++
        elif metric_name == 'brier_score_loss': # Optuna 方向应为 "minimize"
            if y_proba_positive_class is None: raise ValueError("Brier Score requires y_proba for positive class.")
            # brier_score_loss 需要 y_true (0或1的真实标签) 和 y_prob (模型预测为正类1的概率)
            # 确保 y_true_np 是纯0/1标签
            if not all(label in [0, 1] for label in np.unique(y_true_np)):
                raise ValueError(f"Brier Score requires y_true to contain only 0s and 1s. Got: {np.unique(y_true_np)}")
            return brier_score_loss(y_true_np, y_proba_positive_class)

        elif metric_name == 'neg_brier_score': # Optuna 方向应为 "maximize"
            if y_proba_positive_class is None: raise ValueError("Brier Score requires y_proba for positive class.")
            if not all(label in [0, 1] for label in np.unique(y_true_np)):
                raise ValueError(f"Brier Score requires y_true to contain only 0s and 1s. Got: {np.unique(y_true_np)}")
            return -brier_score_loss(y_true_np, y_proba_positive_class)
        # +++ 结束新增 +++

        # +++ 新增元模型专用指标 +++
        elif metric_name == 'custom_f1_class01_avg': # 高优先级优化：只关注Class 0和Class 1的F1分数
            if y_pred_np is None: raise ValueError("Custom F1 Class01 Average requires y_pred.")
            # 计算Class 0和Class 1的F1分数
            f1_scores = f1_score(y_true_np, y_pred_np, labels=[0, 1], average=None, zero_division=0)
            if len(f1_scores) >= 2:
                return (f1_scores[0] + f1_scores[1]) / 2  # Class 0和1的F1平均值
            else:
                return 0.0

        elif metric_name == 'custom_precision_class01_avg': # Class 0和1的精确率平均值
            if y_pred_np is None: raise ValueError("Custom Precision Class01 Average requires y_pred.")
            # 计算Class 0和Class 1的精确率
            precision_scores = precision_score(y_true_np, y_pred_np, labels=[0, 1], average=None, zero_division=0)
            if len(precision_scores) >= 2:
                return (precision_scores[0] + precision_scores[1]) / 2  # Class 0和1的精确率平均值
            else:
                return 0.0

        elif metric_name == 'custom_recall_class01_avg': # Class 0和1的召回率平均值
            if y_pred_np is None: raise ValueError("Custom Recall Class01 Average requires y_pred.")
            # 计算Class 0和Class 1的召回率
            recall_scores = recall_score(y_true_np, y_pred_np, labels=[0, 1], average=None, zero_division=0)
            if len(recall_scores) >= 2:
                return (recall_scores[0] + recall_scores[1]) / 2  # Class 0和1的召回率平均值
            else:
                return 0.0
        # +++ 结束元模型专用指标 +++

        # 🎯 新增：基础模型盈利能力指标 (二分类)
        elif metric_name == 'binary_simulated_profit':
            # 基于模拟交易盈利能力优化 (二分类版本)
            if y_proba_positive_class is None:
                raise ValueError("Binary Simulated Profit requires y_proba for positive class.")

            # 导入简化的盈利计算函数
            from src.core.data_utils import _calculate_binary_simulated_profit

            # 使用默认阈值0.5计算盈利指标
            profit_result = _calculate_binary_simulated_profit(
                y_true_np, y_proba_positive_class, threshold=0.5, payout_ratio=0.85
            )
            return profit_result.get('expected_profit_per_trade', 0.0)

        elif metric_name == 'binary_risk_adjusted_return':
            # 基于风险调整收益优化 (二分类版本)
            if y_proba_positive_class is None:
                raise ValueError("Binary Risk Adjusted Return requires y_proba for positive class.")

            from src.core.data_utils import _calculate_binary_simulated_profit

            profit_result = _calculate_binary_simulated_profit(
                y_true_np, y_proba_positive_class, threshold=0.5, payout_ratio=0.85
            )
            return profit_result.get('risk_adjusted_return', 0.0)

        elif metric_name == 'binary_profit_precision_composite':
            # 复合指标：盈利能力 + 精确率 (二分类版本)
            if y_proba_positive_class is None or y_pred_np is None:
                raise ValueError("Binary Profit Precision Composite requires both y_proba and y_pred.")

            from src.core.data_utils import _calculate_binary_simulated_profit

            # 计算盈利指标
            profit_result = _calculate_binary_simulated_profit(
                y_true_np, y_proba_positive_class, threshold=0.5, payout_ratio=0.85
            )
            expected_profit = profit_result.get('expected_profit_per_trade', 0.0)

            # 计算精确率
            precision = precision_score(y_true_np, y_pred_np, zero_division=0)

            # 复合评分：70% 盈利能力 + 30% 精确率
            composite_score = 0.7 * expected_profit + 0.3 * precision
            return composite_score

        elif metric_name == 'binary_win_rate_weighted_profit':
            # 胜率加权的期望收益 (二分类版本)
            if y_proba_positive_class is None:
                raise ValueError("Binary Win Rate Weighted Profit requires y_proba for positive class.")

            from src.core.data_utils import _calculate_binary_simulated_profit

            profit_result = _calculate_binary_simulated_profit(
                y_true_np, y_proba_positive_class, threshold=0.5, payout_ratio=0.85
            )
            expected_profit = profit_result.get('expected_profit_per_trade', 0.0)
            win_rate = profit_result.get('win_rate', 0.0)

            # 胜率加权的期望收益
            return expected_profit * win_rate

        else:
            print(f"! 未知的 Optuna 指标 '{metric_name}'，将使用 accuracy_score。")
            if y_pred_np is None: raise ValueError("Fallback Accuracy score requires y_pred.")
            return accuracy_score(y_true_np, y_pred_np)

    except ValueError as ve: # 更具体的错误捕获
        unique_labels_true = np.unique(y_true_np) if 'y_true_np' in locals() else np.unique(y_true)
        unique_labels_pred = np.unique(y_pred_np) if 'y_pred_np' in locals() and y_pred_np is not None else "N/A"
        
        # 根据指标类型确定返回值
        minimize_metrics = ['binary_logloss', 'brier_score_loss']
        maximize_metrics = ['accuracy', 'f1', 'roc_auc', 'average_precision', 'macro_avg_precision',
                           'custom_precision_recall_mix', 'precision_macro_focused',
                           'precision_positive_focused', 'neg_log_loss', 'neg_brier_score',
                           'custom_f1_class01_avg', 'custom_precision_class01_avg', 'custom_recall_class01_avg']
        
        if metric_name in minimize_metrics:
            return_value = float('inf')  # 对于最小化指标，返回正无穷
            return_msg = "返回 float('inf') (最小化指标)"
        else:  # 默认假设为最大化指标
            return_value = float('-inf')  # 对于最大化指标，返回负无穷
            return_msg = "返回 float('-inf') (最大化指标)"
        
        print(f"!! Optuna 指标计算 ValueError ('{metric_name}'): {ve}. "
              f"y_true unique: {unique_labels_true}, y_pred unique: {unique_labels_pred}. "
              f"y_proba_positive_class is None: {y_proba_positive_class is None if 'y_proba_positive_class' in locals() else 'N/A'}. "
              f"{return_msg}。")
        
        return return_value
    except Exception as e:
        # 根据指标类型确定返回值
        minimize_metrics = ['binary_logloss', 'brier_score_loss']
        maximize_metrics = ['accuracy', 'f1', 'roc_auc', 'average_precision', 'macro_avg_precision',
                           'custom_precision_recall_mix', 'precision_macro_focused',
                           'precision_positive_focused', 'neg_log_loss', 'neg_brier_score',
                           'custom_f1_class01_avg', 'custom_precision_class01_avg', 'custom_recall_class01_avg']
        
        if metric_name in minimize_metrics:
            return_value = float('inf')  # 对于最小化指标，返回正无穷
            return_msg = "返回 float('inf') (最小化指标)"
        else:  # 默认假设为最大化指标
            return_value = float('-inf')  # 对于最大化指标，返回负无穷
            return_msg = "返回 float('-inf') (最大化指标)"
        
        print(f"!! Optuna 指标计算时发生未知错误 ('{metric_name}'): {type(e).__name__} - {e}。{return_msg}")
        traceback.print_exc(limit=1) # 打印堆栈信息
        return return_value


def _generate_oof_predictions_from_data(base_model_target_name, base_model_config, df_full_hist_data_raw,
                                        y_meta_series_aligned, selected_feature_names, model_dir, model_meta_oof, app_timezone, binance_client=None): # 修改参数名
    """
    🎯 Probability Calibration Fix: 根据加载的工件和原始历史数据生成OOF预测

    重要修复：现在优先使用校准后的模型进行OOF预测生成，确保概率质量一致性
    """
    print(f"  [OOF Info - {base_model_target_name}] 使用 {len(selected_feature_names)} 个特征进行OOF生成。")

    # 重要：始终使用当前基础模型的配置重新生成特征，确保特征参数匹配
    try:
        print(f"  [OOF Info - {base_model_target_name}] 使用当前模型配置重新生成特征...")

        # 从原始OHLCV数据开始生成特征（取前9列确保是原始数据）
        df_raw_ohlcv = df_full_hist_data_raw.iloc[:, :9].copy()

        df_features_for_current_model = data_utils.add_classification_features(df_raw_ohlcv, base_model_config)
        if df_features_for_current_model is None or df_features_for_current_model.empty:
            print(f"!!! 错误 ({base_model_target_name} OOF): 使用当前模型配置生成特征失败。"); return None

        # 如果启用了MTFA，也需要添加MTFA特征
        if base_model_config.get('enable_mtfa', False):
            print(f"  [OOF Info - {base_model_target_name}] 添加MTFA特征...")

            # 🎯 修复：检查binance_client是否可用
            if binance_client is None:
                print(f"  警告 ({base_model_target_name} OOF): binance_client未初始化，跳过MTFA特征添加，将使用基础特征。")
                print(f"  提示: 如果需要MTFA特征，请确保Binance客户端正确初始化。")
            else:
                df_features_with_mtfa = data_utils.add_mtfa_features_to_df(
                    df_features_for_current_model.copy(), base_model_config, binance_client  # 使用传递的binance_client
                )
                if df_features_with_mtfa is not None and not df_features_with_mtfa.empty:
                    df_features_for_current_model = df_features_with_mtfa
                    print(f"  [OOF Info - {base_model_target_name}] MTFA特征添加成功")
                else:
                    print(f"  警告 ({base_model_target_name} OOF): MTFA特征添加失败，将使用基础特征。")

        print(f"  [OOF Info - {base_model_target_name}] 特征生成完成，特征数量: {df_features_for_current_model.shape[1]}")
    except Exception as e_feat_gen:
        print(f"!!! 错误 ({base_model_target_name} OOF): 特征生成过程出错: {e_feat_gen}"); return None

    df_with_base_target, base_target_col_name = data_utils.create_target_variable(
        df_features_for_current_model.copy(), base_model_config
    )
    if not base_target_col_name or base_target_col_name not in df_with_base_target.columns:
        print(f"!!! 未能为 '{base_model_target_name}' 创建OOF基础目标变量。"); return None
    y_base_binary_series = df_with_base_target[base_target_col_name]

    common_index = df_features_for_current_model.index.intersection(y_base_binary_series.index)
    if y_meta_series_aligned is not None and not y_meta_series_aligned.empty:
        common_index = common_index.intersection(y_meta_series_aligned.index)
    if common_index.empty:
        print(f"!!! '{base_model_target_name}' (OOF): 无法对齐数据索引。"); return None

    missing_cols_in_hist = [col for col in selected_feature_names if col not in df_features_for_current_model.columns]
    if missing_cols_in_hist:
        print(f"!!! 错误 ({base_model_target_name} OOF): 特征列表中的特征在历史数据中不存在: {missing_cols_in_hist}"); return None

    X_features_aligned = df_features_for_current_model.loc[common_index, selected_feature_names].copy()
    y_base_aligned = y_base_binary_series.loc[common_index].copy()
    
    if X_features_aligned.empty or y_base_aligned.empty:
        print(f"!!! '{base_model_target_name}' (OOF): 对齐后的特征或目标为空。"); return None

    if X_features_aligned.isnull().values.any() or y_base_aligned.isnull().values.any():
        print(f"  警告 ({base_model_target_name} OOF): 对齐后的X或y包含NaN值。X NaN sum: {X_features_aligned.isnull().sum().sum()}, y NaN sum: {y_base_aligned.isnull().sum()}. 将尝试填充。")
        for col in X_features_aligned.columns:
            default_val = 0
            if any(price_term in col.lower() for price_term in ['price', 'close', 'open', 'high', 'low']):
                non_nan_vals = X_features_aligned[col].dropna()
                if len(non_nan_vals) > 0:
                    default_val = non_nan_vals.mean()
            X_features_aligned[col] = data_utils.safe_fill_nans(X_features_aligned[col], default_val)
        
        y_base_aligned = y_base_aligned.ffill()
        if y_base_aligned.isnull().any():
            if y_base_aligned.dtype == int or y_base_aligned.dtype == 'int64' or y_base_aligned.dtype == 'int32':
                mode_value = y_base_aligned.mode().iloc[0] if not y_base_aligned.dropna().empty else 0
                y_base_aligned = y_base_aligned.fillna(mode_value)
            else:
                mean_value = y_base_aligned.mean() if not y_base_aligned.dropna().empty else 0
                y_base_aligned = y_base_aligned.fillna(mean_value)
        
        if y_base_aligned.isnull().any():
            print(f"  !!! 错误 ({base_model_target_name} OOF): 填充后y_base_aligned仍有NaN，无法继续。"); return None

    oof_preds_proba_series = pd.Series(index=y_base_aligned.index, dtype=float, name=f"oof_proba_{base_model_target_name}")

    # 🎯 Probability Calibration Fix: 尝试加载训练时保存的校准模型
    use_trained_models = True
    trained_models = []

    if model_meta_oof and 'fold_model_artifacts' in model_meta_oof:
        print(f"  [OOF Info - {base_model_target_name}] 尝试加载训练时的校准模型...")
        fold_artifacts = model_meta_oof['fold_model_artifacts']

        for fold_idx, fold_artifact in enumerate(fold_artifacts):
            # 优先使用校准模型
            calibrated_model_fn = fold_artifact.get("calibrated_model_filename")
            base_model_fn = fold_artifact.get("model_filename")

            model_path = None
            if calibrated_model_fn and os.path.exists(os.path.join(model_dir, calibrated_model_fn)):
                model_path = os.path.join(model_dir, calibrated_model_fn)
                print(f"    Fold {fold_idx}: 使用校准模型 {calibrated_model_fn}")
            elif base_model_fn and os.path.exists(os.path.join(model_dir, base_model_fn)):
                model_path = os.path.join(model_dir, base_model_fn)
                print(f"    Fold {fold_idx}: 使用原始模型 {base_model_fn}")

            if model_path:
                try:
                    trained_model = joblib.load(model_path)
                    trained_models.append(trained_model)
                    print(f"    Fold {fold_idx}: 模型加载成功")
                except Exception as e:
                    print(f"    Fold {fold_idx}: 模型加载失败: {e}")
                    use_trained_models = False
                    break
            else:
                print(f"    Fold {fold_idx}: 未找到模型文件")
                use_trained_models = False
                break
    else:
        print(f"  [OOF Info - {base_model_target_name}] 未找到fold模型元数据，将重新训练")
        use_trained_models = False

    if not use_trained_models or not trained_models:
        print(f"  [OOF Info - {base_model_target_name}] 回退到重新训练模式")
        # 原有的重新训练逻辑
        lgbm_oof_base_params = {
            'objective': base_model_config.get('objective', 'binary'),
            'metric': base_model_config.get('metric', 'binary_logloss'),
            'boosting_type': 'gbdt',
            'device_type': base_model_config.get('device_type', 'cpu'),
            'n_jobs': -1, 'random_state': base_model_config.get('random_state', 42) + 1042,
            'verbose': -1, 'verbosity': -1,
            'n_estimators': getattr(config, 'META_MODEL_OOF_LGBM_N_ESTIMATORS', 1000),
            'class_weight': 'balanced',
            'learning_rate': 0.02, 'num_leaves': 10, 'max_depth': 3,
            'min_child_samples': 40, 'reg_alpha': 1.0, 'reg_lambda': 1.0,
            'subsample': 0.7, 'colsample_bytree': 0.7
        }
        global VALID_LGBM_PARAM_KEYS
        if not VALID_LGBM_PARAM_KEYS:
            VALID_LGBM_PARAM_KEYS = ['boosting_type', 'num_leaves', 'max_depth', 'learning_rate', 'n_estimators', 'subsample_for_bin', 'objective', 'class_weight', 'min_split_gain', 'min_child_weight', 'min_child_samples', 'subsample', 'subsample_freq', 'colsample_bytree', 'reg_alpha', 'reg_lambda', 'random_state', 'n_jobs', 'importance_type', 'metric', 'device_type', 'verbose', 'verbosity', 'seed', 'callbacks', 'eval_set', 'eval_names', 'eval_metric', 'feature_name', 'categorical_feature', 'early_stopping_rounds', 'first_metric_only', 'num_class']
        lgbm_oof_final_params = {k: v for k, v in lgbm_oof_base_params.items() if k in VALID_LGBM_PARAM_KEYS}

    # 🎯 Probability Calibration Fix: 根据是否有训练好的模型选择不同的预测策略
    if use_trained_models and trained_models:
        print(f"  [OOF Info - {base_model_target_name}] 使用训练好的校准模型进行预测...")

        # 使用训练好的模型进行集成预测
        # 需要对整个数据集进行缩放
        scaler_type_str = base_model_config.get('scaler_type', 'MinMaxScaler')
        if scaler_type_str == 'StandardScaler':
            scaler = StandardScaler()
        elif scaler_type_str == 'MinMaxScaler':
            scaler = MinMaxScaler()
        else:
            scaler = MinMaxScaler()

        # 缩放特征
        X_scaled = scaler.fit_transform(X_features_aligned)
        X_scaled_df = pd.DataFrame(X_scaled, columns=selected_feature_names, index=X_features_aligned.index)

        # 使用所有训练好的模型进行集成预测
        all_model_probas = []
        for model_idx, trained_model in enumerate(trained_models):
            try:
                model_probas = trained_model.predict_proba(X_scaled_df)
                if model_probas.ndim == 2 and model_probas.shape[1] == 2:
                    all_model_probas.append(model_probas[:, 1])  # 取正类概率
                    print(f"    模型 {model_idx}: 预测成功")
                else:
                    print(f"    模型 {model_idx}: 预测概率形状异常: {model_probas.shape}")
            except Exception as e:
                print(f"    模型 {model_idx}: 预测失败: {e}")

        if all_model_probas:
            # 计算集成平均概率
            ensemble_probas = np.mean(all_model_probas, axis=0)
            oof_preds_proba_series.loc[X_features_aligned.index] = ensemble_probas
            print(f"  [OOF Info - {base_model_target_name}] 使用 {len(all_model_probas)} 个校准模型完成集成预测")
        else:
            print(f"  [OOF Info - {base_model_target_name}] 所有模型预测失败，使用默认概率")
            oof_preds_proba_series.loc[X_features_aligned.index] = 0.5
    else:
        print(f"  [OOF Info - {base_model_target_name}] 使用重新训练模式...")

        tscv = TimeSeriesSplit(n_splits=getattr(config, 'META_MODEL_OOF_CV_FOLDS', 5))
        fold_num = 0
        for train_idx, val_idx in tscv.split(X_features_aligned, y_base_aligned):
            fold_num += 1
            print(f"  OOF Fold {fold_num}/{getattr(config, 'META_MODEL_OOF_CV_FOLDS', 5)} for '{base_model_target_name}'...")

            X_train_fold, X_val_fold = X_features_aligned.iloc[train_idx], X_features_aligned.iloc[val_idx]
            y_train_fold, y_val_fold = y_base_aligned.iloc[train_idx], y_base_aligned.iloc[val_idx]

            if X_train_fold.empty or y_train_fold.empty or X_val_fold.empty or y_val_fold.empty:
                print(f"    警告: Fold {fold_num} 数据集为空 (OOF)，跳过。"); oof_preds_proba_series.iloc[val_idx] = np.nan; continue
        
        # <<< NEW SCALER HANDLING PER FOLD >>>
        fold_scaler = None
        scaler_type_str = base_model_config.get('scaler_type', 'MinMaxScaler') 
        if scaler_type_str == 'StandardScaler':
            fold_scaler = StandardScaler()
        elif scaler_type_str == 'MinMaxScaler':
            fold_scaler = MinMaxScaler()
        else: 
            print(f"    警告: Fold {fold_num} 未知的 scaler_type '{scaler_type_str}', 将使用 MinMaxScaler (OOF).")
            fold_scaler = MinMaxScaler()
        
            X_train_fold_scaled = None # Initialize
            try:
                # Fit scaler on current fold's training data
                # Ensure X_train_fold does not contain NaNs/Infs before fitting scaler
                if X_train_fold.isnull().values.any() or np.isinf(X_train_fold.values).any():
                    print(f"    警告: Fold {fold_num} (OOF) X_train_fold 包含 NaN/Inf，在拟合scaler前尝试填充。")
                    X_train_fold_clean_for_scaler = X_train_fold.copy()
                    for col_tf_clean in X_train_fold_clean_for_scaler.columns:
                        X_train_fold_clean_for_scaler[col_tf_clean] = data_utils.safe_fill_nans(X_train_fold_clean_for_scaler[col_tf_clean], default_value=0)
                    X_train_fold_clean_for_scaler = X_train_fold_clean_for_scaler.replace([np.inf, -np.inf], 0)
                    fold_scaler.fit(X_train_fold_clean_for_scaler)
                    X_train_fold_scaled = fold_scaler.transform(X_train_fold_clean_for_scaler)
                else:
                    fold_scaler.fit(X_train_fold)
                    X_train_fold_scaled = fold_scaler.transform(X_train_fold)

            except Exception as e_scale_f:
                print(f"    !!! 错误: Fold {fold_num} Scaler fit/transform失败: {e_scale_f}");
                oof_preds_proba_series.iloc[val_idx] = np.nan;
                continue
        # <<< END NEW SCALER HANDLING >>>

        eval_set_for_fit, callbacks_for_fit = None, None
        min_es_samples = getattr(config, 'META_MODEL_OOF_MIN_SAMPLES_FOR_EARLY_STOP_VALID', 50)
        es_ratio = getattr(config, 'META_MODEL_OOF_VALID_FOR_EARLY_STOP_RATIO', 0.15)
        X_fit_np, y_fit_series = X_train_fold_scaled, y_train_fold
        if len(X_train_fold_scaled) >= min_es_samples * (1 / (es_ratio or 0.01)):
            n_es_val = max(min_es_samples, int(len(X_train_fold_scaled) * es_ratio))
            if len(X_train_fold_scaled) - n_es_val >= min_es_samples:
                X_fit_np, y_fit_series = X_train_fold_scaled[:-n_es_val], y_train_fold.iloc[:-n_es_val]
                X_es_val, y_es_val = X_train_fold_scaled[-n_es_val:], y_train_fold.iloc[-n_es_val:]
                if len(np.unique(y_es_val)) >= 2:
                    eval_set_for_fit = [(X_es_val, y_es_val)]
                    callbacks_for_fit = [early_stopping(stopping_rounds=getattr(config, 'META_MODEL_OOF_LGBM_EARLY_STOPPING_ROUNDS', 75), verbose=-1, first_metric_only=True)]
                else: print(f"    Fold {fold_num} (OOF): 早停验证集标签单一，不使用早停。")
            else: print(f"    Fold {fold_num} (OOF): 划分早停集后训练集过小，不使用早停。")
        else: print(f"    Fold {fold_num} (OOF): 原始训练集过小，不使用早停。")
        
        current_fold_params = lgbm_oof_final_params.copy()
        current_fold_params['random_state'] = lgbm_oof_final_params.get('random_state', 42) + fold_num 

        # 应用SMOTE过采样 (仅在训练数据上)
        smote_enabled_oof = getattr(config, 'SMOTE_GLOBAL_ENABLE', True) and base_model_config.get('smote_enable', True)
        if smote_enabled_oof:
            unique_labels_oof, counts_oof = np.unique(y_fit_series, return_counts=True)
            minority_class_count_oof = counts_oof.min()
            smote_min_threshold_oof = base_model_config.get('smote_min_samples_threshold', getattr(config, 'SMOTE_MIN_SAMPLES_THRESHOLD', 5))

            if minority_class_count_oof < smote_min_threshold_oof:
                print(f"    Fold {fold_num} (OOF): 少数类样本 ({minority_class_count_oof}) 过少，跳过SMOTE。")
                X_fit_resampled_np = X_fit_np
                y_fit_resampled_series = y_fit_series
            else:
                try:
                    print(f"    Fold {fold_num} (OOF): 应用SMOTE前类别分布: {dict(zip(unique_labels_oof, counts_oof))}")
                    # k_neighbors 的值不能超过少数类样本数 - 1
                    smote_k_neighbors_config_oof = base_model_config.get('smote_k_neighbors', getattr(config, 'SMOTE_DEFAULT_K_NEIGHBORS', 4))
                    smote_k_neighbors_oof = min(smote_k_neighbors_config_oof, minority_class_count_oof - 1) if minority_class_count_oof > 1 else 1
                    smote_random_state_oof = base_model_config.get('smote_random_state', getattr(config, 'SMOTE_RANDOM_STATE', 42))
                    sm_oof = SMOTE(random_state=smote_random_state_oof, k_neighbors=smote_k_neighbors_oof)
                    X_fit_resampled_np, y_fit_resampled_np = sm_oof.fit_resample(X_fit_np, y_fit_series)
                    y_fit_resampled_series = pd.Series(y_fit_resampled_np, name=y_fit_series.name)
                    unique_labels_after_oof, counts_after_oof = np.unique(y_fit_resampled_np, return_counts=True)
                    print(f"    Fold {fold_num} (OOF): 应用SMOTE后类别分布: {dict(zip(unique_labels_after_oof, counts_after_oof))}")
                except ValueError as e_smote_oof:
                    print(f"    !!! Fold {fold_num} (OOF): SMOTE执行失败: {e_smote_oof}。将使用原始训练数据。")
                    X_fit_resampled_np = X_fit_np
                    y_fit_resampled_series = y_fit_series
        else:
            print(f"    Fold {fold_num} (OOF): SMOTE已禁用，使用原始训练数据。")
            X_fit_resampled_np = X_fit_np
            y_fit_resampled_series = y_fit_series

            model_fold = LGBMClassifier(**current_fold_params)
            try:
                model_fold.fit(X_fit_resampled_np, y_fit_resampled_series, eval_set=eval_set_for_fit,
                               eval_metric=current_fold_params.get('metric'), callbacks=callbacks_for_fit,
                               feature_name=selected_feature_names)
            except Exception as e_fit_f:
                print(f"    !!! Fold {fold_num} 模型训练失败 (OOF): {e_fit_f}");
                oof_preds_proba_series.iloc[val_idx] = np.nan;
                continue

        try:
            oof_fold_probas = np.full(len(val_idx), np.nan)
            if not X_val_fold.empty and X_val_fold.shape[0] > 0:
                X_val_fold_clean = X_val_fold.copy()
                if X_val_fold_clean.isnull().values.any() or np.isinf(X_val_fold_clean.values).any():
                    print(f"    警告: Fold {fold_num} (OOF) X_val_fold 预测前含NaN/Inf，尝试填充。")
                    for col in X_val_fold_clean.columns:
                        X_val_fold_clean[col] = data_utils.safe_fill_nans(X_val_fold_clean[col], default_value=0)
                    X_val_fold_clean = X_val_fold_clean.replace([np.inf, -np.inf], 0)
                
                X_val_fold_scaled = fold_scaler.transform(X_val_fold_clean) # Use fold_scaler
                X_val_df = pd.DataFrame(X_val_fold_scaled, columns=selected_feature_names, index=X_val_fold_clean.index)
                if not X_val_df.empty:
                    raw_probas = model_fold.predict_proba(X_val_df)
                    if raw_probas.ndim == 2 and raw_probas.shape[1] == 2: oof_fold_probas = raw_probas[:, 1]
                    else: print(f"    !!! 错误: Fold {fold_num} - predict_proba返回形状异常: {raw_probas.shape}")
                else: print(f"    !!! Fold {fold_num} (OOF) X_val_df 处理后为空。")
            else: print(f"    !!! Fold {fold_num} (OOF) 验证集为空或缩放数据为空。")
            
            if len(oof_fold_probas) == len(val_idx): oof_preds_proba_series.iloc[val_idx] = oof_fold_probas
            else: print(f"    !!! 错误: Fold {fold_num} - 预测概率长度与验证索引长度不匹配。"); oof_preds_proba_series.iloc[val_idx] = np.nan
        except Exception as e_pred_f: print(f"    !!! Fold {fold_num} OOF预测过程失败: {e_pred_f}"); oof_preds_proba_series.iloc[val_idx] = np.nan

    nans_before_final_fill = oof_preds_proba_series.isnull().sum()
    if nans_before_final_fill > 0:
        print(f"  警告: OOF预测 for '{base_model_target_name}' 含 {nans_before_final_fill} NaN。填充中...")
        oof_preds_proba_series = data_utils.safe_fill_nans(oof_preds_proba_series, default_value=0.5) # 使用0.5填充概率的NaN
        print(f"    填充后NaN数量: {oof_preds_proba_series.isnull().sum()}")

    # 保存OOF预测结果
    if model_dir and os.path.exists(model_dir):
        target_name_for_file = base_model_target_name.replace('/', '_').replace(':', '_')
        pred_periods_cfg = base_model_config.get('prediction_periods', [1])
        pred_periods_for_file = pred_periods_cfg[0] if isinstance(pred_periods_cfg, list) and pred_periods_cfg else 1
        interval_val_str = base_model_config.get('interval', 'Xm').rstrip('mhd')
        try:
            minutes_display_val = int(interval_val_str) * pred_periods_for_file
        except ValueError:
            minutes_display_val = base_model_config.get('prediction_minutes_display', '?')
        minutes_display_for_file = str(minutes_display_val)
        
        oof_filename = f"oof_predictions_{target_name_for_file}_{minutes_display_for_file}m.csv"
        oof_filepath = os.path.join(model_dir, oof_filename)
        try:
            oof_preds_proba_series.to_csv(oof_filepath, header=True)
            print(f"  OOF预测已保存到: {oof_filepath}")
        except Exception as e_save_oof:
            print(f"!!! 保存OOF预测失败 for '{base_model_target_name}': {e_save_oof}")
    else:
        print(f"!!! 警告: 模型目录 '{model_dir}' 不存在，无法保存OOF预测 for '{base_model_target_name}'。")

    return oof_preds_proba_series


def generate_oof_predictions_for_base_model(base_model_target_name,
                                             df_full_hist_data_with_features,
                                             y_meta_series_aligned,
                                             app_timezone,
                                             binance_client=None):
    print(f"\n--->>> 开始为基础模型 '{base_model_target_name}' 生成OOF预测概率 <<<---")
    if df_full_hist_data_with_features is None:
        print(f"  !!! 错误 [OOF Init - {base_model_target_name}] df_full_hist_data_with_features is None! 无法继续。")
        return None

    try:
        base_model_config = safe_get_target_config(base_model_target_name)
    except ValueError as e:
        print(f"!!! 获取基础模型 '{base_model_target_name}' 配置失败: {e}")
        return None

    # scaler loaded by _load_oof_model_artifacts is named _loaded_scaler_ignore as it's not used for per-fold OOF generation.
    # The actual scaler is now created and fitted within each fold in _generate_oof_predictions_from_data.
    _loaded_scaler_ignore, selected_feature_names, model_meta_oof, model_dir = _load_oof_model_artifacts(base_model_target_name, base_model_config)
    if selected_feature_names is None or model_dir is None: # Check critical artifacts
        print(f"!!! 未能为基础模型 '{base_model_target_name}' 加载OOF工件。")
        return None
    
    oof_preds_proba_series = _generate_oof_predictions_from_data(
        base_model_target_name=base_model_target_name,
        base_model_config=base_model_config,
        df_full_hist_data_raw=df_full_hist_data_with_features,  # 修改参数名，实际上这里应该传递原始数据
        y_meta_series_aligned=y_meta_series_aligned,
        selected_feature_names=selected_feature_names,
        model_dir=model_dir,
        model_meta_oof=model_meta_oof,
        app_timezone=app_timezone,
        binance_client=binance_client
    )

    if oof_preds_proba_series is None:
        print(f"!!! 未能为基础模型 '{base_model_target_name}' 生成OOF预测。")
        return None
    
    print(f"<---<<< 完成 '{base_model_target_name}' 的OOF预测概率生成 <<<---")
    return oof_preds_proba_series




def _load_oof_model_artifacts(base_model_target_name, base_model_config):
    """Helper function to load scaler, feature list, and model metadata for OOF prediction."""
    model_dir = base_model_config.get('model_save_dir')
    if not model_dir or not os.path.exists(model_dir):
        print(f"!!! 基础模型 '{base_model_target_name}' 的模型目录 '{model_dir}' 不存在 (OOF)。")
        return None, None, None, None

    target_name_for_file = base_model_target_name.replace('/', '_').replace(':', '_')
    pred_periods_cfg = base_model_config.get('prediction_periods', [1])
    pred_periods_for_file = pred_periods_cfg[0] if isinstance(pred_periods_cfg, list) and pred_periods_cfg else 1
    interval_val_str = base_model_config.get('interval', 'Xm').rstrip('mhd')
    try:
        minutes_display_val = int(interval_val_str) * pred_periods_for_file
    except ValueError:
        minutes_display_val = base_model_config.get('prediction_minutes_display', '?')
    minutes_display_for_file = str(minutes_display_val)

    scaler_filename = None
    feature_list_filename = None
    model_meta_oof = None
    meta_filename_oof = f"model_meta_{target_name_for_file}_{minutes_display_for_file}m.json"
    meta_file_path_oof = os.path.join(model_dir, meta_filename_oof)

    if os.path.exists(meta_file_path_oof):
        try:
            with open(meta_file_path_oof, 'r', encoding='utf-8') as f_meta_oof:
                model_meta_oof = json.load(f_meta_oof)
            if model_meta_oof:
                scaler_filename = model_meta_oof.get("scaler_filename")
                feature_list_filename = model_meta_oof.get("feature_list_filename")
        except Exception as e_meta_load_oof:
            print(f"  警告: 读取元数据文件 '{meta_filename_oof}' 失败 (OOF): {e_meta_load_oof}。")
    else:
        print(f"  警告: 元数据文件 '{meta_filename_oof}' 未找到 (OOF)。将使用默认文件名规则。")

    if not scaler_filename:
        scaler_filename = f"scaler_{target_name_for_file}_{minutes_display_for_file}m.joblib"
    if not feature_list_filename:
        model_suffix_parts_from_meta = ""
        if model_meta_oof and isinstance(model_meta_oof, dict):
             model_suffix_parts_from_meta = model_meta_oof.get("model_suffix_parts_used_for_artifacts", "")
        potential_suffixes_oof = [""]
        if isinstance(model_suffix_parts_from_meta, str) and model_suffix_parts_from_meta:
            potential_suffixes_oof.append(model_suffix_parts_from_meta)
        elif isinstance(model_suffix_parts_from_meta, list) and model_suffix_parts_from_meta:
            valid_suffixes = [s for s in model_suffix_parts_from_meta if isinstance(s, str) and s.strip()]
            if valid_suffixes: potential_suffixes_oof.append("_" + "_".join(sorted(list(set(valid_suffixes)))))
        common_fs_suffixes = ["_rfe", "_impthresh", "_rfe_impthresh", "_optuna", "_rfe_optuna", "_impthresh_optuna", "_rfe_impthresh_optuna"]
        for sfx in common_fs_suffixes:
            if sfx not in potential_suffixes_oof: potential_suffixes_oof.append(sfx)
        found_feat_file_oof = False
        for suffix_part_oof in sorted(list(set(potential_suffixes_oof)), key=len, reverse=True):
            fname_try_oof = f"final_selected_features{suffix_part_oof}_{target_name_for_file}_{minutes_display_for_file}m.json"
            if os.path.exists(os.path.join(model_dir, fname_try_oof)):
                feature_list_filename = fname_try_oof; found_feat_file_oof = True; break
        if not found_feat_file_oof:
             print(f"!!! 错误: 未能找到 '{base_model_target_name}' 的特征列表 (OOF)。尝试后缀: {potential_suffixes_oof}"); return None, None, None, None

    scaler_path = os.path.join(model_dir, scaler_filename)
    feature_list_path = os.path.join(model_dir, feature_list_filename)

    if not os.path.exists(scaler_path): print(f"!!! Scaler文件 '{scaler_path}' 不存在 (OOF)。"); return None, None, None, None
    if not os.path.exists(feature_list_path): print(f"!!! 特征列表 '{feature_list_path}' 不存在 (OOF)。"); return None, None, None, None

    try:
        scaler = joblib.load(scaler_path)
        with open(feature_list_path, 'r', encoding='utf-8') as f: selected_feature_names = json.load(f)
        if not selected_feature_names or not isinstance(selected_feature_names, list):
            print(f"!!! 错误: 从 '{feature_list_path}' 加载的特征列表为空或格式不正确 (OOF)。"); return None, None, None, None
        return scaler, selected_feature_names, model_meta_oof, model_dir
    except Exception as e:
        print(f"!!! 加载Scaler/特征列表失败 (OOF) for '{base_model_target_name}': {e}")
        return None, None, None, None


# ===============================================================
# WORKER THREAD FUNCTION: TRAINING PIPELINE
# ===============================================================
def run_training_pipeline(target_type_filter=None):
    global binance_client, _main_root
    filter_desc = ""
    if target_type_filter == "UP_ONLY": filter_desc = " (仅上涨目标)"
    elif target_type_filter == "DOWN_ONLY": filter_desc = " (仅下跌目标)"

    print(f"\n===== 开始训练/筛选/优化流程{filter_desc} =====")
    if _main_root and _main_root.winfo_exists():
        gui.update_gui_safe(gui.update_status, f"开始训练{filter_desc}...", "neutral")

    all_targets_results = {}
    targets_to_process_this_run = []

    # --- 确定本次运行要处理哪些目标 ---
    if hasattr(config, 'PREDICTION_TARGETS') and isinstance(config.PREDICTION_TARGETS, list):
        for target_config_base_item in config.PREDICTION_TARGETS:
            if isinstance(target_config_base_item, dict) and 'name' in target_config_base_item:
                target_name_iter = target_config_base_item['name']
                try:
                    full_target_cfg_iter = safe_get_target_config(target_name_iter)
                    current_target_type_from_cfg = full_target_cfg_iter.get('target_variable_type', 'BOTH').upper()
                    if target_type_filter is None: targets_to_process_this_run.append(target_config_base_item)
                    elif target_type_filter == "UP_ONLY" and current_target_type_from_cfg == "UP_ONLY": targets_to_process_this_run.append(target_config_base_item)
                    elif target_type_filter == "DOWN_ONLY" and current_target_type_from_cfg == "DOWN_ONLY": targets_to_process_this_run.append(target_config_base_item)
                except ValueError as ve_cfg_filter: print(f"!!! 获取目标 '{target_name_iter}' 配置以应用过滤器时失败: {ve_cfg_filter}")
                except Exception as e_filter_exc: print(f"!!! 处理目标 '{target_name_iter}' 以应用过滤器时出错: {e_filter_exc}")
    if not targets_to_process_this_run:
        message_no_targets = f"没有找到与过滤器 '{target_type_filter}' 匹配的目标进行训练。" if target_type_filter else "没有配置任何预测目标进行训练。"
        print(message_no_targets)
        if _main_root and _main_root.winfo_exists():
            gui.update_gui_safe(gui.update_status, message_no_targets, "warning")
            messagebox.showwarning("无匹配目标", message_no_targets)
        app_state.set_training_in_progress(False);
        if _main_root and _main_root.winfo_exists():
            gui.update_gui_safe(gui.set_button_state, "train_any", tk.NORMAL)
            gui.update_gui_safe(gui.set_button_state, "predict", tk.NORMAL)
            gui.update_gui_safe(gui.reset_progress_bar)
        return

    def get_unscaled_features_and_target(df_chunk, current_target_config, current_client, target_name_for_log=""):
        """
        为给定的数据块df_chunk添加所有特征并创建目标变量。
        返回未缩放的X_df, y_series, 和所有特征名列表。

        严格防止数据泄露的特征生成函数：
        1. 确保时间序列顺序
        2. 防止未来数据泄露
        3. 移除目标变量相关列
        4. 验证特征计算的时间一致性
        """
        log_prefix = f"  ({target_name_for_log} - get_unscaled) "
        if df_chunk is None or df_chunk.empty:
            print(f"{log_prefix}输入 df_chunk 为空，无法处理。")
            return None, None, []

        # 验证输入参数类型是否为DataFrame
        if not isinstance(df_chunk, pd.DataFrame):
            print(f"{log_prefix}输入参数检查不够严格，df_chunk数据类型是{type(df_chunk)}而非DataFrame。")
            return None, None, []

        # 🛡️ 防泄露检查1: 确保数据按时间排序
        if not df_chunk.index.is_monotonic_increasing:
            print(f"{log_prefix}⚠️  数据未按时间排序，正在重新排序以防止时间泄露")
            df_chunk = df_chunk.sort_index()

        # 🛡️ 防泄露检查2: 验证时间索引的连续性
        if len(df_chunk) > 1:
            time_diffs = df_chunk.index.to_series().diff().dropna()
            expected_interval = pd.Timedelta(current_target_config.get('interval', '15m'))
            abnormal_gaps = time_diffs[time_diffs > expected_interval * 3]  # 允许一些容差
            if len(abnormal_gaps) > 0:
                print(f"{log_prefix}⚠️  发现 {len(abnormal_gaps)} 个异常时间间隔，可能存在数据跳跃")

        # 🛡️ 防泄露检查3: 记录原始时间范围
        original_start_time = df_chunk.index[0]
        original_end_time = df_chunk.index[-1]
        print(f"{log_prefix}🕐 处理时间范围: {original_start_time} 到 {original_end_time} ({len(df_chunk)} 条记录)")

        # 1. 添加基础分类特征 (TA指标, K线形态, 时间等)
        #    add_classification_features 应该返回一个包含所有这些基础特征的DataFrame
        #    并且它内部应该已经处理了NaN填充
        print(f"{log_prefix}调用 data_utils.add_classification_features...")
        # 这里必须使用副本，因为add_classification_features会修改输入数据
        df_primary_feats = data_utils.add_classification_features(df_chunk.copy(), current_target_config)
        if df_primary_feats is None or df_primary_feats.empty:
            print(f"{log_prefix}data_utils.add_classification_features 返回为空。")
            return None, None, []
        print(f"{log_prefix}data_utils.add_classification_features 完成，形状: {df_primary_feats.shape}")

        # 2. 添加MTFA特征 (如果启用)
        #    add_mtfa_features_to_df 应该在df_primary_feats的基础上添加更高时间周期的特征
        df_combined_feats = df_primary_feats # 默认等于基础特征
        if current_target_config.get('enable_mtfa', False):
            print(f"{log_prefix}MTFA已启用，调用 data_utils.add_mtfa_features_to_df...")
            # current_client (即 binance_client) 会被传递给 add_mtfa_features_to_df，用于获取其他时间框架的数据
            # 这里必须使用副本，因为add_mtfa_features_to_df会修改输入数据
            df_mtfa_temp = data_utils.add_mtfa_features_to_df(df_primary_feats.copy(), current_target_config, current_client)
            if df_mtfa_temp is not None and not df_mtfa_temp.empty:
                df_combined_feats = df_mtfa_temp
                print(f"{log_prefix}data_utils.add_mtfa_features_to_df 完成，形状: {df_combined_feats.shape}")
            else:
                print(f"{log_prefix}警告: MTFA已启用但 data_utils.add_mtfa_features_to_df 返回为空，将仅使用基础特征。")
        else:
            print(f"{log_prefix}MTFA未启用。")
            
        # 3. 创建目标变量
        #    create_target_variable 会在 df_combined_feats 的基础上创建目标列，并可能移除一些行
        print(f"{log_prefix}调用 data_utils.create_target_variable...")
        # 这里必须使用副本，因为create_target_variable会修改输入数据
        df_final_for_X, target_col_name_internal = data_utils.create_target_variable(df_combined_feats.copy(), current_target_config)
        
        if df_final_for_X is None or df_final_for_X.empty:
            print(f"{log_prefix}data_utils.create_target_variable 返回的DataFrame为空。")
            return None, None, []
        if not target_col_name_internal or target_col_name_internal not in df_final_for_X.columns:
            print(f"{log_prefix}错误: data_utils.create_target_variable 未能成功创建目标列 '{target_col_name_internal}' 或列名不匹配。")
            return None, None, []
        print(f"{log_prefix}data_utils.create_target_variable 完成，目标列: {target_col_name_internal}, 数据形状: {df_final_for_X.shape}")
            
        # 4. 提取目标Series (y) - 这里使用副本是必要的，因为后续可能会修改y_series_from_final
        y_series_from_final = df_final_for_X[target_col_name_internal].copy()

        # 5. 确定用作特征的列 (X)
        #    排除原始OHLCV列、目标列和字符串类型列
        base_cols_to_exclude = {'open', 'high', 'low', 'close', 'volume', 'qav', 'n', 'tbbav', 'tbqav'} # 包含原始数据中的一些列名

        # 从 df_final_for_X (可能已经因目标变量创建而移除了行) 中选择特征列
        features_to_use_names = []
        for col in df_final_for_X.columns:
            if (col not in base_cols_to_exclude and
                not col.startswith('target_') and
                col != target_col_name_internal and
                not col.endswith('_name') and  # 排除形态名称列
                col != 'candlestick_pattern_name'):  # 明确排除K线形态名称列

                # 检查列的数据类型，只保留数值类型的列
                if df_final_for_X[col].dtype in ['int64', 'float64', 'int32', 'float32', 'bool']:
                    features_to_use_names.append(col)
                else:
                    print(f"{log_prefix}跳过非数值列: {col} (类型: {df_final_for_X[col].dtype})")
        
        if not features_to_use_names:
            print(f"{log_prefix}错误: 未找到任何可用的特征列 (排除基础OHLCV和目标列后)。")
            return None, None, []
        print(f"{log_prefix}提取了 {len(features_to_use_names)} 个特征列。")
        if len(features_to_use_names) < 50: # 只是一个示例性的少量特征警告
             print(f"   {log_prefix}注意: 提取的特征数量较少 ({len(features_to_use_names)}). 前5个: {features_to_use_names[:5]}")


        # 创建特征DataFrame - 这里可以使用视图而不是副本，因为后续不会修改X_unscaled_df的内容
        X_unscaled_df = df_final_for_X[features_to_use_names]

        # 🛡️ 防泄露检查4: 移除任何可能包含未来信息的特征
        future_leak_patterns = ['_future_', '_next_', '_ahead_', '_forward_', '_target_', 'target_']
        leaked_features = []
        for feature in features_to_use_names:
            if any(pattern in feature.lower() for pattern in future_leak_patterns):
                leaked_features.append(feature)

        if leaked_features:
            print(f"{log_prefix}🚫 发现可能包含未来信息的特征，将移除: {leaked_features}")
            features_to_use_names = [f for f in features_to_use_names if f not in leaked_features]
            X_unscaled_df = X_unscaled_df[features_to_use_names]

        # 🛡️ 防泄露检查5: 验证特征时间范围不超过原始数据
        feature_start_time = X_unscaled_df.index[0] if not X_unscaled_df.empty else None
        feature_end_time = X_unscaled_df.index[-1] if not X_unscaled_df.empty else None

        if feature_start_time and feature_end_time:
            if feature_start_time < original_start_time or feature_end_time > original_end_time:
                print(f"{log_prefix}⚠️  特征时间范围 ({feature_start_time} 到 {feature_end_time}) 超出原始数据范围")
                # 裁剪到原始时间范围内
                X_unscaled_df = X_unscaled_df.loc[original_start_time:original_end_time]
                print(f"{log_prefix}🔧 已裁剪特征到原始时间范围")

        # 6. 确保 X 和 y 的索引对齐 (通常在create_target_variable之后它们应该是对齐的，但双重保险)
        common_index = X_unscaled_df.index.intersection(y_series_from_final.index)
        if len(common_index) != len(X_unscaled_df) or len(common_index) != len(y_series_from_final):
            print(f"{log_prefix}警告: X_unscaled_df ({len(X_unscaled_df)}) 和 y_series_from_final ({len(y_series_from_final)}) 在提取后索引长度不完全一致，将按共同索引 ({len(common_index)}) 对齐。")

        X_unscaled_df = X_unscaled_df.loc[common_index]
        y_series_from_final = y_series_from_final.loc[common_index]

        if X_unscaled_df.empty or y_series_from_final.empty:
            print(f"{log_prefix}错误: 最终对齐后 X 或 y 为空。")
            return None, None, []
            

        # 7. 再次检查NaN (理论上 data_utils 中的函数应该已经处理了，但这里可以加一道防线)
        if X_unscaled_df.isnull().values.any():
            nan_cols_in_X = X_unscaled_df.columns[X_unscaled_df.isnull().any()].tolist()
            nan_count_per_col = X_unscaled_df.isnull().sum()
            
            print(f"{log_prefix}警告: 未缩放的X中包含NaN值，位于列: {nan_cols_in_X}。NaN数量统计:")
            for col, count in nan_count_per_col.items():
                if count > 0:
                    print(f"   {col}: {count}个NaN ({count/len(X_unscaled_df):.2%})")
            
            # 优先尝试删除包含NaN的行
            X_clean = X_unscaled_df.dropna()
            y_clean = y_series_from_final.loc[X_clean.index]
            
            if len(X_clean) < len(X_unscaled_df) * 0.9:  # 如果删除了超过10%的数据
                print(f"{log_prefix}警告: 删除NaN行后数据量减少过多 (原始: {len(X_unscaled_df)}, 删除后: {len(X_clean)})")
                
                # 如果删除过多数据，则尝试使用安全填充方法
                print(f"{log_prefix}尝试使用基于历史数据的安全填充方法...")
                
                # 使用data_utils中的safe_fill_nans函数进行NaN填充
                # 对每一列应用安全填充
                for col in X_unscaled_df.columns:
                    # 确定合适的默认值
                    default_val = 0
                    # 如果列名包含某些关键词，可以设置不同的默认值
                    if any(price_term in col.lower() for price_term in ['price', 'close', 'open', 'high', 'low']):
                        # 价格相关列使用该列非NaN值的均值
                        non_nan_vals = X_unscaled_df[col].dropna()
                        if len(non_nan_vals) > 0:
                            default_val = non_nan_vals.mean()
                    
                    X_unscaled_df[col] = data_utils.safe_fill_nans(X_unscaled_df[col], default_val)
                
                # 填充后再次验证
                if X_unscaled_df.isnull().values.any():
                    print(f"{log_prefix}错误: 填充后仍有NaN，处理失败。")
                    return None, None, []
                
                # 增加验证步骤，确保数据完整性
                nan_check = X_unscaled_df.isnull().sum().sum()
                if nan_check > 0:
                    print(f"{log_prefix}错误: 二次验证发现仍有{nan_check}个NaN值，处理失败。")
                    return None, None, []
                
                # 使用填充后的数据
                X_clean = X_unscaled_df
                y_clean = y_series_from_final
            else:
                print(f"{log_prefix}已删除包含NaN的行，剩余数据量: {len(X_clean)}")
            
            X_unscaled_df = X_clean
            y_series_from_final = y_clean

        # 🛡️ 防泄露检查6: 最终验证 - 确保没有数据泄露
        final_start_time = X_unscaled_df.index[0] if not X_unscaled_df.empty else None
        final_end_time = X_unscaled_df.index[-1] if not X_unscaled_df.empty else None

        # 验证时间范围
        if final_start_time and final_end_time:
            if final_start_time < original_start_time or final_end_time > original_end_time:
                print(f"{log_prefix}❌ 最终验证失败: 特征时间范围仍然超出原始数据范围")
                return None, None, []

        # 验证特征名称中没有泄露信息
        final_leaked_features = []
        for feature in features_to_use_names:
            if any(pattern in feature.lower() for pattern in ['target_', '_target', 'future_', '_future']):
                final_leaked_features.append(feature)

        if final_leaked_features:
            print(f"{log_prefix}❌ 最终验证失败: 仍然存在可能泄露的特征: {final_leaked_features}")
            return None, None, []

        # 验证数据完整性
        if X_unscaled_df.isnull().values.any():
            print(f"{log_prefix}❌ 最终验证失败: 特征数据中仍然存在NaN值")
            return None, None, []

        if np.isinf(X_unscaled_df.values).any():
            print(f"{log_prefix}❌ 最终验证失败: 特征数据中存在无穷大值")
            return None, None, []

        # 验证时间序列的单调性
        if not X_unscaled_df.index.is_monotonic_increasing:
            print(f"{log_prefix}❌ 最终验证失败: 特征数据的时间索引不是单调递增的")
            return None, None, []

        print(f"{log_prefix}✅ 防泄露验证通过")
        print(f"{log_prefix}get_unscaled_features_and_target 完成。返回 X_shape: {X_unscaled_df.shape}, y_shape: {y_series_from_final.shape}, num_features: {len(features_to_use_names)}")
        return X_unscaled_df, y_series_from_final, features_to_use_names



    def optuna_objective_on_subset(trial, X_train_obj_param, y_train_obj_param, feature_names_obj_list_param, current_target_config_param):
        optuna_params_trial = {
            'learning_rate': trial.suggest_float('learning_rate', 0.003, 0.03, log=True),
            'num_leaves': trial.suggest_int('num_leaves', 10, 40), 'max_depth': trial.suggest_int('max_depth', 3, 8),
            'reg_alpha': trial.suggest_float('reg_alpha', 4.0, 20.0, log=True), 'reg_lambda': trial.suggest_float('reg_lambda', 4.0, 20.0, log=True),
            'colsample_bytree': trial.suggest_float('colsample_bytree', 0.5, 0.9), 'subsample': trial.suggest_float('subsample', 0.5, 0.95),
            'min_child_samples': trial.suggest_int('min_child_samples', 40, 80)}
        fixed_params_optuna_trial = {'objective': 'binary', 'verbosity': -1, 'device_type': current_target_config_param.get('device_type', 'gpu'),
                                   'n_jobs': 1, 'random_state': 42 + trial.number,
                                   'n_estimators': current_target_config_param.get('optuna_trial_n_estimators_max', 1000),
                                   'subsample_freq': current_target_config_param.get('subsample_freq', 1)}
        lgbm_trial_params = {**fixed_params_optuna_trial, **optuna_params_trial}
        cv = TimeSeriesSplit(n_splits=current_target_config_param.get('optuna_cv_folds', 3)); scores = []
        esr = current_target_config_param.get('optuna_trial_early_stopping_rounds', 50)
        cb = [lgb.early_stopping(esr, verbose=False, first_metric_only=False)]
        eval_m = current_target_config_param.get('optuna_trial_eval_metric', 'binary_logloss')

        # Determine scaler type from config
        scaler_type_str = current_target_config_param.get('scaler_type', 'MinMaxScaler')

        try:
            for fold, (train_idx, val_idx) in enumerate(cv.split(X_train_obj_param, y_train_obj_param)):
                # Extract unscaled data for this fold
                X_t_unscaled, X_v_unscaled = X_train_obj_param[train_idx], X_train_obj_param[val_idx]
                y_t, y_v = y_train_obj_param[train_idx], y_train_obj_param[val_idx]

                if len(X_v_unscaled) < 2 or (len(np.unique(y_v)) < 2 and len(y_v) > 0):
                    scores.append(0.0); continue

                # Create and fit independent scaler for this fold
                fold_scaler = None
                if scaler_type_str == 'StandardScaler':
                    fold_scaler = StandardScaler()
                elif scaler_type_str == 'MinMaxScaler':
                    fold_scaler = MinMaxScaler()
                else:
                    print(f"    警告: Optuna Trial {trial.number}, Fold {fold} 未知的 scaler_type '{scaler_type_str}', 将使用 MinMaxScaler.")
                    fold_scaler = MinMaxScaler()

                # Fit scaler on current fold's training data and transform both train and validation
                try:
                    # Handle NaN/Inf values before fitting scaler
                    X_t_clean = pd.DataFrame(X_t_unscaled, columns=feature_names_obj_list_param) if feature_names_obj_list_param else pd.DataFrame(X_t_unscaled)
                    X_v_clean = pd.DataFrame(X_v_unscaled, columns=feature_names_obj_list_param) if feature_names_obj_list_param else pd.DataFrame(X_v_unscaled)

                    if X_t_clean.isnull().values.any() or np.isinf(X_t_clean.values).any():
                        for col in X_t_clean.columns:
                            X_t_clean[col] = data_utils.safe_fill_nans(X_t_clean[col], default_value=0)
                        X_t_clean = X_t_clean.replace([np.inf, -np.inf], 0)

                    if X_v_clean.isnull().values.any() or np.isinf(X_v_clean.values).any():
                        for col in X_v_clean.columns:
                            X_v_clean[col] = data_utils.safe_fill_nans(X_v_clean[col], default_value=0)
                        X_v_clean = X_v_clean.replace([np.inf, -np.inf], 0)

                    # Fit scaler on training fold and transform both sets
                    fold_scaler.fit(X_t_clean)
                    X_t_scaled = fold_scaler.transform(X_t_clean)
                    X_v_scaled = fold_scaler.transform(X_v_clean)

                except Exception as e_scale:
                    print(f"    !!! Optuna Trial {trial.number}, Fold {fold} Scaler fit/transform失败: {e_scale}")
                    scores.append(0.0); continue

                # 应用SMOTE过采样 (仅在训练数据上)
                smote_enabled = getattr(config, 'SMOTE_GLOBAL_ENABLE', True) and target_config.get('smote_enable', True)
                if smote_enabled:
                    unique_labels, counts = np.unique(y_t, return_counts=True)
                    smote_min_threshold = target_config.get('smote_min_samples_threshold', getattr(config, 'SMOTE_MIN_SAMPLES_THRESHOLD', 5))

                    # 确定目标类别：对于UP模型，目标类别是"明确上涨"（标签1）
                    target_variable_type = target_config.get('target_variable_type', 'BOTH').upper()
                    if target_variable_type == "UP_ONLY":
                        # 对于UP模型，我们总是对"明确上涨"类别（标签1）进行SMOTE
                        target_class_for_smote = 1
                        target_class_name = "明确上涨"
                    elif target_variable_type == "DOWN_ONLY":
                        # 对于DOWN模型，我们总是对"明确下跌"类别（标签1）进行SMOTE
                        target_class_for_smote = 1
                        target_class_name = "明确下跌"
                    else:
                        # 对于其他情况，使用传统的少数类逻辑
                        minority_class_idx = np.argmin(counts)
                        target_class_for_smote = unique_labels[minority_class_idx]
                        target_class_name = f"少数类(标签{target_class_for_smote})"

                    # 检查目标类别的样本数量
                    if target_class_for_smote in unique_labels:
                        target_class_idx = np.where(unique_labels == target_class_for_smote)[0][0]
                        target_class_count = counts[target_class_idx]

                        if target_class_count < smote_min_threshold:
                            print(f"    Optuna Trial {trial.number}, Fold {fold}: {target_class_name}样本 ({target_class_count}) 过少，跳过SMOTE。")
                            X_t_resampled = X_t_scaled
                            y_t_resampled = y_t
                        else:
                            try:
                                print(f"    Optuna Trial {trial.number}, Fold {fold}: 应用SMOTE前类别分布: {dict(zip(unique_labels, counts))}")
                                print(f"    Optuna Trial {trial.number}, Fold {fold}: 对{target_class_name}(标签{target_class_for_smote})进行SMOTE过采样")
                                # k_neighbors 的值不能超过目标类别样本数 - 1
                                smote_k_neighbors_config = target_config.get('smote_k_neighbors', getattr(config, 'SMOTE_DEFAULT_K_NEIGHBORS', 4))
                                smote_k_neighbors = min(smote_k_neighbors_config, target_class_count - 1) if target_class_count > 1 else 1
                                smote_random_state = target_config.get('smote_random_state', getattr(config, 'SMOTE_RANDOM_STATE', 42))
                                sm = SMOTE(random_state=smote_random_state, k_neighbors=smote_k_neighbors)
                                X_t_resampled, y_t_resampled = sm.fit_resample(X_t_scaled, y_t)
                                unique_labels_after, counts_after = np.unique(y_t_resampled, return_counts=True)
                                print(f"    Optuna Trial {trial.number}, Fold {fold}: 应用SMOTE后类别分布: {dict(zip(unique_labels_after, counts_after))}")
                            except ValueError as e_smote:
                                print(f"    !!! Optuna Trial {trial.number}, Fold {fold}: SMOTE执行失败: {e_smote}。将使用原始训练数据。")
                                X_t_resampled = X_t_scaled
                                y_t_resampled = y_t
                    else:
                        print(f"    Optuna Trial {trial.number}, Fold {fold}: 目标类别 {target_class_for_smote} 在当前折中不存在，跳过SMOTE。")
                        X_t_resampled = X_t_scaled
                        y_t_resampled = y_t
                else:
                    print(f"    Optuna Trial {trial.number}, Fold {fold}: SMOTE已禁用，使用原始训练数据。")
                    X_t_resampled = X_t_scaled
                    y_t_resampled = y_t

                # Train model with resampled data
                m = LGBMClassifier(**lgbm_trial_params)
                m.fit(X_t_resampled, y_t_resampled, eval_set=[(X_v_scaled, y_v)], eval_metric=eval_m, callbacks=cb, feature_name=feature_names_obj_list_param or 'auto')

                # Make predictions on scaled validation data
                pred = m.predict(X_v_scaled); proba = m.predict_proba(X_v_scaled)
                score = calculate_optuna_metric(current_target_config_param.get('optuna_metric', 'f1'), y_v, pred, proba); scores.append(score)
                trial.report(np.mean(scores) if scores else 0.0, fold);
                if trial.should_prune(): raise optuna.TrialPruned()
            return np.mean(scores) if scores else 0.0
        except optuna.TrialPruned: raise
        except Exception as e: print(f"Optuna CV Err {trial.number}, Fold {fold if 'fold' in locals() else 'N/A'}: {e}"); return 0.0

    try:
        if binance_client is None and not getattr(config, 'GA_RUN_OFFLINE', False):
            raise ConnectionError("Binance Client 未初始化 (且非离线模式)")

        for target_config_base_idx, target_config_base in enumerate(targets_to_process_this_run):
            target_name = target_config_base.get('name', f"UnnamedTarget_{target_config_base_idx}")
            try:
                target_config = safe_get_target_config(target_name)
            except ValueError as ve_cfg:
                print(f"!!! 获取目标 '{target_name}' 配置失败: {ve_cfg}")
                all_targets_results[target_name] = {'status': f'配置获取失败: {ve_cfg}'}
                continue

            # 🔧 修复：跳过META_MODEL_DISPLAY类型的目标，它们不应该参与独立训练
            target_variable_type = target_config.get('target_variable_type', '').upper()
            if target_variable_type == 'META_MODEL_DISPLAY':
                print(f"  跳过目标 '{target_name}': 这是元模型显示条目，不参与独立训练流程")
                all_targets_results[target_name] = {'status': '跳过 - 元模型显示条目'}
                continue

            model_dir = target_config.get('model_save_dir')
            if not model_dir:
                print(f"!! 目标 '{target_name}' 未配置 'model_save_dir', 跳过。")
                all_targets_results[target_name] = {'status': 'model_save_dir 未配置'}
                continue
            os.makedirs(model_dir, exist_ok=True)
            target_name_for_file = target_name.replace('/', '_').replace(':', '_')
            pred_periods_cfg = target_config.get('prediction_periods', [1])
            pred_periods_for_file = pred_periods_cfg[0] if isinstance(pred_periods_cfg, list) and pred_periods_cfg else 1
            interval_val_str = target_config.get('interval', 'Xm').rstrip('mhd')
            try: minutes_display_val = int(interval_val_str) * pred_periods_for_file
            except ValueError: minutes_display_val = target_config.get('prediction_minutes_display', '?')
            minutes_display_for_file = str(minutes_display_val)
            
            fitted_scaler = None; feature_names_current = []; model_suffix_parts_for_file = []
            rfe_succeeded = False; initial_importance_succeeded = False; optuna_completed_successfully = False
            saved_scaler_filename_shared = None 
            saved_feature_list_filename_shared = None

            current_target_gui_metrics = {'status': '处理中...'} 
            fold_model_artifacts_list = [] 
            ensemble_test_metrics_collected_folds = [] 
            all_folds_eval_details = []
            current_target_summary_for_console_report = {} 

            print(f"\n--->>> 开始处理目标: {target_name} (预测未来 {minutes_display_for_file}m, 基于 {target_config.get('interval')} K线) <<<---")
            print(f"      保存目录: {model_dir}")
            device_type_cfg = target_config.get('device_type', 'gpu')
            # 显示特征选择配置详情
            rfe_status = '开' if target_config.get('rfe_enable', False) else '关'
            imp_thresh_status = '开' if target_config.get('importance_thresholding_enable', False) else '关'
            optuna_status = '开' if target_config.get('optuna_enable', False) else '关'

            print(f"      Config: RFE={rfe_status}, ImpThresh={imp_thresh_status}, Optuna={optuna_status}, Device:{device_type_cfg}")

            # 显示重要性筛选的详细配置
            if target_config.get('importance_thresholding_enable', False):
                imp_top_n = target_config.get('importance_top_n_features', None)
                imp_threshold = target_config.get('importance_threshold_value', 0)
                if imp_top_n is not None and imp_top_n > 0:
                    print(f"      重要性筛选: Top-{imp_top_n} 特征模式")
                else:
                    print(f"      重要性筛选: 阈值模式 (阈值={imp_threshold})")

            # 配置验证警告
            if target_config.get('importance_thresholding_enable', False):
                imp_top_n = target_config.get('importance_top_n_features', None)
                imp_threshold = target_config.get('importance_threshold_value', 0)
                if imp_top_n is not None and imp_top_n > 0 and imp_threshold > 0:
                    print(f"      ⚠️  配置提示: 同时设置了 importance_top_n_features ({imp_top_n}) 和 importance_threshold_value ({imp_threshold})")
                    print(f"          将优先使用 Top-N 模式，阈值设置将被忽略")
            if _main_root and _main_root.winfo_exists():
                gui.update_gui_safe(gui.update_status, f"处理训练 Target '{target_name}'...", "neutral")
                gui.update_gui_safe(gui.update_prediction_display, target_name, f"准备数据 ({target_config.get('interval')})...")
                gui.update_gui_safe(gui.set_progress_bar_indeterminate, True)

            try:
                # --- 步骤 1: 数据获取和划分 ---
                print(f"  步骤 1 ({target_name}): 数据获取和划分...")
                fetch_limit = config.DATA_FETCH_LIMIT 
                df_raw = data_utils.fetch_binance_history(binance_client, target_config.get('symbol'), target_config.get('interval'), fetch_limit)
                if df_raw is None or df_raw.empty: raise ValueError(f"未能获取 {target_config.get('interval')} K线数据 for {target_name}")

                # +++ 2. 数据验证 +++
                print(f"  步骤 1b ({target_name}): 验证获取的原始K线数据...")
                validation_result = DataValidator.validate_ohlcv_data(df_raw) # 直接使用原始数据，DataValidator内部应该不修改输入数据
                
                if not validation_result['is_valid']:
                    error_msg_val = f"原始K线数据 for '{target_name}' 未通过验证: {validation_result['errors']}"
                    print(f"!!! {error_msg_val}")
                    # 根据你的需求，这里可以选择跳过此目标或中止
                    if _main_root and _main_root.winfo_exists():
                        gui.update_gui_safe(gui.update_status, f"目标 '{target_name}' 数据验证失败", "error")
                        messagebox.showerror("数据验证失败", error_msg_val, parent=_main_root if _main_root else None)
                    all_targets_results[target_name] = {'status': '数据验证失败', 'error_message': validation_result['errors']}
                    continue # 跳到下一个目标
                
                if validation_result['warnings']:
                    print(f"  警告 ({target_name}): 数据验证时发现以下问题（已尝试清理）: {validation_result['warnings']}")
                
                df_raw = validation_result['cleaned_data'] # 使用验证和可能清理过的数据
                print(f"  步骤 1b ({target_name}): 原始数据验证通过 (或已清理)。")
                # +++ 验证结束 +++


                n_total = len(df_raw)
                train_ratio_cfg = target_config.get('train_ratio', config.TRAIN_RATIO)
                val_ratio_cfg = target_config.get('validation_ratio', config.VALIDATION_RATIO)
                n_train_end = int(n_total * train_ratio_cfg); n_val_end = n_train_end + int(n_total * val_ratio_cfg)
                min_samples_train = target_config.get("min_train_samples", 50)
                min_samples_val_test = target_config.get("min_val_test_samples", 20)
                if n_train_end < min_samples_train or (n_val_end - n_train_end) < min_samples_val_test or (n_total - n_val_end) < min_samples_val_test:
                    raise ValueError(f"数据量不足 ({n_total}条) for {target_name} 以进行有效划分.")
                df_train_raw_chunk = df_raw.iloc[:n_train_end]; df_val_raw_chunk = df_raw.iloc[n_train_end:n_val_end]; df_test_raw_chunk = df_raw.iloc[n_val_end:]
                print(f"    数据划分: 训练 {len(df_train_raw_chunk)}, 验证 {len(df_val_raw_chunk)}, 测试 {len(df_test_raw_chunk)}")

                # --- 步骤 2: 准备未缩放训练集特征 ---
                print(f"  步骤 2 ({target_name}): 准备未缩放的训练集特征 (用于特征选择)...")
                X_train_raw_df_all_feats, y_train_series_raw, feature_names_all_from_data_prep = \
                    get_unscaled_features_and_target(df_train_raw_chunk, target_config, binance_client, f"{target_name}-TrainPrep")
                if X_train_raw_df_all_feats is None or y_train_series_raw is None or not feature_names_all_from_data_prep:
                    raise ValueError(f"为 {target_name} 准备未缩放训练特征失败。")
                print(f"    -> {target_name}: 原始特征数: {len(feature_names_all_from_data_prep)}")

                # --- 步骤 3: 特征选择 ---
                print(f"\n  步骤 3 ({target_name}): 特征选择流程...")
                gui.update_gui_safe(gui.update_status, f"T '{target_name}': 特征选择...", "neutral") # 更新GUI状态

                # 避免创建不必要的副本，直接使用原始数据的视图
                X_for_selection_temp_df = X_train_raw_df_all_feats
                y_for_selection_temp_np = y_train_series_raw.values
                # feature_names_for_selection_temp 初始化为所有从数据准备阶段得到的特征
                feature_names_for_selection_temp = list(X_for_selection_temp_df.columns) # 或者直接用 feature_names_all_from_data_prep
                model_suffix_parts_for_file = [] # 用于记录特征选择方法，影响最终模型/产物文件名
                rfe_succeeded = False
                initial_importance_succeeded = False # 虽然你之前说关闭了，但保留变量以防逻辑依赖

                # --- 🎯 两阶段特征选择：重要性初筛 + RFE精选 ---
                use_two_stage_cfg = target_config.get('two_stage_feature_selection_enable', True)
                # 构建特征选择产物文件的预期名称
                if use_two_stage_cfg:
                    features_filename_default = f"final_selected_features_two_stage_{target_name_for_file}_{minutes_display_for_file}m.json"
                else:
                    # 兼容传统RFE文件名
                    features_filename_default = f"final_selected_features_rfe_{target_name_for_file}_{minutes_display_for_file}m.json"
                features_filepath_default = os.path.join(model_dir, features_filename_default)

                if use_two_stage_cfg:
                    print(f"\n  步骤 3a ({target_name}): 🎯 两阶段特征选择已启用...")
                    print(f"    输入特征数量: {len(feature_names_for_selection_temp)}")
                    if _main_root and _main_root.winfo_exists(): gui.update_gui_safe(gui.set_progress_bar_indeterminate, True) # 进度条

                    try:
                        # 使用新的两阶段特征选择函数
                        selected_features, selection_stats = data_utils.two_stage_feature_selection(
                            X_train_raw_df_all_feats[feature_names_for_selection_temp],
                            y_for_selection_temp_np,
                            target_config,
                            target_name,
                            device_type_cfg,
                            verbose=True
                        )

                        if selected_features:
                            features_removed = len(feature_names_for_selection_temp) - len(selected_features)
                            print(f"    两阶段特征选择完成: 从 {len(feature_names_for_selection_temp)} 个特征中选定 {len(selected_features)} 个，移除 {features_removed} 个")
                            print(f"    选择统计: 第一阶段={selection_stats.get('stage1_features', 0)}, 第二阶段={selection_stats.get('stage2_features', 0)}")
                            print(f"    压缩比例: {len(selected_features)/len(feature_names_for_selection_temp):.1%}")
                            print(f"    选定特征 Top5: {selected_features[:5]}")

                            feature_names_for_selection_temp = selected_features # 更新当前选择的特征列表
                            rfe_succeeded = True  # 标记特征选择成功
                            if "two_stage" not in model_suffix_parts_for_file: model_suffix_parts_for_file.append("two_stage")

                            # 保存两阶段选定的特征列表
                            with open(features_filepath_default, 'w') as f_json_save:
                                json.dump(selected_features, f_json_save, indent=2)
                            print(f"    -> 两阶段选定的特征列表已保存到: {features_filepath_default}")

                            # 同时保存选择统计信息
                            stats_filename = features_filename_default.replace('.json', '_stats.json')
                            stats_filepath = os.path.join(model_dir, stats_filename)
                            with open(stats_filepath, 'w') as f_stats:
                                json.dump(selection_stats, f_stats, indent=2)
                            print(f"    -> 特征选择统计信息已保存到: {stats_filepath}")
                        else:
                            print(f"    警告 ({target_name}): 两阶段特征选择未选定任何特征。将使用原始特征列表。")
                            # feature_names_for_selection_temp 保持不变
                    except Exception as e_two_stage:
                        print(f"    !!! ({target_name}) 两阶段特征选择出错: {e_two_stage}")
                        print(f"    将回退到传统特征选择方法...")
                        # feature_names_for_selection_temp 保持不变
                    finally:
                        if _main_root and _main_root.winfo_exists(): gui.update_gui_safe(gui.set_progress_bar_indeterminate, False)

                else: # 两阶段特征选择未启用
                    print(f"\n  步骤 3a ({target_name}): 两阶段特征选择已禁用。")
                    print(f"    当前特征数量: {len(feature_names_for_selection_temp)}")
                    if os.path.exists(features_filepath_default):
                        print(f"    发现缓存的特征文件: {features_filepath_default}")
                        print(f"    尝试加载之前保存的特征列表...")
                        try:
                            with open(features_filepath_default, 'r') as f_json_load:
                                loaded_features = json.load(f_json_load)
                            if loaded_features and isinstance(loaded_features, list):
                                # 验证加载的特征是否存在于当前所有特征中
                                valid_loaded_features = [f_name for f_name in loaded_features if f_name in X_train_raw_df_all_feats.columns]
                                if len(valid_loaded_features) != len(loaded_features):
                                    print(f"    警告: 从文件加载的特征中，有 {len(loaded_features) - len(valid_loaded_features)} 个特征在当前数据中不存在。")

                                if valid_loaded_features:
                                    features_reduced_by_cached = len(feature_names_for_selection_temp) - len(valid_loaded_features)
                                    feature_names_for_selection_temp = valid_loaded_features
                                    if "cached" not in model_suffix_parts_for_file: model_suffix_parts_for_file.append("cached") # 标记文件名后缀
                                    print(f"    ✓ 成功加载缓存的特征: 使用 {len(feature_names_for_selection_temp)} 个特征 (减少了 {features_reduced_by_cached} 个)")
                                    rfe_succeeded = True # 标记为特征选择成功
                                else:
                                    print(f"    警告: 加载的特征列表为空或所有特征均无效。将使用全部特征。")
                                    # feature_names_for_selection_temp 保持为 X_train_raw_df_all_feats.columns
                            else:
                                print(f"    警告: 加载的特征文件内容格式不正确。将使用全部特征。")
                        except Exception as e_load_json:
                            print(f"    !!! ({target_name}) 加载已保存的特征列表时出错: {e_load_json}。将使用全部特征。")
                            # feature_names_for_selection_temp 保持为 X_train_raw_df_all_feats.columns
                    else:
                        print(f"    未找到缓存的特征文件 ({features_filepath_default})。")
                        print(f"    将使用全部 {len(feature_names_for_selection_temp)} 个特征继续。")
                        # feature_names_for_selection_temp 保持为 X_train_raw_df_all_feats.columns

                # --- 基于初始重要性的特征筛选 (如果启用) ---
                # 🎯 Logic Flow Fix: 确保特征选择链条的逻辑正确
                feature_names_final_selected = feature_names_for_selection_temp[:] # 默认等于前一阶段的输出
                use_imp_thresh_cfg = target_config.get('importance_thresholding_enable', False)

                if use_imp_thresh_cfg:
                    print(f"\n  步骤 3b ({target_name}): 初始特征重要性筛选已启用 (基于 {'两阶段选择后' if rfe_succeeded else '原始'} 特征)...")
                    print(f"    输入特征数量: {len(feature_names_final_selected)}")

                    # 🎯 Logic Flow Fix: 使用前一阶段的输出作为输入
                    X_for_imp_thresh_df = X_train_raw_df_all_feats[feature_names_final_selected]
                    if not X_for_imp_thresh_df.empty:
                        temp_scaler_for_imp = StandardScaler() if target_config.get('scaler_type') == 'standard' else MinMaxScaler()
                        X_for_imp_scaled_temp_np = temp_scaler_for_imp.fit_transform(X_for_imp_thresh_df)

                        imp_model_lgbm_params = {
                            'n_estimators': target_config.get('importance_model_n_estimators', 300),
                            'learning_rate': target_config.get('learning_rate_initial_imp'),
                            'num_leaves': target_config.get('num_leaves_initial_imp'),
                            'max_depth': target_config.get('max_depth_initial_imp'),
                            'reg_alpha': target_config.get('reg_alpha_initial_imp'),
                            'reg_lambda': target_config.get('reg_lambda_initial_imp'),
                            'colsample_bytree': target_config.get('colsample_bytree_initial_imp'),
                            'subsample': target_config.get('subsample_initial_imp'),
                            'min_child_samples': target_config.get('min_child_samples_initial_imp'),
                            'objective': 'binary', 'boosting_type': 'gbdt', 'n_jobs': -1, 'verbosity': -1,
                            'random_state': target_config.get('random_state', 42) + 200,
                            'device_type': device_type_cfg
                        }
                        imp_model_lgbm_params_clean = {k:v for k,v in imp_model_lgbm_params.items() if k in VALID_LGBM_PARAM_KEYS and v is not None}
                        temp_imp_model = LGBMClassifier(**imp_model_lgbm_params_clean)

                        print(f"    训练重要性评估模型 (n_estimators={imp_model_lgbm_params_clean.get('n_estimators', 'N/A')})...")
                        # 🎯 Logic Flow Fix: 使用当前阶段的特征名列表
                        temp_imp_model.fit(X_for_imp_scaled_temp_np, y_for_selection_temp_np, feature_name=feature_names_final_selected)
                        importances = temp_imp_model.feature_importances_
                        imp_df = pd.DataFrame({'feature': feature_names_final_selected, 'importance': importances}).sort_values('importance', ascending=False)

                        # 显示重要性统计信息
                        print(f"    重要性分布: 最高={imp_df['importance'].max():.4f}, 最低={imp_df['importance'].min():.4f}, 平均={imp_df['importance'].mean():.4f}")

                        imp_thresh_val_cfg = target_config.get('importance_threshold_value', 0)
                        imp_top_n_cfg = target_config.get('importance_top_n_features', None)

                        # 详细的筛选逻辑和日志
                        if imp_top_n_cfg is not None and imp_top_n_cfg > 0:
                            print(f"    使用Top-N筛选方法: 选择重要性最高的 {imp_top_n_cfg} 个特征")
                            feature_names_final_selected = imp_df['feature'].head(imp_top_n_cfg).tolist()
                            print(f"    Top-N筛选结果: 选定 {len(feature_names_final_selected)} 个特征")
                            if len(feature_names_final_selected) > 0:
                                min_importance_selected = imp_df.iloc[len(feature_names_final_selected)-1]['importance']
                                print(f"    最低选中特征的重要性: {min_importance_selected:.4f}")
                        else:
                            print(f"    使用阈值筛选方法: 重要性阈值 = {imp_thresh_val_cfg}")
                            features_above_threshold = imp_df[imp_df['importance'] > imp_thresh_val_cfg]
                            feature_names_final_selected = features_above_threshold['feature'].tolist()
                            print(f"    阈值筛选结果: {len(features_above_threshold)} 个特征超过阈值 {imp_thresh_val_cfg}")
                            if len(features_above_threshold) > 0:
                                print(f"    超过阈值的特征重要性范围: {features_above_threshold['importance'].min():.4f} - {features_above_threshold['importance'].max():.4f}")

                        if not feature_names_final_selected:
                            print(f"    警告 ({target_name}): 初始重要性筛选后无特征剩余，使用筛选前特征。")
                            # 🎯 Logic Flow Fix: 保持前一阶段的输出不变
                            feature_names_final_selected = feature_names_for_selection_temp[:]
                        else:
                            features_removed = len(feature_names_for_selection_temp) - len(feature_names_final_selected)
                            print(f"    重要性筛选完成: 保留 {len(feature_names_final_selected)} 个特征，移除 {features_removed} 个特征")
                            initial_importance_succeeded = True
                            if "impthresh" not in model_suffix_parts_for_file: model_suffix_parts_for_file.append("impthresh")
                            # 🎯 Logic Flow Fix: 更新特征选择链条的当前状态
                            feature_names_for_selection_temp = feature_names_final_selected[:]
                    else:
                        print(f"    警告 ({target_name}): 用于重要性筛选的数据为空，跳过。")
                else:
                    print(f"\n  步骤 3b ({target_name}): 初始特征重要性筛选已禁用。")

                # --- 最终选定的特征 ---
                if not feature_names_final_selected: # 确保最终选定列表不为空
                    print(f"!!! 警告 ({target_name}): 特征选择流程后，最终选定特征列表为空！将回退使用所有初始特征。")
                    feature_names_final_selected = feature_names_all_from_data_prep[:]
                    model_suffix_parts_for_file = [] # 重置文件名后缀，因为没有成功的选择方法

                print(f"    -> {target_name}: 特征选择后，最终特征数: {len(feature_names_final_selected)}")
                feature_names_current = feature_names_final_selected[:] # 这是传递给后续步骤的特征列表 



                # --- 步骤 4 & 5: 准备最终数据集、拟合/保存Scaler, 保存特征列表 ---
                # ... (Logic as before, ensuring saved_scaler_filename_shared and saved_feature_list_filename_shared are set) ...
                print(f"  步骤 4&5 ({target_name}): 准备最终数据集、拟合Scaler、保存产物...")
                # 使用视图而非副本，减少内存消耗
                X_train_final_df = X_train_raw_df_all_feats[feature_names_current]
                y_train_final_np = y_train_series_raw.loc[X_train_final_df.index].values   
                X_val_unscaled_df_selected = pd.DataFrame(); y_val_final_np = np.array([])
                if not df_val_raw_chunk.empty:
                    X_val_raw_temp, y_val_raw_temp, _ = get_unscaled_features_and_target(df_val_raw_chunk, target_config, binance_client, f"{target_name}-ValFinalPrep")
                    if X_val_raw_temp is not None and not X_val_raw_temp.empty:
                        cols_present_val = [col for col in feature_names_current if col in X_val_raw_temp.columns]
                        X_val_unscaled_df_selected = X_val_raw_temp[cols_present_val] # 使用视图而非副本
                        if set(cols_present_val) != set(feature_names_current): print(f"    警告 ({target_name}): 并非所有选定特征 ({len(feature_names_current)}) 都在验证数据中找到 ({len(cols_present_val)}).")
                        if not X_val_unscaled_df_selected.empty: y_val_final_np = y_val_raw_temp.loc[X_val_unscaled_df_selected.index].values
                X_test_unscaled_df_selected = pd.DataFrame(); y_test_final_np = np.array([])
                if not df_test_raw_chunk.empty:
                    X_test_raw_temp, y_test_raw_temp, _ = get_unscaled_features_and_target(df_test_raw_chunk, target_config, binance_client, f"{target_name}-TestFinalPrep")
                    if X_test_raw_temp is not None and not X_test_raw_temp.empty:
                        cols_present_test = [col for col in feature_names_current if col in X_test_raw_temp.columns]
                        X_test_unscaled_df_selected = X_test_raw_temp[cols_present_test] # 使用视图而非副本
                        if set(cols_present_test) != set(feature_names_current): print(f"    警告 ({target_name}): 并非所有选定特征 ({len(feature_names_current)}) 都在测试数据中找到 ({len(cols_present_test)}).")
                        if not X_test_unscaled_df_selected.empty: y_test_final_np = y_test_raw_temp.loc[X_test_unscaled_df_selected.index].values
                if X_train_final_df.empty: raise ValueError(f"({target_name}) X_train_final_df (最终选择) 为空，无法拟合Scaler。")
                fitted_scaler = StandardScaler() if target_config.get('scaler_type') == 'standard' else MinMaxScaler()
                X_train_final_np = fitted_scaler.fit_transform(X_train_final_df) 
                if hasattr(fitted_scaler, 'feature_names_in_'): 
                    setattr(fitted_scaler, 'feature_names_in_', X_train_final_df.columns.tolist())
                scaler_fn_shared_temp = f"scaler_{target_name_for_file}_{minutes_display_for_file}m.joblib"
                saved_scaler_filename_shared = scaler_fn_shared_temp 
                joblib.dump(fitted_scaler, os.path.join(model_dir, scaler_fn_shared_temp), compress=3)
                print(f"    -> Scaler 已保存: {os.path.join(model_dir, scaler_fn_shared_temp)}")
                # 构造最终特征列表文件名，应该基于实际应用的筛选方法
                current_selection_method_suffix_parts = []
                if rfe_succeeded: # 包含了特征选择成功的情况
                    if use_two_stage_cfg:
                        current_selection_method_suffix_parts.append("two_stage")
                    else:
                        current_selection_method_suffix_parts.append("cached")
                if initial_importance_succeeded: # 如果将来启用了这个并且它成功了
                    current_selection_method_suffix_parts.append("impthresh")

                feature_selection_suffix_for_file = ("_" + "_".join(sorted(list(set(current_selection_method_suffix_parts))))) if current_selection_method_suffix_parts else ""

                # 如果特征选择成功，使用对应的文件名
                if rfe_succeeded and os.path.exists(features_filepath_default):
                    saved_feature_list_filename_shared = features_filename_default
                else: # 其他情况（特征选择失败或文件不存在）
                    saved_feature_list_filename_shared = f"final_selected_features{feature_selection_suffix_for_file}_{target_name_for_file}_{minutes_display_for_file}m.json"
                
                # 保存最终使用的特征列表 (确保是 feature_names_current)
                with open(os.path.join(model_dir, saved_feature_list_filename_shared), 'w') as f_json_feat_final_save:
                    json.dump(feature_names_current, f_json_feat_final_save, indent=2)
                print(f"    -> 最终使用的特征列表 ({len(feature_names_current)}) 已保存到: {os.path.join(model_dir, saved_feature_list_filename_shared)}")
                X_val_final_np_scaled = fitted_scaler.transform(X_val_unscaled_df_selected) if not X_val_unscaled_df_selected.empty else np.array([])
                X_test_final_np_scaled = fitted_scaler.transform(X_test_unscaled_df_selected) if not X_test_unscaled_df_selected.empty else np.array([])
                input_size_current = len(feature_names_current)

                # --- 步骤 6: Optuna ---
                # ... (Logic as before, updating best_params_from_optuna and model_suffix_parts_for_file) ...
                use_optuna_cfg = target_config.get('optuna_enable', False); best_params_from_optuna = {}
                if use_optuna_cfg:
                    print(f"\n  步骤 6 ({target_name}): Optuna 超参数优化...")
                    if X_train_final_np is None or len(X_train_final_np) < (target_config.get('optuna_cv_folds', 3) +1) or not feature_names_current : print(f"    警告 ({target_name}): Optuna 数据或特征不足，跳过。"); optuna_completed_successfully = False
                    else:
                        gui.update_gui_safe(gui.update_status, f"T '{target_name}': Optuna...", "neutral")
                        if _main_root and _main_root.winfo_exists(): gui.update_gui_safe(gui.set_progress_bar_indeterminate, True)
                        try:
                            study = optuna.create_study(direction=target_config.get('optuna_direction', 'maximize'), pruner=optuna.pruners.MedianPruner(n_startup_trials=target_config.get('optuna_pruner_n_startup_trials', 5), n_warmup_steps=max(1, target_config.get('optuna_cv_folds', 3) // 2), interval_steps=1))
                            study.optimize(functools.partial(optuna_objective_on_subset, X_train_obj_param=X_train_final_df.values, y_train_obj_param=y_train_final_np, feature_names_obj_list_param=feature_names_current, current_target_config_param=target_config), n_trials=target_config.get('optuna_n_trials', 100), timeout=target_config.get('optuna_timeout', None), n_jobs=1, show_progress_bar=True) # n_jobs=1 for Optuna
                            best_params_from_optuna = study.best_params
                            print(f"    Optuna 完成 for {target_name}. Best Value: {study.best_value:.4f}. Params: {best_params_from_optuna}")
                            optuna_completed_successfully = True
                            if "optuna" not in model_suffix_parts_for_file: model_suffix_parts_for_file.append("optuna")
                        except Exception as e_opt: print(f"    !!! ({target_name}) Optuna 出错: {e_opt}"); optuna_completed_successfully = False
                        finally:
                            if _main_root and _main_root.winfo_exists(): gui.update_gui_safe(gui.set_progress_bar_indeterminate, False)

                # --- 步骤 7: 准备最终LGBM参数 ---
                # ... (Logic as before, getting final_lgbm_params_cleaned_for_runs) ...
                final_lgbm_params_to_use_for_runs = {}
                if use_optuna_cfg and optuna_completed_successfully and best_params_from_optuna: final_lgbm_params_to_use_for_runs = best_params_from_optuna.copy(); print(f"    将使用 Optuna 找到的最佳参数 for {target_name}。")
                else:
                    print(f"    Optuna 未运行或失败 for {target_name}, 将从配置加载LGBM参数。")
                    lgbm_keys_cfg_run = ['learning_rate','num_leaves','max_depth','reg_alpha','reg_lambda', 'colsample_bytree','subsample','min_child_samples','subsample_freq']
                    for k_run in lgbm_keys_cfg_run:
                        if k_run in target_config: final_lgbm_params_to_use_for_runs[k_run] = target_config[k_run]
                base_lgbm_params_for_runs = {'objective': target_config.get('objective', 'binary'), 'metric': target_config.get('metric', 'binary_logloss'), 'boosting_type': target_config.get('boosting_type', 'gbdt'), 'device_type': target_config.get('device_type', 'gpu'), 'n_jobs': -1, 'n_estimators': target_config.get('n_estimators', 5000), 'random_state': target_config.get('random_state', 42), 'verbosity': -1}
                verbose_cfg = target_config.get('verbose', 1)
                if verbose_cfg == 0: base_lgbm_params_for_runs['verbosity'] = -1
                elif verbose_cfg == 1: base_lgbm_params_for_runs['verbosity'] = 0
                elif verbose_cfg > 1: base_lgbm_params_for_runs['verbosity'] = 1
                final_lgbm_params_cleaned_for_runs = {**base_lgbm_params_for_runs, **final_lgbm_params_to_use_for_runs}
                final_lgbm_params_cleaned_for_runs = {k: v for k,v in final_lgbm_params_cleaned_for_runs.items() if k in VALID_LGBM_PARAM_KEYS}
                print(f"    最终LGBM参数 (用于Fold/Run) for {target_name}: {json.dumps(final_lgbm_params_cleaned_for_runs, indent=2)}")
                final_model_suffix_str_for_files = ("_" + "_".join(sorted(list(set(model_suffix_parts_for_file))))) if model_suffix_parts_for_file else ""
             
                # --- 步骤 8: 基于 TimeSeriesSplit 的多折集成训练或单模型训练 ---
                print(f"\n  步骤 8 ({target_name}): 基于 TimeSeriesSplit 或单模型训练与评估...")
                n_cv_folds_for_ensemble = int(target_config.get('ensemble_cv_folds', 1))
                min_samples_for_cv = n_cv_folds_for_ensemble + 1 if n_cv_folds_for_ensemble > 0 else 2

                if n_cv_folds_for_ensemble > 1 and len(X_train_final_np) >= min_samples_for_cv:
                    # ... (Multi-fold training logic as before) ...
                    print(f"    ({target_name}): 将使用 TimeSeriesSplit ({n_cv_folds_for_ensemble} 折) 进行集成训练。")
                    tscv_ensemble = TimeSeriesSplit(n_splits=n_cv_folds_for_ensemble)
                    for fold_num, (train_indices, val_indices) in enumerate(tscv_ensemble.split(X_train_final_np, y_train_final_np)):
                        fold_log_prefix = f"    --- Fold {fold_num + 1}/{n_cv_folds_for_ensemble} (Target: {target_name})"
                        print(f"\n{fold_log_prefix} ---")
                        current_fold_lgbm_params = final_lgbm_params_cleaned_for_runs.copy()
                        current_fold_lgbm_params['random_state'] = final_lgbm_params_cleaned_for_runs.get('random_state', 42) + fold_num * 101
                        X_train_this_fold_np = X_train_final_np[train_indices]; y_train_this_fold_np = y_train_final_np[train_indices]
                        X_val_this_fold_np = X_train_final_np[val_indices]; y_val_this_fold_np = y_train_final_np[val_indices]
                        fold_eval_summary_this_fold = {"fold_index": fold_num, "model_type": "LGBM_Fold"}
                        current_fold_model = LGBMClassifier(**{k:v for k,v in current_fold_lgbm_params.items() if k in VALID_LGBM_PARAM_KEYS})
                        eval_set_for_fold_fit, callbacks_for_fold_fit = [], []
                        if len(X_val_this_fold_np) > 0 and len(np.unique(y_val_this_fold_np)) > 1:
                            eval_set_for_fold_fit.append((X_val_this_fold_np, y_val_this_fold_np))
                            esr_fold = target_config.get('early_stopping_rounds', 200)
                            if esr_fold > 0: callbacks_for_fold_fit.append(lgb.early_stopping(esr_fold, verbose=False, first_metric_only=False))
                        else: print(f"      {fold_log_prefix}: 内部验证集无效，不用于早停。")
                        if len(X_train_this_fold_np) > 0: eval_set_for_fold_fit.insert(0, (X_train_this_fold_np, y_train_this_fold_np))
                        else: print(f"      !!! {fold_log_prefix}: 当前fold训练集为空，跳过此fold训练。"); all_folds_eval_details.append({"fold_index": fold_num, "status": "Skipped - Empty Train Set"}); continue

                        # 应用SMOTE过采样 (仅在训练数据上)
                        smote_enabled_fold = getattr(config, 'SMOTE_GLOBAL_ENABLE', True) and target_config.get('smote_enable', True)
                        if smote_enabled_fold:
                            unique_labels_fold, counts_fold = np.unique(y_train_this_fold_np, return_counts=True)
                            smote_min_threshold_fold = target_config.get('smote_min_samples_threshold', getattr(config, 'SMOTE_MIN_SAMPLES_THRESHOLD', 5))

                            # 确定目标类别：对于UP模型，目标类别是"明确上涨"（标签1）
                            target_variable_type_fold = target_config.get('target_variable_type', 'BOTH').upper()
                            if target_variable_type_fold == "UP_ONLY":
                                # 对于UP模型，我们总是对"明确上涨"类别（标签1）进行SMOTE
                                target_class_for_smote_fold = 1
                                target_class_name_fold = "明确上涨"
                            elif target_variable_type_fold == "DOWN_ONLY":
                                # 对于DOWN模型，我们总是对"明确下跌"类别（标签1）进行SMOTE
                                target_class_for_smote_fold = 1
                                target_class_name_fold = "明确下跌"
                            else:
                                # 对于其他情况，使用传统的少数类逻辑
                                minority_class_idx_fold = np.argmin(counts_fold)
                                target_class_for_smote_fold = unique_labels_fold[minority_class_idx_fold]
                                target_class_name_fold = f"少数类(标签{target_class_for_smote_fold})"

                            # 检查目标类别的样本数量
                            if target_class_for_smote_fold in unique_labels_fold:
                                target_class_idx_fold = np.where(unique_labels_fold == target_class_for_smote_fold)[0][0]
                                target_class_count_fold = counts_fold[target_class_idx_fold]

                                if target_class_count_fold < smote_min_threshold_fold:
                                    print(f"      {fold_log_prefix}: {target_class_name_fold}样本 ({target_class_count_fold}) 过少，跳过SMOTE。")
                                    X_train_this_fold_resampled_np = X_train_this_fold_np
                                    y_train_this_fold_resampled_np = y_train_this_fold_np
                                else:
                                    try:
                                        print(f"      {fold_log_prefix}: 应用SMOTE前类别分布: {dict(zip(unique_labels_fold, counts_fold))}")
                                        print(f"      {fold_log_prefix}: 对{target_class_name_fold}(标签{target_class_for_smote_fold})进行SMOTE过采样")
                                        # k_neighbors 的值不能超过目标类别样本数 - 1
                                        smote_k_neighbors_config_fold = target_config.get('smote_k_neighbors', getattr(config, 'SMOTE_DEFAULT_K_NEIGHBORS', 4))
                                        smote_k_neighbors_fold = min(smote_k_neighbors_config_fold, target_class_count_fold - 1) if target_class_count_fold > 1 else 1
                                        smote_random_state_fold = target_config.get('smote_random_state', getattr(config, 'SMOTE_RANDOM_STATE', 42))
                                        sm_fold = SMOTE(random_state=smote_random_state_fold, k_neighbors=smote_k_neighbors_fold)
                                        X_train_this_fold_resampled_np, y_train_this_fold_resampled_np = sm_fold.fit_resample(X_train_this_fold_np, y_train_this_fold_np)
                                        unique_labels_after_fold, counts_after_fold = np.unique(y_train_this_fold_resampled_np, return_counts=True)
                                        print(f"      {fold_log_prefix}: 应用SMOTE后类别分布: {dict(zip(unique_labels_after_fold, counts_after_fold))}")
                                    except ValueError as e_smote_fold:
                                        print(f"      !!! {fold_log_prefix}: SMOTE执行失败: {e_smote_fold}。将使用原始训练数据。")
                                        X_train_this_fold_resampled_np = X_train_this_fold_np
                                        y_train_this_fold_resampled_np = y_train_this_fold_np
                            else:
                                print(f"      {fold_log_prefix}: 目标类别 {target_class_for_smote_fold} 在当前折中不存在，跳过SMOTE。")
                                X_train_this_fold_resampled_np = X_train_this_fold_np
                                y_train_this_fold_resampled_np = y_train_this_fold_np
                        else:
                            print(f"      {fold_log_prefix}: SMOTE已禁用，使用原始训练数据。")
                            X_train_this_fold_resampled_np = X_train_this_fold_np
                            y_train_this_fold_resampled_np = y_train_this_fold_np

                        # 更新eval_set以使用重采样后的训练数据
                        eval_set_for_fold_fit = []
                        if len(X_train_this_fold_resampled_np) > 0: eval_set_for_fold_fit.insert(0, (X_train_this_fold_resampled_np, y_train_this_fold_resampled_np))
                        if len(X_val_this_fold_np) > 0 and len(np.unique(y_val_this_fold_np)) > 1:
                            eval_set_for_fold_fit.append((X_val_this_fold_np, y_val_this_fold_np))

                        print(f"      {fold_log_prefix}: 开始训练模型... (Train size: {len(X_train_this_fold_resampled_np)}, Val size: {len(X_val_this_fold_np)})")
                        current_fold_model.fit(X_train_this_fold_resampled_np, y_train_this_fold_resampled_np, eval_set=eval_set_for_fold_fit, eval_names=['train_fold', 'valid_fold'] if len(eval_set_for_fold_fit) > 1 else ['train_fold'], eval_metric=current_fold_lgbm_params.get('metric'), callbacks=callbacks_for_fold_fit, feature_name=feature_names_current if feature_names_current else 'auto')
                        print(f"      {fold_log_prefix}: 训练完成. Best Iter: {getattr(current_fold_model, 'best_iteration_', 'N/A')}")
                        fold_eval_summary_this_fold['best_iteration_raw'] = getattr(current_fold_model, 'best_iteration_', None)
                        if len(X_train_this_fold_np) > 0:
                            X_train_this_fold_df_for_eval = pd.DataFrame(X_train_this_fold_np, columns=feature_names_current)
                            y_pred_train_fold_raw = current_fold_model.predict(X_train_this_fold_df_for_eval)
                            y_proba_train_fold_raw_cls1 = current_fold_model.predict_proba(X_train_this_fold_df_for_eval)[:,1]
                            fold_eval_summary_this_fold['raw_train_accuracy'] = accuracy_score(y_train_this_fold_np, y_pred_train_fold_raw)
                            fold_eval_summary_this_fold['raw_train_brier'] = brier_score_loss(y_train_this_fold_np, y_proba_train_fold_raw_cls1)
                        if len(X_val_this_fold_np) > 0 and len(np.unique(y_val_this_fold_np)) > 1:
                            X_val_this_fold_df_for_eval = pd.DataFrame(X_val_this_fold_np, columns=feature_names_current)
                            y_proba_val_fold_raw_cls1 = current_fold_model.predict_proba(X_val_this_fold_df_for_eval)[:,1]
                            fold_eval_summary_this_fold['raw_internal_val_brier'] = brier_score_loss(y_val_this_fold_np, y_proba_val_fold_raw_cls1)
                        fold_artifact_this_fold_dict_local = {} 
                        raw_model_fn_fold_local = f"model_{target_name_for_file}_{minutes_display_for_file}m_fold{fold_num}{final_model_suffix_str_for_files}.joblib"
                        joblib.dump(current_fold_model, os.path.join(model_dir, raw_model_fn_fold_local), compress=3)
                        print(f"      {fold_log_prefix}: 原始模型已保存: {raw_model_fn_fold_local}")
                        fold_artifact_this_fold_dict_local["model_filename"] = raw_model_fn_fold_local

                        if fold_num == 0 and target_config.get('enable_shap_analysis', False) and shap and plt:
                            # ... (SHAP logic as before) ...
                            print(f"      {fold_log_prefix}: 开始SHAP分析 (仅首个Fold)...")
                            if X_train_final_df is None or X_train_final_df.empty: print(f"        SHAP背景数据X_train_final_df为空，跳过。")
                            else:
                                try:
                                    shap_explainer_fold0_local = shap.TreeExplainer(current_fold_model, data=X_train_final_df)
                                    shap_explainer_fn_fold0_local = f"shap_explainer_{target_name_for_file}_{minutes_display_for_file}m_fold{fold_num}{final_model_suffix_str_for_files}.joblib"
                                    joblib.dump(shap_explainer_fold0_local, os.path.join(model_dir, shap_explainer_fn_fold0_local), compress=3)
                                    print(f"        SHAP解释器 (Fold {fold_num}) 已保存: {shap_explainer_fn_fold0_local}")
                                    fold_artifact_this_fold_dict_local["shap_explainer_filename"] = shap_explainer_fn_fold0_local
                                    df_for_shap_plot_fold0_local = None; plot_suffix_fold0_local = "unknown"
                                    if X_val_unscaled_df_selected is not None and not X_val_unscaled_df_selected.empty: df_for_shap_plot_fold0_local = X_val_unscaled_df_selected; plot_suffix_fold0_local = "val"
                                    elif X_test_unscaled_df_selected is not None and not X_test_unscaled_df_selected.empty: df_for_shap_plot_fold0_local = X_test_unscaled_df_selected; plot_suffix_fold0_local = "test"
                                    if df_for_shap_plot_fold0_local is not None and plt and not df_for_shap_plot_fold0_local.empty and feature_names_current:
                                        # 使用视图而非副本，因为SHAP分析不会修改数据内容
                                        df_for_shap_plot_fold0_aligned_local = df_for_shap_plot_fold0_local[feature_names_current]
                                        shap_values_plot_fold0_local = shap_explainer_fold0_local(df_for_shap_plot_fold0_aligned_local)
                                        
                                        # 生成条形图 (原有功能)
                                        plt.figure(); shap.summary_plot(shap_values_plot_fold0_local, df_for_shap_plot_fold0_aligned_local, plot_type="bar", show=False, max_display=30)
                                        plot_fn_bar_fold0_local = f"shap_bar_{target_name_for_file}_{minutes_display_for_file}m_fold{fold_num}_{plot_suffix_fold0_local}{final_model_suffix_str_for_files}.png"
                                        plt.savefig(os.path.join(model_dir, plot_fn_bar_fold0_local), bbox_inches='tight'); plt.close()
                                        print(f"        SHAP bar plot (Fold {fold_num}, data: {plot_suffix_fold0_local}) 已保存: {plot_fn_bar_fold0_local}")
                                        
                                        # 生成散点摘要图 (默认类型)
                                        plt.figure(); shap.summary_plot(shap_values_plot_fold0_local, df_for_shap_plot_fold0_aligned_local, show=False, max_display=30)
                                        plot_fn_summary_fold0_local = f"shap_summary_{target_name_for_file}_{minutes_display_for_file}m_fold{fold_num}_{plot_suffix_fold0_local}{final_model_suffix_str_for_files}.png"
                                        plt.savefig(os.path.join(model_dir, plot_fn_summary_fold0_local), bbox_inches='tight'); plt.close()
                                        print(f"        SHAP summary plot (Fold {fold_num}, data: {plot_suffix_fold0_local}) 已保存: {plot_fn_summary_fold0_local}")
                                        
                                        # 生成特征依赖图 (前5个最重要的特征)
                                        try:
                                            # 获取特征重要性排序
                                            feature_importance = np.abs(shap_values_plot_fold0_local.values).mean(0)
                                            top_features_indices = np.argsort(feature_importance)[::-1][:5]  # 前5个最重要特征
                                            
                                            for i, feat_idx in enumerate(top_features_indices):
                                                feat_name = feature_names_current[feat_idx]
                                                plt.figure()
                                                shap.dependence_plot(feat_idx, shap_values_plot_fold0_local.values, df_for_shap_plot_fold0_aligned_local, 
                                                                   feature_names=feature_names_current, show=False)
                                                plot_fn_dep_fold0_local = f"shap_dependence_{feat_name}_{target_name_for_file}_{minutes_display_for_file}m_fold{fold_num}_{plot_suffix_fold0_local}{final_model_suffix_str_for_files}.png"
                                                plt.savefig(os.path.join(model_dir, plot_fn_dep_fold0_local), bbox_inches='tight'); plt.close()
                                                print(f"        SHAP dependence plot for '{feat_name}' (Fold {fold_num}, data: {plot_suffix_fold0_local}) 已保存: {plot_fn_dep_fold0_local}")
                                        except Exception as e_dep_plot:
                                            print(f"        警告: 生成特征依赖图时出错: {e_dep_plot}")
                                except Exception as e_shap_fold0_local_inner: print(f"      !!! {fold_log_prefix} SHAP出错: {e_shap_fold0_local_inner}")
                        current_calibrated_model_obj_this_fold_local = None # 初始化

                        if target_config.get('enable_probability_calibration', False) and \
                           X_val_this_fold_np is not None and len(X_val_this_fold_np) > 0 and \
                           y_val_this_fold_np is not None and len(y_val_this_fold_np) > 0 and \
                           len(np.unique(y_val_this_fold_np)) > 1:
                            try:
                                print(f"      {fold_log_prefix}: 开始概率校准...")
                                # --- 确保这里的DataFrame变量名与后续使用的一致 ---
                                # 使用 X_val_this_fold_np (这是当前fold的验证集NumPy数据)
                                # 和 feature_names_current (这是当前目标使用的特征名列表)
                                X_val_this_fold_df_for_calib = pd.DataFrame(X_val_this_fold_np, columns=feature_names_current)
                                # --- 修改结束：确保变量名是 X_val_this_fold_df_for_calib ---

                                raw_brier_on_calib_val = fold_eval_summary_this_fold.get('raw_internal_val_brier', None)
                                if raw_brier_on_calib_val is None: 
                                     # 如果之前没有计算，现在用DataFrame计算
                                     y_proba_orig_fold_val_calib_local = current_fold_model.predict_proba(X_val_this_fold_df_for_calib)[:, 1] # 使用DataFrame
                                     raw_brier_on_calib_val = brier_score_loss(y_val_this_fold_np, y_proba_orig_fold_val_calib_local)
                                     fold_eval_summary_this_fold['raw_internal_val_brier'] = raw_brier_on_calib_val 
                                print(f"        原始模型Fold内部验证集 Brier (Fold {fold_num}): {raw_brier_on_calib_val:.4f}")
                                
                                temp_cal_cand_this_fold_local = CalibratedClassifierCV(current_fold_model, method='sigmoid', cv='prefit', n_jobs=-1)
                                # 使用创建的DataFrame进行拟合
                                temp_cal_cand_this_fold_local.fit(X_val_this_fold_df_for_calib, y_val_this_fold_np)
                                
                                # --- 使用相同的DataFrame变量名进行预测 ---
                                y_proba_cal_fold_val_calib_local = temp_cal_cand_this_fold_local.predict_proba(X_val_this_fold_df_for_calib)[:, 1]
                                # --- 修改结束 ---
                                
                                brier_cal_fold_val_calib_local = brier_score_loss(y_val_this_fold_np, y_proba_cal_fold_val_calib_local)
                                fold_eval_summary_this_fold['calibrated_internal_val_brier'] = brier_cal_fold_val_calib_local
                                print(f"        校准后模型Fold内部验证集 Brier (Fold {fold_num}): {brier_cal_fold_val_calib_local:.4f}")
                                
                                cal_impr_thr_fold_local = target_config.get('calibration_brier_improvement_threshold', 0.0001)
                                if brier_cal_fold_val_calib_local < (raw_brier_on_calib_val - cal_impr_thr_fold_local):
                                    print(f"        校准有效 (Fold {fold_num})。")
                                    current_calibrated_model_obj_this_fold_local = temp_cal_cand_this_fold_local 
                                    calibrated_model_fn_this_fold_local = f"model_{target_name_for_file}_{minutes_display_for_file}m_fold{fold_num}{final_model_suffix_str_for_files}_calibrated.joblib"
                                    joblib.dump(temp_cal_cand_this_fold_local, os.path.join(model_dir, calibrated_model_fn_this_fold_local), compress=3)
                                    print(f"        校准模型 (Fold {fold_num}) 已保存: {calibrated_model_fn_this_fold_local}")
                                    fold_artifact_this_fold_dict_local["calibrated_model_filename"] = calibrated_model_fn_this_fold_local
                                    fold_eval_summary_this_fold['calibration_selected'] = True
                                else:
                                    print(f"        校准未显著改善或效果变差 (Fold {fold_num})。将使用原始模型进行测试评估。")
                                    fold_eval_summary_this_fold['calibration_selected'] = False
                            except Exception as e_cal_fold_inner_local: 
                                print(f"      !!!     --- Fold {fold_num + 1}/5 (Target: {target_name}) 校准过程出错: {e_cal_fold_inner_local}") # 修正日志打印的目标名和fold编号
                                traceback.print_exc(limit=1) # 打印更详细的错误
                                fold_eval_summary_this_fold['calibration_selected'] = 'Error'


                        fold_model_artifacts_list.append(fold_artifact_this_fold_dict_local)
                        model_to_test_on_global_test_fold = current_calibrated_model_obj_this_fold_local if current_calibrated_model_obj_this_fold_local else current_fold_model

                        # 🎯 为当前fold进行最优阈值寻优 (移动到概率校准之后，确保model_to_test_on_global_test_fold已正确赋值)
                        threshold_optimization_enabled_fold = getattr(config, 'THRESHOLD_OPTIMIZATION_ENABLE', True) and target_config.get('threshold_optimization_enable', True)
                        if threshold_optimization_enabled_fold and X_val_this_fold_np is not None and len(X_val_this_fold_np) > 0 and y_val_this_fold_np is not None and len(y_val_this_fold_np) > 0 and len(np.unique(y_val_this_fold_np)) > 1:
                            try:
                                print(f"      {fold_log_prefix}: 开始当前fold最优阈值寻优...")
                                threshold_method_fold = target_config.get('threshold_optimization_method', getattr(config, 'THRESHOLD_OPTIMIZATION_METHOD', 'f1'))

                                # 🎯 获取精确率约束参数
                                min_precision_constraint_fold = target_config.get('threshold_min_precision', 0.65)

                                # 使用当前fold的验证集进行阈值优化
                                X_val_this_fold_df_for_threshold = pd.DataFrame(X_val_this_fold_np, columns=feature_names_current)
                                y_proba_val_fold_for_threshold = model_to_test_on_global_test_fold.predict_proba(X_val_this_fold_df_for_threshold)[:, 1]

                                # 使用data_utils中的find_optimal_threshold函数
                                threshold_result_fold = data_utils.find_optimal_threshold(
                                    y_val_this_fold_np,
                                    y_proba_val_fold_for_threshold,
                                    target_name=target_name + f"_fold{fold_num}",
                                    method=threshold_method_fold,
                                    verbose=True,
                                    min_precision=min_precision_constraint_fold
                                )

                                if threshold_result_fold:
                                    optimal_threshold_fold = threshold_result_fold['optimal_threshold']
                                    fold_eval_summary_this_fold['optimal_threshold'] = optimal_threshold_fold
                                    fold_eval_summary_this_fold['threshold_f1_score'] = threshold_result_fold['f1_score']
                                    fold_eval_summary_this_fold['threshold_precision'] = threshold_result_fold['precision']
                                    fold_eval_summary_this_fold['threshold_recall'] = threshold_result_fold['recall']
                                    fold_eval_summary_this_fold['threshold_accuracy'] = threshold_result_fold['accuracy']
                                    fold_eval_summary_this_fold['threshold_method'] = threshold_method_fold

                                    print(f"        Fold {fold_num} 最优阈值: {optimal_threshold_fold:.4f} (方法: {threshold_method_fold})")
                                    print(f"        Fold {fold_num} 阈值性能: F1={threshold_result_fold['f1_score']:.4f}, "
                                          f"Precision={threshold_result_fold['precision']:.4f}, "
                                          f"Recall={threshold_result_fold['recall']:.4f}")
                                else:
                                    print(f"        Fold {fold_num} 阈值寻优失败，将使用默认阈值")
                                    fold_eval_summary_this_fold['optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))

                            except Exception as e_threshold_fold:
                                print(f"      !!! {fold_log_prefix} 阈值寻优出错: {e_threshold_fold}")
                                fold_eval_summary_this_fold['optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                        else:
                            print(f"      {fold_log_prefix}: 阈值寻优已禁用或验证集无效")
                            fold_eval_summary_this_fold['optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                        eval_desc_fold_on_test_local = f"Fold {fold_num} ({'Calibrated' if current_calibrated_model_obj_this_fold_local else 'Raw'})"
                        fold_eval_summary_this_fold['test_model_type'] = 'Calibrated' if current_calibrated_model_obj_this_fold_local else 'Raw'
                        print(f"      {fold_log_prefix}: 开始测试集评估 ({eval_desc_fold_on_test_local})...")
                        if X_test_final_np_scaled is not None and len(X_test_final_np_scaled) > 0 and y_test_final_np is not None and len(y_test_final_np) > 0 and len(np.unique(y_test_final_np)) > 1:
                            try:
                                X_test_final_df_for_eval_pred_fold = pd.DataFrame(X_test_final_np_scaled, columns=feature_names_current)

                                # 🎯 使用最优阈值进行测试集评估
                                # 获取当前fold的最优阈值
                                fold_optimal_threshold = fold_eval_summary_this_fold.get('optimal_threshold', 0.5)

                                # 使用最优阈值进行预测
                                y_pred_test_this_fold_global_eval = data_utils.predict_with_optimal_threshold(
                                    model_to_test_on_global_test_fold,
                                    X_test_final_df_for_eval_pred_fold,
                                    fold_optimal_threshold
                                )

                                y_proba_test_this_fold_global_cls1_eval = model_to_test_on_global_test_fold.predict_proba(X_test_final_df_for_eval_pred_fold)[:,1]
                                fold_eval_summary_this_fold['test_accuracy'] = accuracy_score(y_test_final_np, y_pred_test_this_fold_global_eval)
                                fold_eval_summary_this_fold['test_brier'] = brier_score_loss(y_test_final_np, y_proba_test_this_fold_global_cls1_eval)

                                # 生成使用最优阈值的分类报告
                                report_dict_test_fold = classification_report(y_test_final_np, y_pred_test_this_fold_global_eval, output_dict=True, zero_division=0, target_names=['下跌 (0)', '上涨 (1)'])
                                fold_eval_summary_this_fold['test_classification_report'] = report_dict_test_fold
                                fold_eval_summary_this_fold['test_optimal_threshold_used'] = fold_optimal_threshold

                                print(f"      {fold_log_prefix} 测试集评估使用最优阈值: {fold_optimal_threshold:.4f}")
                            except Exception as e_eval_fold_on_test_inner_local: print(f"      !!! {fold_log_prefix} 测试集评估计算出错: {e_eval_fold_on_test_inner_local}"); fold_eval_summary_this_fold['test_accuracy'] = 'Error_Eval'
                        else: fold_eval_summary_this_fold['test_accuracy'] = 'Skipped_NoTestData'
                        all_folds_eval_details.append(fold_eval_summary_this_fold)
                        if _main_root and _main_root.winfo_exists(): gui.update_gui_safe(gui.update_progress_bar, fold_num + 1, n_cv_folds_for_ensemble)

                # --- 多折集成模型的最优阈值寻优 (方法A: 收集所有验证折的预测概率) ---
                if n_cv_folds_for_ensemble > 1 and len(X_train_final_np) >= min_samples_for_cv:
                    threshold_optimization_enabled_ensemble = getattr(config, 'THRESHOLD_OPTIMIZATION_ENABLE', True) and target_config.get('threshold_optimization_enable', True)
                    if threshold_optimization_enabled_ensemble:
                        try:
                            print(f"    ({target_name}): 开始多折集成模型的最优阈值寻优...")

                            # 收集所有验证折的真实标签和预测概率
                            all_val_y_true = []
                            all_val_y_proba = []

                            # 重新进行交叉验证以收集验证集预测概率
                            tscv_for_threshold = TimeSeriesSplit(n_splits=n_cv_folds_for_ensemble)
                            fold_models_for_threshold = []

                            # 加载已保存的模型
                            for fold_num_th in range(n_cv_folds_for_ensemble):
                                fold_artifact_th = fold_model_artifacts_list[fold_num_th]

                                # 优先使用校准模型，如果没有则使用原始模型
                                if fold_artifact_th.get("calibrated_model_filename"):
                                    model_filename_th = fold_artifact_th["calibrated_model_filename"]
                                else:
                                    model_filename_th = fold_artifact_th["model_filename"]

                                model_path_th = os.path.join(model_dir, model_filename_th)
                                if os.path.exists(model_path_th):
                                    fold_model_th = joblib.load(model_path_th)
                                    fold_models_for_threshold.append(fold_model_th)
                                else:
                                    print(f"        警告: 无法找到Fold {fold_num_th}的模型文件: {model_filename_th}")
                                    fold_models_for_threshold.append(None)

                            # 对每个验证折进行预测
                            for fold_idx_th, (train_idx_th, val_idx_th) in enumerate(tscv_for_threshold.split(X_train_final_np)):
                                if fold_idx_th >= len(fold_models_for_threshold) or fold_models_for_threshold[fold_idx_th] is None:
                                    continue

                                X_val_fold_th = X_train_final_np[val_idx_th]
                                y_val_fold_th = y_train_final_np[val_idx_th]

                                # 缩放验证集数据
                                X_val_fold_scaled_th = fitted_scaler.transform(X_val_fold_th)

                                # 使用对应的fold模型进行预测
                                X_val_fold_df_th = pd.DataFrame(X_val_fold_scaled_th, columns=feature_names_current)
                                y_proba_val_fold_th = fold_models_for_threshold[fold_idx_th].predict_proba(X_val_fold_df_th)[:, 1]

                                all_val_y_true.extend(y_val_fold_th)
                                all_val_y_proba.extend(y_proba_val_fold_th)

                            if len(all_val_y_true) > 0 and len(all_val_y_proba) > 0:
                                threshold_method_ensemble = target_config.get('threshold_optimization_method', getattr(config, 'THRESHOLD_OPTIMIZATION_METHOD', 'f1'))

                                # 🎯 获取精确率约束参数
                                min_precision_constraint = target_config.get('threshold_min_precision', 0.65)

                                # 使用data_utils中的find_optimal_threshold函数
                                threshold_result_ensemble = data_utils.find_optimal_threshold(
                                    all_val_y_true,
                                    all_val_y_proba,
                                    target_name=target_name + "_ensemble",
                                    method=threshold_method_ensemble,
                                    verbose=True,
                                    min_precision=min_precision_constraint
                                )

                                if threshold_result_ensemble:
                                    optimal_threshold_ensemble = threshold_result_ensemble['optimal_threshold']

                                    # 将阈值信息添加到第一个fold的评估摘要中（代表整个集成）
                                    if all_folds_eval_details:
                                        all_folds_eval_details[0]['ensemble_optimal_threshold'] = optimal_threshold_ensemble
                                        all_folds_eval_details[0]['ensemble_threshold_f1_score'] = threshold_result_ensemble['f1_score']
                                        all_folds_eval_details[0]['ensemble_threshold_precision'] = threshold_result_ensemble['precision']
                                        all_folds_eval_details[0]['ensemble_threshold_recall'] = threshold_result_ensemble['recall']
                                        all_folds_eval_details[0]['ensemble_threshold_accuracy'] = threshold_result_ensemble['accuracy']
                                        all_folds_eval_details[0]['ensemble_threshold_method'] = threshold_method_ensemble

                                    print(f"        集成模型最优阈值: {optimal_threshold_ensemble:.4f} (方法: {threshold_method_ensemble})")
                                    print(f"        集成阈值性能 [验证集]: F1={threshold_result_ensemble['f1_score']:.4f}, "
                                          f"Precision={threshold_result_ensemble['precision']:.4f}, "
                                          f"Recall={threshold_result_ensemble['recall']:.4f}")

                                    # 🎯 保存集成模型最优阈值到主模型元数据文件
                                    if target_config.get('threshold_save_to_metadata', getattr(config, 'THRESHOLD_SAVE_TO_METADATA', True)):
                                        # 构建主模型元数据文件路径
                                        main_meta_filename = f"model_meta_{target_name_for_file}_{minutes_display_for_file}m.json"
                                        main_meta_path = os.path.join(model_dir, main_meta_filename)

                                        # 保存集成模型最优阈值到主元数据文件
                                        success = data_utils.save_ensemble_threshold_to_main_metadata(
                                            main_meta_path, optimal_threshold_ensemble, target_name
                                        )

                                        if success:
                                            print(f"        ✅ 集成模型最优阈值 {optimal_threshold_ensemble:.4f} 已保存到主元数据文件")
                                        else:
                                            print(f"        ⚠️  集成模型最优阈值保存失败")

                                else:
                                    print(f"        集成模型阈值寻优失败，将使用默认阈值")
                                    if all_folds_eval_details:
                                        all_folds_eval_details[0]['ensemble_optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                            else:
                                print(f"        无法收集到有效的验证集预测数据进行阈值寻优")

                        except Exception as e_threshold_ensemble:
                            print(f"    !!! ({target_name}) 集成模型阈值寻优出错: {e_threshold_ensemble}")
                            if all_folds_eval_details:
                                all_folds_eval_details[0]['ensemble_optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                    else:
                        print(f"    ({target_name}): 集成模型阈值寻优已禁用")
                        if all_folds_eval_details:
                            all_folds_eval_details[0]['ensemble_optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                else:
                    # ... (Single model training logic as before) ...
                    print(f"    ({target_name}): 将进行单模型训练 (ensemble_cv_folds={n_cv_folds_for_ensemble} 或数据不足: {len(X_train_final_np)} < {min_samples_for_cv})。")
                    single_model_log_prefix_main = f"    --- Single Model (Target: {target_name})"
                    single_model_params_main_local = final_lgbm_params_cleaned_for_runs.copy()
                    single_model_main_obj = LGBMClassifier(**{k:v for k,v in single_model_params_main_local.items() if k in VALID_LGBM_PARAM_KEYS})
                    single_model_eval_summary_dict = {"fold_index": -1, "model_type": "LGBM_Single"} 
                    eval_set_single_main_local, callbacks_single_main_local = [], []
                    if len(X_train_final_np) > 0: eval_set_single_main_local.insert(0, (X_train_final_np, y_train_final_np))
                    else: raise ValueError(f"单模型训练失败：训练集为空 for {target_name}")
                    if X_val_final_np_scaled is not None and len(X_val_final_np_scaled) > 0 and y_val_final_np is not None and len(y_val_final_np) > 0 and len(np.unique(y_val_final_np)) > 1:
                        eval_set_single_main_local.append((X_val_final_np_scaled, y_val_final_np))
                        esr_single_main_local = target_config.get('early_stopping_rounds', 200)
                        if esr_single_main_local > 0: callbacks_single_main_local.append(lgb.early_stopping(esr_single_main_local, verbose=target_config.get('verbose',1)>0, first_metric_only=False))
                    else: print(f"      {single_model_log_prefix_main}: 全局验证集无效，不用于早停。")
                    if base_lgbm_params_for_runs['verbosity'] > 0 and target_config.get('verbose', 1) > 1 : callbacks_single_main_local.append(lgb.log_evaluation(period=target_config.get('verbose')))

                    # 应用SMOTE过采样 (仅在训练数据上)
                    smote_enabled_single = getattr(config, 'SMOTE_GLOBAL_ENABLE', True) and target_config.get('smote_enable', True)
                    if smote_enabled_single:
                        unique_labels_single, counts_single = np.unique(y_train_final_np, return_counts=True)
                        smote_min_threshold_single = target_config.get('smote_min_samples_threshold', getattr(config, 'SMOTE_MIN_SAMPLES_THRESHOLD', 5))

                        # 确定目标类别：对于UP模型，目标类别是"明确上涨"（标签1）
                        target_variable_type_single = target_config.get('target_variable_type', 'BOTH').upper()
                        if target_variable_type_single == "UP_ONLY":
                            # 对于UP模型，我们总是对"明确上涨"类别（标签1）进行SMOTE
                            target_class_for_smote_single = 1
                            target_class_name_single = "明确上涨"
                        elif target_variable_type_single == "DOWN_ONLY":
                            # 对于DOWN模型，我们总是对"明确下跌"类别（标签1）进行SMOTE
                            target_class_for_smote_single = 1
                            target_class_name_single = "明确下跌"
                        else:
                            # 对于其他情况，使用传统的少数类逻辑
                            minority_class_idx_single = np.argmin(counts_single)
                            target_class_for_smote_single = unique_labels_single[minority_class_idx_single]
                            target_class_name_single = f"少数类(标签{target_class_for_smote_single})"

                        # 检查目标类别的样本数量
                        if target_class_for_smote_single in unique_labels_single:
                            target_class_idx_single = np.where(unique_labels_single == target_class_for_smote_single)[0][0]
                            target_class_count_single = counts_single[target_class_idx_single]

                            if target_class_count_single < smote_min_threshold_single:
                                print(f"      {single_model_log_prefix_main}: {target_class_name_single}样本 ({target_class_count_single}) 过少，跳过SMOTE。")
                                X_train_final_resampled_np = X_train_final_np
                                y_train_final_resampled_np = y_train_final_np
                            else:
                                try:
                                    print(f"      {single_model_log_prefix_main}: 应用SMOTE前类别分布: {dict(zip(unique_labels_single, counts_single))}")
                                    print(f"      {single_model_log_prefix_main}: 对{target_class_name_single}(标签{target_class_for_smote_single})进行SMOTE过采样")
                                    # k_neighbors 的值不能超过目标类别样本数 - 1
                                    smote_k_neighbors_config_single = target_config.get('smote_k_neighbors', getattr(config, 'SMOTE_DEFAULT_K_NEIGHBORS', 4))
                                    smote_k_neighbors_single = min(smote_k_neighbors_config_single, target_class_count_single - 1) if target_class_count_single > 1 else 1
                                    smote_random_state_single = target_config.get('smote_random_state', getattr(config, 'SMOTE_RANDOM_STATE', 42))
                                    sm_single = SMOTE(random_state=smote_random_state_single, k_neighbors=smote_k_neighbors_single)
                                    X_train_final_resampled_np, y_train_final_resampled_np = sm_single.fit_resample(X_train_final_np, y_train_final_np)
                                    unique_labels_after_single, counts_after_single = np.unique(y_train_final_resampled_np, return_counts=True)
                                    print(f"      {single_model_log_prefix_main}: 应用SMOTE后类别分布: {dict(zip(unique_labels_after_single, counts_after_single))}")
                                except ValueError as e_smote_single:
                                    print(f"      !!! {single_model_log_prefix_main}: SMOTE执行失败: {e_smote_single}。将使用原始训练数据。")
                                    X_train_final_resampled_np = X_train_final_np
                                    y_train_final_resampled_np = y_train_final_np
                        else:
                            print(f"      {single_model_log_prefix_main}: 目标类别 {target_class_for_smote_single} 在当前数据中不存在，跳过SMOTE。")
                            X_train_final_resampled_np = X_train_final_np
                            y_train_final_resampled_np = y_train_final_np
                    else:
                        print(f"      {single_model_log_prefix_main}: SMOTE已禁用，使用原始训练数据。")
                        X_train_final_resampled_np = X_train_final_np
                        y_train_final_resampled_np = y_train_final_np

                    # 更新eval_set以使用重采样后的训练数据
                    eval_set_single_main_local = []
                    if len(X_train_final_resampled_np) > 0: eval_set_single_main_local.insert(0, (X_train_final_resampled_np, y_train_final_resampled_np))
                    else: raise ValueError(f"单模型训练失败：重采样后训练集为空 for {target_name}")
                    if X_val_final_np_scaled is not None and len(X_val_final_np_scaled) > 0 and y_val_final_np is not None and len(y_val_final_np) > 0 and len(np.unique(y_val_final_np)) > 1:
                        eval_set_single_main_local.append((X_val_final_np_scaled, y_val_final_np))
                        esr_single_main_local = target_config.get('early_stopping_rounds', 200)
                        if esr_single_main_local > 0: callbacks_single_main_local.append(lgb.early_stopping(esr_single_main_local, verbose=target_config.get('verbose',1)>0, first_metric_only=False))
                    else: print(f"      {single_model_log_prefix_main}: 全局验证集无效，不用于早停。")

                    print(f"      {single_model_log_prefix_main}: 开始训练模型... (Train size: {len(X_train_final_resampled_np)}, Global Val size: {len(X_val_final_np_scaled) if X_val_final_np_scaled is not None else 0})")
                    single_model_main_obj.fit(X_train_final_resampled_np, y_train_final_resampled_np, eval_set=eval_set_single_main_local, eval_names=['train', 'valid'] if len(eval_set_single_main_local) > 1 else ['train'], eval_metric=single_model_params_main_local.get('metric'), callbacks=callbacks_single_main_local, feature_name=feature_names_current if feature_names_current else 'auto')
                    print(f"      {single_model_log_prefix_main}: 训练完成. Best Iter: {getattr(single_model_main_obj, 'best_iteration_', 'N/A')}")
                    single_model_eval_summary_dict['best_iteration_raw'] = getattr(single_model_main_obj, 'best_iteration_', None)
                    if len(X_train_final_np) > 0:
                        X_train_final_df_for_eval_single_raw = pd.DataFrame(X_train_final_np, columns=feature_names_current)
                        y_pred_train_single_raw = single_model_main_obj.predict(X_train_final_df_for_eval_single_raw)
                        y_proba_train_single_raw_cls1 = single_model_main_obj.predict_proba(X_train_final_df_for_eval_single_raw)[:,1]
                        single_model_eval_summary_dict['raw_train_accuracy'] = accuracy_score(y_train_final_np, y_pred_train_single_raw)
                        single_model_eval_summary_dict['raw_train_brier'] = brier_score_loss(y_train_final_np, y_proba_train_single_raw_cls1)
                    if X_val_final_np_scaled is not None and len(X_val_final_np_scaled) > 0 and len(np.unique(y_val_final_np)) > 1:
                        X_val_final_df_for_eval_single_raw = pd.DataFrame(X_val_final_np_scaled, columns=feature_names_current)
                        y_proba_val_single_raw_cls1 = single_model_main_obj.predict_proba(X_val_final_df_for_eval_single_raw)[:,1]
                        single_model_eval_summary_dict['raw_internal_val_brier'] = brier_score_loss(y_val_final_np, y_proba_val_single_raw_cls1)

                        # --- 最优阈值寻优 ---
                        threshold_optimization_enabled = getattr(config, 'THRESHOLD_OPTIMIZATION_ENABLE', True) and target_config.get('threshold_optimization_enable', True)
                        if threshold_optimization_enabled:
                            try:
                                print(f"      {single_model_log_prefix_main}: 开始最优阈值寻优...")
                                threshold_method = target_config.get('threshold_optimization_method', getattr(config, 'THRESHOLD_OPTIMIZATION_METHOD', 'f1'))

                                # 🎯 获取精确率约束参数
                                min_precision_constraint = target_config.get('threshold_min_precision', 0.65)

                                # 使用data_utils中的find_optimal_threshold函数
                                threshold_result = data_utils.find_optimal_threshold(
                                    y_val_final_np,
                                    y_proba_val_single_raw_cls1,
                                    target_name=target_name,
                                    method=threshold_method,
                                    verbose=True,
                                    min_precision=min_precision_constraint
                                )

                                if threshold_result:
                                    optimal_threshold = threshold_result['optimal_threshold']
                                    single_model_eval_summary_dict['optimal_threshold'] = optimal_threshold
                                    single_model_eval_summary_dict['threshold_f1_score'] = threshold_result['f1_score']
                                    single_model_eval_summary_dict['threshold_precision'] = threshold_result['precision']
                                    single_model_eval_summary_dict['threshold_recall'] = threshold_result['recall']
                                    single_model_eval_summary_dict['threshold_accuracy'] = threshold_result['accuracy']
                                    single_model_eval_summary_dict['threshold_method'] = threshold_method

                                    print(f"        最优阈值: {optimal_threshold:.4f} (方法: {threshold_method})")
                                    print(f"        阈值性能: F1={threshold_result['f1_score']:.4f}, "
                                          f"Precision={threshold_result['precision']:.4f}, "
                                          f"Recall={threshold_result['recall']:.4f}")

                                    # 保存阈值到模型元数据
                                    if target_config.get('threshold_save_to_metadata', getattr(config, 'THRESHOLD_SAVE_TO_METADATA', True)):
                                        model_path = os.path.join(model_dir, raw_model_fn_single_main_local)
                                        data_utils.save_threshold_to_model_metadata(model_path, optimal_threshold, target_name)

                                else:
                                    print(f"        阈值寻优失败，将使用默认阈值")
                                    single_model_eval_summary_dict['optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))

                            except Exception as e_threshold:
                                print(f"      !!! {single_model_log_prefix_main} 阈值寻优出错: {e_threshold}")
                                single_model_eval_summary_dict['optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                        else:
                            print(f"      {single_model_log_prefix_main}: 阈值寻优已禁用")
                            single_model_eval_summary_dict['optimal_threshold'] = target_config.get('threshold_default_value', getattr(config, 'THRESHOLD_DEFAULT_VALUE', 0.5))
                    single_model_artifact_info_dict_local = {}
                    raw_model_fn_single_main_local = f"model_{target_name_for_file}_{minutes_display_for_file}m{final_model_suffix_str_for_files}.joblib"
                    joblib.dump(single_model_main_obj, os.path.join(model_dir, raw_model_fn_single_main_local), compress=3)
                    print(f"      {single_model_log_prefix_main}: 原始模型已保存: {raw_model_fn_single_main_local}")
                    single_model_artifact_info_dict_local["model_filename"] = raw_model_fn_single_main_local
                    if target_config.get('enable_shap_analysis', False) and shap and plt:
                        # ... (SHAP logic as before) ...
                        print(f"      {single_model_log_prefix_main}: 开始SHAP分析...")
                        if X_train_final_df is not None and not X_train_final_df.empty:
                            try:
                                shap_explainer_single_main_local = shap.TreeExplainer(single_model_main_obj, data=X_train_final_df)
                                shap_explainer_fn_single_main_local = f"shap_explainer_{target_name_for_file}_{minutes_display_for_file}m{final_model_suffix_str_for_files}.joblib"
                                joblib.dump(shap_explainer_single_main_local, os.path.join(model_dir, shap_explainer_fn_single_main_local), compress=3)
                                print(f"        SHAP解释器已保存: {shap_explainer_fn_single_main_local}")
                                single_model_artifact_info_dict_local["shap_explainer_filename"] = shap_explainer_fn_single_main_local
                                
                                # 为单模型生成SHAP图表
                                df_for_shap_plot_single_local = None; plot_suffix_single_local = "unknown"
                                if X_val_unscaled_df_selected is not None and not X_val_unscaled_df_selected.empty: 
                                    df_for_shap_plot_single_local = X_val_unscaled_df_selected; plot_suffix_single_local = "val"
                                elif X_test_unscaled_df_selected is not None and not X_test_unscaled_df_selected.empty: 
                                    df_for_shap_plot_single_local = X_test_unscaled_df_selected; plot_suffix_single_local = "test"
                                
                                if df_for_shap_plot_single_local is not None and not df_for_shap_plot_single_local.empty and feature_names_current:
                                    # 使用视图而非副本，因为SHAP分析不会修改数据内容
                                    df_for_shap_plot_single_aligned_local = df_for_shap_plot_single_local[feature_names_current]
                                    shap_values_plot_single_local = shap_explainer_single_main_local(df_for_shap_plot_single_aligned_local)
                                    
                                    # 生成条形图
                                    plt.figure(); shap.summary_plot(shap_values_plot_single_local, df_for_shap_plot_single_aligned_local, plot_type="bar", show=False, max_display=30)
                                    plot_fn_bar_single_local = f"shap_bar_{target_name_for_file}_{minutes_display_for_file}m_{plot_suffix_single_local}{final_model_suffix_str_for_files}.png"
                                    plt.savefig(os.path.join(model_dir, plot_fn_bar_single_local), bbox_inches='tight'); plt.close()
                                    print(f"        SHAP bar plot (Single Model, data: {plot_suffix_single_local}) 已保存: {plot_fn_bar_single_local}")
                                    
                                    # 生成散点摘要图
                                    plt.figure(); shap.summary_plot(shap_values_plot_single_local, df_for_shap_plot_single_aligned_local, show=False, max_display=30)
                                    plot_fn_summary_single_local = f"shap_summary_{target_name_for_file}_{minutes_display_for_file}m_{plot_suffix_single_local}{final_model_suffix_str_for_files}.png"
                                    plt.savefig(os.path.join(model_dir, plot_fn_summary_single_local), bbox_inches='tight'); plt.close()
                                    print(f"        SHAP summary plot (Single Model, data: {plot_suffix_single_local}) 已保存: {plot_fn_summary_single_local}")
                                    
                                    # 生成特征依赖图 (前5个最重要的特征)
                                    try:
                                        # 获取特征重要性排序
                                        feature_importance_single = np.abs(shap_values_plot_single_local.values).mean(0)
                                        top_features_indices_single = np.argsort(feature_importance_single)[::-1][:5]  # 前5个最重要特征
                                        
                                        for i, feat_idx in enumerate(top_features_indices_single):
                                            feat_name = feature_names_current[feat_idx]
                                            plt.figure()
                                            shap.dependence_plot(feat_idx, shap_values_plot_single_local.values, df_for_shap_plot_single_aligned_local, 
                                                               feature_names=feature_names_current, show=False)
                                            plot_fn_dep_single_local = f"shap_dependence_{feat_name}_{target_name_for_file}_{minutes_display_for_file}m_{plot_suffix_single_local}{final_model_suffix_str_for_files}.png"
                                            plt.savefig(os.path.join(model_dir, plot_fn_dep_single_local), bbox_inches='tight'); plt.close()
                                            print(f"        SHAP dependence plot for '{feat_name}' (Single Model, data: {plot_suffix_single_local}) 已保存: {plot_fn_dep_single_local}")
                                    except Exception as e_dep_plot_single:
                                        print(f"        警告: 生成单模型特征依赖图时出错: {e_dep_plot_single}")
                                        
                            except Exception as e_shap_single_main_local_inner: print(f"      !!! {single_model_log_prefix_main} SHAP出错: {e_shap_single_main_local_inner}")
                        else: print(f"        SHAP背景数据X_train_final_df为空，跳过。")


                    current_calibrated_model_obj_single_main_local = None
                    single_model_eval_summary_dict['calibration_selected'] = None # 初始化为None
                    single_model_eval_summary_dict['calibrated_internal_val_brier'] = np.nan # 初始化为NaN

                    if target_config.get('enable_probability_calibration', False) and \
                       X_val_final_np_scaled is not None and len(X_val_final_np_scaled) > 0 and \
                       y_val_final_np is not None and len(y_val_final_np) > 0: # 确保验证数据有效

                        if len(np.unique(y_val_final_np)) < 2:
                            print(f"      !!! {single_model_log_prefix_main}: 跳过概率校准，因为验证集标签少于2个类别 (实际只有 {len(np.unique(y_val_final_np))} 个)。")
                            single_model_eval_summary_dict['calibration_selected'] = 'Skipped_SingleLabelInVal'
                        else:
                            try:
                                print(f"      {single_model_log_prefix_main}: 开始概率校准...")
                                # 从NumPy数组创建DataFrame，不需要额外的copy操作
                                X_val_final_df_for_calib = pd.DataFrame(X_val_final_np_scaled, columns=feature_names_current)

                                raw_brier_on_val_single = single_model_eval_summary_dict.get('raw_internal_val_brier', None)
                                if raw_brier_on_val_single is None or pd.isna(raw_brier_on_val_single):
                                    y_proba_orig_single_val_main_local = single_model_main_obj.predict_proba(X_val_final_df_for_calib)[:, 1]
                                    raw_brier_on_val_single = brier_score_loss(y_val_final_np, y_proba_orig_single_val_main_local)
                                    single_model_eval_summary_dict['raw_internal_val_brier'] = raw_brier_on_val_single
                                print(f"        原始单模型全局验证集 Brier: {raw_brier_on_val_single:.4f}")
                                
                                temp_cal_cand_single_main_local = CalibratedClassifierCV(single_model_main_obj, method='sigmoid', cv='prefit', n_jobs=-1)
                                temp_cal_cand_single_main_local.fit(X_val_final_df_for_calib, y_val_final_np)
                                
                                y_proba_cal_single_val_main_local = temp_cal_cand_single_main_local.predict_proba(X_val_final_df_for_calib)[:, 1]
                                
                                brier_cal_single_val_main_local = brier_score_loss(y_val_final_np, y_proba_cal_single_val_main_local)
                                single_model_eval_summary_dict['calibrated_internal_val_brier'] = brier_cal_single_val_main_local
                                print(f"        校准后单模型全局验证集 Brier: {brier_cal_single_val_main_local:.4f}")
                                
                                cal_impr_thr_single_local = target_config.get('calibration_brier_improvement_threshold', 0.0001)
                                
                                # --- 核心逻辑修改在这里 ---
                                if brier_cal_single_val_main_local < (raw_brier_on_val_single - cal_impr_thr_single_local):
                                    print(f"        校准有效。")
                                    current_calibrated_model_obj_single_main_local = temp_cal_cand_single_main_local
                                    
                                    # 只有在校准有效时才保存校准模型并更新记录
                                    calibrated_model_fn_single_main_local = f"model_{target_name_for_file}_{minutes_display_for_file}m{final_model_suffix_str_for_files}_calibrated.joblib"
                                    joblib.dump(current_calibrated_model_obj_single_main_local, os.path.join(model_dir, calibrated_model_fn_single_main_local), compress=3) # 使用 current_calibrated_model_obj_single_main_local
                                    print(f"        校准模型已保存: {calibrated_model_fn_single_main_local}")
                                    if "single_model_artifact_info_dict_local" in locals(): # 确保字典存在
                                        single_model_artifact_info_dict_local["calibrated_model_filename"] = calibrated_model_fn_single_main_local
                                    single_model_eval_summary_dict['calibration_selected'] = True
                                else: 
                                    print(f"        校准未显著改善或效果变差。将使用原始（未校准）模型。") # 修改日志
                                    single_model_eval_summary_dict['calibration_selected'] = False
                                    # 注意：current_calibrated_model_obj_single_main_local 保持为 None
                                    # single_model_artifact_info_dict_local 中不记录 calibrated_model_filename
                                # --- 修改结束 ---

                            except Exception as e_cal_single_main_local_inner: 
                                print(f"      !!! {single_model_log_prefix_main} 校准过程出错: {e_cal_single_main_local_inner}")
                                traceback.print_exc(limit=1)
                                single_model_eval_summary_dict['calibration_selected'] = 'Error'
                                single_model_eval_summary_dict['calibrated_internal_val_brier'] = np.nan
                    else: 
                        print(f"      !!! {single_model_log_prefix_main}: 跳过概率校准，因为条件未满足 (例如验证数据无效或为空)。")
                        single_model_eval_summary_dict['calibration_selected'] = 'Skipped_PreConditionsNotMet'


                    fold_model_artifacts_list.append(single_model_artifact_info_dict_local)
                    model_to_test_single_glob_main = current_calibrated_model_obj_single_main_local if current_calibrated_model_obj_single_main_local else single_model_main_obj
                    eval_desc_single_test_main_local = f"Single Model ({'Calibrated' if current_calibrated_model_obj_single_main_local else 'Raw'})"
                    single_model_eval_summary_dict['test_model_type'] = 'Calibrated' if current_calibrated_model_obj_single_main_local else 'Raw'
                    print(f"      {single_model_log_prefix_main}: 开始测试集评估 ({eval_desc_single_test_main_local})...")
                    if X_test_final_np_scaled is not None and len(X_test_final_np_scaled) > 0 and y_test_final_np is not None and len(y_test_final_np) > 0 and len(np.unique(y_test_final_np)) > 1:
                        try:
                            # 从NumPy数组创建DataFrame，不需要额外的copy操作
                            X_test_final_df_for_eval_pred_single_main = pd.DataFrame(X_test_final_np_scaled, columns=feature_names_current)

                            # 🎯 使用最优阈值进行单个模型测试集评估
                            # 获取单个模型的最优阈值
                            single_optimal_threshold = single_model_eval_summary_dict.get('optimal_threshold', 0.5)

                            # 使用最优阈值进行预测
                            y_pred_test_single_main_eval = data_utils.predict_with_optimal_threshold(
                                model_to_test_single_glob_main,
                                X_test_final_df_for_eval_pred_single_main,
                                single_optimal_threshold
                            )

                            y_proba_test_single_cls1_main_eval = model_to_test_single_glob_main.predict_proba(X_test_final_df_for_eval_pred_single_main)[:,1]
                            single_model_eval_summary_dict['test_accuracy'] = accuracy_score(y_test_final_np, y_pred_test_single_main_eval)
                            single_model_eval_summary_dict['test_brier'] = brier_score_loss(y_test_final_np, y_proba_test_single_cls1_main_eval)

                            # 生成使用最优阈值的分类报告
                            report_dict_test_single_main = classification_report(y_test_final_np, y_pred_test_single_main_eval, output_dict=True, zero_division=0, target_names=['下跌 (0)', '上涨 (1)'])
                            single_model_eval_summary_dict['test_classification_report'] = report_dict_test_single_main
                            single_model_eval_summary_dict['test_optimal_threshold_used'] = single_optimal_threshold

                            print(f"      {single_model_log_prefix_main} 测试集评估使用最优阈值: {single_optimal_threshold:.4f}")
                        except Exception as e_eval_single_test_main_local_inner: print(f"      !!! {single_model_log_prefix_main} 测试集评估计算出错: {e_eval_single_test_main_local_inner}"); single_model_eval_summary_dict['test_accuracy'] = 'Error_Eval'
                    else: single_model_eval_summary_dict['test_accuracy'] = 'Skipped_NoTestData'
                    all_folds_eval_details.append(single_model_eval_summary_dict)
                    if _main_root and _main_root.winfo_exists(): gui.update_gui_safe(gui.update_progress_bar, 1, 1)



                # --- 统一的评估信息打印和GUI更新 ---
                print(f"\n  --- 目标 '{target_name}' 训练评估总结 ---")
                current_target_gui_metrics_for_display_final = {'status': '评估信息收集中...'} 

                if not all_folds_eval_details:
                    print("    未能收集到任何有效的fold/单模型评估信息。")
                    current_target_gui_metrics_for_display_final = {'status': '训练完成但无评估数据', 'accuracy': 'N/A'}
                else:
                    for i_print, fold_eval_print in enumerate(all_folds_eval_details):
                        fold_id_str_print = f"Fold {fold_eval_print['fold_index']}" if fold_eval_print['fold_index'] != -1 else "SingleModel"
                        print(f"    --- {fold_id_str_print} 评估详情 ---")
                        train_acc_p = fold_eval_print.get('raw_train_accuracy', 'N/A')
                        test_acc_p = fold_eval_print.get('test_accuracy', 'N/A')
                        train_brier_p = fold_eval_print.get('raw_train_brier', 'N/A')
                        test_brier_p = fold_eval_print.get('test_brier', 'N/A')
                        cal_eff_p = fold_eval_print.get('calibration_selected', 'N/A') 
                        val_brier_raw_p = fold_eval_print.get('raw_internal_val_brier', 'N/A')
                        val_brier_cal_p = fold_eval_print.get('calibrated_internal_val_brier', 'N/A')
                        
                        # FIXED: 使用修正后的f-string格式
                        train_acc_str = f"{train_acc_p:.4f}" if isinstance(train_acc_p, float) else str(train_acc_p)
                        test_acc_str = f"{test_acc_p:.4f}" if isinstance(test_acc_p, float) else str(test_acc_p)
                        print(f"      准确率: Train={train_acc_str}, Test={test_acc_str}")

                        train_brier_str = f"{train_brier_p:.4f}" if isinstance(train_brier_p, float) else str(train_brier_p)
                        test_brier_str = f"{test_brier_p:.4f}" if isinstance(test_brier_p, float) else str(test_brier_p)
                        print(f"      Brier  : Train={train_brier_str}, Test={test_brier_str}")
                        
                        val_brier_raw_str = f"{val_brier_raw_p:.4f}" if isinstance(val_brier_raw_p, float) else str(val_brier_raw_p)
                        val_brier_cal_str = f"{val_brier_cal_p:.4f}" if isinstance(val_brier_cal_p, float) else str(val_brier_cal_p)
                        print(f"      校准评估: Val(Raw)={val_brier_raw_str}, Val(Cal)={val_brier_cal_str}, 校准选用={cal_eff_p}")

                        overfit_indicator_p = "未知"
                        if isinstance(train_acc_p, float) and isinstance(test_acc_p, float) and test_acc_p != 'Error_Eval' and test_acc_p != 'Skipped_NoTestData':
                            if train_acc_p > test_acc_p + 0.15 : overfit_indicator_p = "可能过拟合 (Acc显著高于Test)"
                            elif test_acc_p > train_acc_p + 0.05 : overfit_indicator_p = "测试集表现意外好 (Acc)"
                            else: overfit_indicator_p = "Acc表现相对一致"
                        elif isinstance(train_brier_p, float) and isinstance(test_brier_p, float) and test_brier_p != 'Error_Eval' and test_brier_p != 'Skipped_NoTestData':
                             if train_brier_p < test_brier_p - 0.05 : overfit_indicator_p = "可能过拟合 (Brier远低于Test)"
                        print(f"      过拟合判断: {overfit_indicator_p}")
                        if 'test_classification_report' in fold_eval_print and isinstance(fold_eval_print['test_classification_report'], dict):
                             report_to_print = fold_eval_print['test_classification_report']
                             threshold_used = fold_eval_print.get('test_optimal_threshold_used', 0.5)

                             # 🎯 修复阈值显示格式化问题
                             if isinstance(threshold_used, (int, float)):
                                 threshold_display = f"{threshold_used:.4f}"
                             else:
                                 threshold_display = str(threshold_used)

                             print(f"      测试集分类报告 ({fold_id_str_print}) [阈值: {threshold_display}]:")
                             print(f"        下跌(0): P={report_to_print.get('下跌 (0)',{}).get('precision','N/A'):.3f} R={report_to_print.get('下跌 (0)',{}).get('recall','N/A'):.3f} F1={report_to_print.get('下跌 (0)',{}).get('f1-score','N/A'):.3f}")
                             print(f"        上涨(1): P={report_to_print.get('上涨 (1)',{}).get('precision','N/A'):.3f} R={report_to_print.get('上涨 (1)',{}).get('recall','N/A'):.3f} F1={report_to_print.get('上涨 (1)',{}).get('f1-score','N/A'):.3f}")
                             print(f"        Accuracy: {report_to_print.get('accuracy','N/A'):.4f}")

                    # 🎯 添加使用集成模型最优阈值的全局测试集评估
                    if (n_cv_folds_for_ensemble > 1 and len(X_test_final_np_scaled) > 0 and
                        y_test_final_np is not None and len(y_test_final_np) > 0 and
                        len(np.unique(y_test_final_np)) > 1 and fold_model_artifacts_list):

                        print(f"\n      🎯 开始使用集成模型最优阈值进行全局测试集评估...")

                        try:
                            # 加载集成模型最优阈值
                            main_meta_filename = f"model_meta_{target_name_for_file}_{minutes_display_for_file}m.json"
                            main_meta_path = os.path.join(model_dir, main_meta_filename)

                            ensemble_optimal_threshold = data_utils.load_ensemble_threshold_from_main_metadata(
                                main_meta_path, target_name=target_name, default_threshold=0.5
                            )

                            print(f"      📊 使用集成模型最优阈值: {ensemble_optimal_threshold:.4f}")

                            # 使用所有fold模型进行集成预测
                            ensemble_probabilities = []
                            for fold_artifact in fold_model_artifacts_list:
                                # 选择校准模型或原始模型
                                if fold_artifact.get("calibrated_model_filename"):
                                    model_filename = fold_artifact["calibrated_model_filename"]
                                else:
                                    model_filename = fold_artifact["model_filename"]

                                model_path = os.path.join(model_dir, model_filename)
                                if os.path.exists(model_path):
                                    fold_model = joblib.load(model_path)
                                    X_test_df = pd.DataFrame(X_test_final_np_scaled, columns=feature_names_current)
                                    fold_proba = fold_model.predict_proba(X_test_df)[:, 1]
                                    ensemble_probabilities.append(fold_proba)

                            if ensemble_probabilities:
                                # 计算集成概率（平均）
                                ensemble_proba_avg = np.mean(ensemble_probabilities, axis=0)

                                # 使用集成模型最优阈值进行预测
                                ensemble_pred_optimal = (ensemble_proba_avg >= ensemble_optimal_threshold).astype(int)

                                # 计算集成模型评估指标
                                ensemble_accuracy = accuracy_score(y_test_final_np, ensemble_pred_optimal)
                                ensemble_brier = brier_score_loss(y_test_final_np, ensemble_proba_avg)
                                ensemble_report = classification_report(
                                    y_test_final_np, ensemble_pred_optimal,
                                    output_dict=True, zero_division=0,
                                    target_names=['下跌 (0)', '上涨 (1)']
                                )

                                print(f"      📊 集成模型全局测试集性能 [阈值: {ensemble_optimal_threshold:.4f}]:")
                                print(f"         准确率: {ensemble_accuracy:.4f}")
                                print(f"         Brier分数: {ensemble_brier:.4f}")
                                print(f"         下跌(0): P={ensemble_report.get('下跌 (0)',{}).get('precision','N/A'):.3f} R={ensemble_report.get('下跌 (0)',{}).get('recall','N/A'):.3f} F1={ensemble_report.get('下跌 (0)',{}).get('f1-score','N/A'):.3f}")
                                print(f"         上涨(1): P={ensemble_report.get('上涨 (1)',{}).get('precision','N/A'):.3f} R={ensemble_report.get('上涨 (1)',{}).get('recall','N/A'):.3f} F1={ensemble_report.get('上涨 (1)',{}).get('f1-score','N/A'):.3f}")

                                # 更新GUI显示指标为集成模型结果
                                current_target_gui_metrics_for_display_final['accuracy'] = ensemble_accuracy
                                current_target_gui_metrics_for_display_final['brier_score_test'] = ensemble_brier
                                current_target_gui_metrics_for_display_final.update(ensemble_report)
                                current_target_gui_metrics_for_display_final['ensemble_optimal_threshold_used'] = ensemble_optimal_threshold

                                print(f"      ✅ 集成模型全局测试集评估完成")
                            else:
                                print(f"      ⚠️  无法加载fold模型进行集成评估")
                                # 使用第一个fold的结果作为fallback
                                primary_eval_for_gui_display = all_folds_eval_details[0]
                                current_target_gui_metrics_for_display_final['accuracy'] = primary_eval_for_gui_display.get('test_accuracy', 'N/A')
                                current_target_gui_metrics_for_display_final['brier_score_test'] = primary_eval_for_gui_display.get('test_brier', 'N/A')
                                if 'test_classification_report' in primary_eval_for_gui_display and isinstance(primary_eval_for_gui_display['test_classification_report'], dict):
                                    current_target_gui_metrics_for_display_final.update(primary_eval_for_gui_display['test_classification_report'])

                        except Exception as e_ensemble_eval:
                            print(f"      ❌ 集成模型全局测试集评估失败: {e_ensemble_eval}")
                            # 使用第一个fold的结果作为fallback
                            primary_eval_for_gui_display = all_folds_eval_details[0]
                            current_target_gui_metrics_for_display_final['accuracy'] = primary_eval_for_gui_display.get('test_accuracy', 'N/A')
                            current_target_gui_metrics_for_display_final['brier_score_test'] = primary_eval_for_gui_display.get('test_brier', 'N/A')
                            if 'test_classification_report' in primary_eval_for_gui_display and isinstance(primary_eval_for_gui_display['test_classification_report'], dict):
                                current_target_gui_metrics_for_display_final.update(primary_eval_for_gui_display['test_classification_report'])
                    else:
                        # 单模型或无测试数据时，使用第一个fold的结果
                        primary_eval_for_gui_display = all_folds_eval_details[0]
                        current_target_gui_metrics_for_display_final['accuracy'] = primary_eval_for_gui_display.get('test_accuracy', 'N/A')
                        current_target_gui_metrics_for_display_final['brier_score_test'] = primary_eval_for_gui_display.get('test_brier', 'N/A')
                        if 'test_classification_report' in primary_eval_for_gui_display and isinstance(primary_eval_for_gui_display['test_classification_report'], dict):
                            current_target_gui_metrics_for_display_final.update(primary_eval_for_gui_display['test_classification_report'])

                avg_accuracy_display_text_gui_final_str = ""
                temp_acc_val_for_gui = current_target_gui_metrics_for_display_final.get('accuracy')
                if isinstance(temp_acc_val_for_gui, float): avg_accuracy_display_text_gui_final_str = f"(AccTest:{temp_acc_val_for_gui:.4f}"
                else: avg_accuracy_display_text_gui_final_str = f"({temp_acc_val_for_gui}"
                if len(all_folds_eval_details) > 1 and n_cv_folds_for_ensemble > 1:
                    valid_test_accuracies_for_avg = [f['test_accuracy'] for f in all_folds_eval_details if isinstance(f.get('test_accuracy'), float)]
                    if len(valid_test_accuracies_for_avg) >= 1:
                        avg_test_acc_val = np.mean(valid_test_accuracies_for_avg)
                        current_target_gui_metrics_for_display_final['ensemble_avg_test_accuracy'] = avg_test_acc_val
                        if len(valid_test_accuracies_for_avg) > 1 : avg_accuracy_display_text_gui_final_str += f", AvgFolds:{avg_test_acc_val:.4f}"
                avg_accuracy_display_text_gui_final_str += ")"
                current_target_gui_metrics_for_display_final['status'] = '评估完成' if all_folds_eval_details else '训练完成 (无评估数据)'
                status_tags_for_gui_title_final_list = []
                if rfe_succeeded: status_tags_for_gui_title_final_list.append("RFE")
                if initial_importance_succeeded: status_tags_for_gui_title_final_list.append("ImpThresh")
                if optuna_completed_successfully: status_tags_for_gui_title_final_list.append("Optuna")
                if n_cv_folds_for_ensemble > 1 and len(X_train_final_np) >= min_samples_for_cv : status_tags_for_gui_title_final_list.append(f"CV{n_cv_folds_for_ensemble}")
                else: status_tags_for_gui_title_final_list.append("SingleM")
                any_fold_calibrated_in_final = any(fold_art.get("calibrated_model_filename") for fold_art in fold_model_artifacts_list)
                if target_config.get('enable_probability_calibration', False) and any_fold_calibrated_in_final: status_tags_for_gui_title_final_list.append("Calib")
                status_tags_for_gui_title_final_list.append(f"{input_size_current}F")
                gui_title_text_final_update_str = f"目标: {target_name} {avg_accuracy_display_text_gui_final_str} [{', '.join(status_tags_for_gui_title_final_list)}]"
                if _main_root and _main_root.winfo_exists():
                 gui.update_gui_safe(gui.update_evaluation_metrics, target_name, current_target_gui_metrics_for_display_final)
                # 调用修复后的函数
                gui.update_gui_safe(gui.update_labelframe_text, target_name, gui_title_text_final_update_str)


                if all_folds_eval_details:
                    primary_eval_report = all_folds_eval_details[0] 
                    current_target_summary_for_console_report['name'] = target_name
                    current_target_summary_for_console_report['model_type'] = primary_eval_report.get('model_type', 'Unknown')
                    current_target_summary_for_console_report['num_folds_or_runs'] = len(all_folds_eval_details) if n_cv_folds_for_ensemble > 1 else 1
                    current_target_summary_for_console_report['train_accuracy'] = primary_eval_report.get('raw_train_accuracy')
                    current_target_summary_for_console_report['train_brier'] = primary_eval_report.get('raw_train_brier')
                    current_target_summary_for_console_report['internal_val_brier_raw'] = primary_eval_report.get('raw_internal_val_brier')
                    current_target_summary_for_console_report['internal_val_brier_calibrated'] = primary_eval_report.get('calibrated_internal_val_brier')
                    current_target_summary_for_console_report['calibration_selected_on_val'] = primary_eval_report.get('calibration_selected')
                    current_target_summary_for_console_report['test_accuracy'] = primary_eval_report.get('test_accuracy')
                    current_target_summary_for_console_report['test_brier'] = primary_eval_report.get('test_brier')
                    current_target_summary_for_console_report['test_model_used'] = primary_eval_report.get('test_model_type', 'Raw')
                    current_target_summary_for_console_report['test_classification_report_dict'] = primary_eval_report.get('test_classification_report')

                    # --- 添加最优阈值信息到控制台报告 ---
                    # 单模型或首折的阈值信息
                    current_target_summary_for_console_report['optimal_threshold'] = primary_eval_report.get('optimal_threshold')
                    current_target_summary_for_console_report['threshold_method'] = primary_eval_report.get('threshold_method')
                    current_target_summary_for_console_report['threshold_f1_score'] = primary_eval_report.get('threshold_f1_score')
                    current_target_summary_for_console_report['threshold_precision'] = primary_eval_report.get('threshold_precision')
                    current_target_summary_for_console_report['threshold_recall'] = primary_eval_report.get('threshold_recall')
                    current_target_summary_for_console_report['threshold_accuracy'] = primary_eval_report.get('threshold_accuracy')

                    # 多折集成模型的阈值信息（如果存在）
                    current_target_summary_for_console_report['ensemble_optimal_threshold'] = primary_eval_report.get('ensemble_optimal_threshold')
                    current_target_summary_for_console_report['ensemble_threshold_method'] = primary_eval_report.get('ensemble_threshold_method')
                    current_target_summary_for_console_report['ensemble_threshold_f1_score'] = primary_eval_report.get('ensemble_threshold_f1_score')
                    current_target_summary_for_console_report['ensemble_threshold_precision'] = primary_eval_report.get('ensemble_threshold_precision')
                    current_target_summary_for_console_report['ensemble_threshold_recall'] = primary_eval_report.get('ensemble_threshold_recall')
                    current_target_summary_for_console_report['ensemble_threshold_accuracy'] = primary_eval_report.get('ensemble_threshold_accuracy')

                    # 阈值优化状态信息
                    threshold_optimization_enabled_status = getattr(config, 'THRESHOLD_OPTIMIZATION_ENABLE', True) and target_config.get('threshold_optimization_enable', True)
                    current_target_summary_for_console_report['threshold_optimization_enabled'] = threshold_optimization_enabled_status
                    current_target_summary_for_console_report['threshold_use_independent_validation'] = target_config.get('threshold_use_independent_validation', getattr(config, 'THRESHOLD_USE_INDEPENDENT_VALIDATION', True))

                    # 添加RFE和Optuna信息到控制台报告
                    current_target_summary_for_console_report['rfe_succeeded'] = rfe_succeeded
                    current_target_summary_for_console_report['optuna_completed_successfully'] = optuna_completed_successfully
                    current_target_summary_for_console_report['feature_selection_details'] = {
                        "two_stage_enabled": use_two_stage_cfg,
                        "original_features_count": len(feature_names_all_from_data_prep) if 'feature_names_all_from_data_prep' in locals() else None,
                        "selected_features_count": len(feature_names_current) if rfe_succeeded and 'feature_names_current' in locals() else None,
                        "features_removed_count": (len(feature_names_all_from_data_prep) - len(feature_names_current)) if rfe_succeeded and 'feature_names_current' in locals() and 'feature_names_all_from_data_prep' in locals() else None,
                        "selected_features": feature_names_current[:10] if rfe_succeeded and 'feature_names_current' in locals() else None,
                        "selection_config": {
                            "prescreening_ratio": target_config.get('importance_prescreening_ratio', 0.6),
                            "rfe_cv_folds": target_config.get('rfe_cv_folds', 3),
                            "rfe_step": target_config.get('rfe_step', 1),
                            "rfe_min_features": target_config.get('rfe_min_features_to_select', 30),
                            "rfe_scoring": target_config.get('rfe_scoring', 'binary_simulated_profit')
                        } if use_two_stage_cfg else None
                    }
                    current_target_summary_for_console_report['optuna_details'] = {
                        "enabled": use_optuna_cfg,
                        "n_trials": target_config.get('optuna_n_trials', 100) if use_optuna_cfg else None,
                        "best_params": best_params_from_optuna if optuna_completed_successfully and best_params_from_optuna else None,
                        "optuna_config": {
                            "metric": target_config.get('optuna_metric', 'f1'),
                            "direction": target_config.get('optuna_direction', 'maximize'),
                            "cv_folds": target_config.get('optuna_cv_folds', 3),
                            "timeout": target_config.get('optuna_timeout', None)
                        } if use_optuna_cfg else None
                    }

                    overfit_indicator_console = "数据不足或评估错误"
                    train_acc_console = current_target_summary_for_console_report.get('train_accuracy')
                    test_acc_console = current_target_summary_for_console_report.get('test_accuracy')
                    train_brier_console = current_target_summary_for_console_report.get('train_brier')
                    test_brier_console = current_target_summary_for_console_report.get('test_brier')
                    if isinstance(train_acc_console, float) and isinstance(test_acc_console, float):
                        if train_acc_console > test_acc_console + 0.10: overfit_indicator_console = f"过拟合风险 (TrainAcc {train_acc_console:.3f} >> TestAcc {test_acc_console:.3f})"
                        elif test_acc_console > train_acc_console + 0.05: overfit_indicator_console = f"测试集表现意外好 (TestAcc {test_acc_console:.3f} > TrainAcc {train_acc_console:.3f})"
                        else: overfit_indicator_console = "Acc表现相对一致"
                    elif isinstance(train_brier_console, float) and isinstance(test_brier_console, float):
                        if train_brier_console < test_brier_console - 0.03: overfit_indicator_console = f"过拟合风险 (TrainBrier {train_brier_console:.3f} << TestBrier {test_brier_console:.3f})"
                        else: overfit_indicator_console = "Brier表现相对一致"
                    current_target_summary_for_console_report['overfitting_assessment'] = overfit_indicator_console
                    if current_target_summary_for_console_report['num_folds_or_runs'] > 1:
                        avg_test_acc = np.mean([f['test_accuracy'] for f in all_folds_eval_details if isinstance(f.get('test_accuracy'), float)]) if any(isinstance(f.get('test_accuracy'), float) for f in all_folds_eval_details) else None
                        avg_test_brier = np.mean([f['test_brier'] for f in all_folds_eval_details if isinstance(f.get('test_brier'), float)]) if any(isinstance(f.get('test_brier'), float) for f in all_folds_eval_details) else None
                        if avg_test_acc is not None: current_target_summary_for_console_report['avg_folds_test_accuracy'] = avg_test_acc
                        if avg_test_brier is not None: current_target_summary_for_console_report['avg_folds_test_brier'] = avg_test_brier
# --- 新增：计算并存储多折平均的分类指标 ---
                    if current_target_summary_for_console_report.get('num_folds_or_runs', 0) > 1 and all_folds_eval_details:
                        avg_metrics_class_1_console = {'precision': [], 'recall': [], 'f1-score': [], 'support': []}
                        avg_metrics_class_0_console = {'precision': [], 'recall': [], 'f1-score': [], 'support': []}
                        positive_class_key_in_report_console = '上涨 (1)' # 对于DOWN_ONLY模型，这代表“明确下跌”
                        negative_class_key_in_report_console = '下跌 (0)' # 代表“非明确下跌”
                        valid_reports_for_avg_console = 0

                        for fold_eval_console in all_folds_eval_details:
                            report_dict_console = fold_eval_console.get('test_classification_report')
                            if isinstance(report_dict_console, dict):
                                class_1_metrics_console = report_dict_console.get(positive_class_key_in_report_console)
                                class_0_metrics_console = report_dict_console.get(negative_class_key_in_report_console)
                                if isinstance(class_1_metrics_console, dict) and isinstance(class_0_metrics_console, dict):
                                    valid_reports_for_avg_console += 1
                                    for metric_key_console in ['precision', 'recall', 'f1-score', 'support']:
                                        if isinstance(class_1_metrics_console.get(metric_key_console), (int, float)):
                                            avg_metrics_class_1_console[metric_key_console].append(class_1_metrics_console[metric_key_console])
                                        if isinstance(class_0_metrics_console.get(metric_key_console), (int, float)):
                                            avg_metrics_class_0_console[metric_key_console].append(class_0_metrics_console[metric_key_console])
    
                        if valid_reports_for_avg_console > 0:
                             calculated_avg_class_1_console = {}
                        for metric_name_c, values_c in avg_metrics_class_1_console.items():
                            if values_c:
                                calculated_avg_class_1_console[metric_name_c] = np.mean(values_c) if metric_name_c != 'support' else np.sum(values_c)
                            else:
                                calculated_avg_class_1_console[metric_name_c] = np.nan
                        current_target_summary_for_console_report[f'avg_metrics_{positive_class_key_in_report_console}'] = calculated_avg_class_1_console

                        calculated_avg_class_0_console = {}
                        for metric_name_c, values_c in avg_metrics_class_0_console.items():
                            if values_c:
                                calculated_avg_class_0_console[metric_name_c] = np.mean(values_c) if metric_name_c != 'support' else np.sum(values_c)
                            else:
                                calculated_avg_class_0_console[metric_name_c] = np.nan
                        current_target_summary_for_console_report[f'avg_metrics_{negative_class_key_in_report_console}'] = calculated_avg_class_0_console
# --- 新增结束 ---
                        # --- 新增：计算多折平均的训练集性能以评估平均过拟合情况 ---
                        if current_target_summary_for_console_report.get('num_folds_or_runs', 0) > 1 and all_folds_eval_details:
                           train_accuracies_all_folds = [f.get('raw_train_accuracy') for f in all_folds_eval_details if isinstance(f.get('raw_train_accuracy'), float)]
                           train_briers_all_folds = [f.get('raw_train_brier') for f in all_folds_eval_details if isinstance(f.get('raw_train_brier'), float)]

                           avg_train_acc_all_folds = np.mean(train_accuracies_all_folds) if train_accuracies_all_folds else np.nan
                           avg_train_brier_all_folds = np.mean(train_briers_all_folds) if train_briers_all_folds else np.nan

                        # 获取已计算的多折平均测试集性能
                           avg_test_acc_console = current_target_summary_for_console_report.get('avg_folds_test_accuracy', np.nan)
                           avg_test_brier_console = current_target_summary_for_console_report.get('avg_folds_test_brier', np.nan)

                        overfit_indicator_avg_folds = "数据不足或评估错误"
                        if not pd.isna(avg_train_acc_all_folds) and not pd.isna(avg_test_acc_console):
                            if avg_train_acc_all_folds > avg_test_acc_console + 0.10: # 阈值可以调整
                                overfit_indicator_avg_folds = f"可能过拟合 (AvgTrainAcc {avg_train_acc_all_folds:.3f} >> AvgTestAcc {avg_test_acc_console:.3f})"
                            elif avg_test_acc_console > avg_train_acc_all_folds + 0.05:
                                overfit_indicator_avg_folds = f"测试集表现意外好 (AvgTestAcc {avg_test_acc_console:.3f} > AvgTrainAcc {avg_train_acc_all_folds:.3f})"
                            else:
                                overfit_indicator_avg_folds = "Acc表现相对一致 (多折平均)"
                        elif not pd.isna(avg_train_brier_all_folds) and not pd.isna(avg_test_brier_console):
                            if avg_train_brier_all_folds < avg_test_brier_console - 0.03: # 阈值可以调整 (Brier越小越好)
                                overfit_indicator_avg_folds = f"可能过拟合 (AvgTrainBrier {avg_train_brier_all_folds:.3f} << AvgTestBrier {avg_test_brier_console:.3f})"
                            else:
                                overfit_indicator_avg_folds = "Brier表现相对一致 (多折平均)"

                        current_target_summary_for_console_report['avg_train_accuracy_all_folds'] = avg_train_acc_all_folds
                        current_target_summary_for_console_report['avg_train_brier_all_folds'] = avg_train_brier_all_folds
                        current_target_summary_for_console_report['overfitting_assessment_avg_folds'] = overfit_indicator_avg_folds
# --- 新增结束 ---



                all_targets_results[target_name + "_console_summary"] = current_target_summary_for_console_report

            except Exception as e_target_pipeline_inner_main: 
                print(f"!!! 处理 Target '{target_name}' 核心流程出错: {e_target_pipeline_inner_main}"); traceback.print_exc(limit=2)
                current_target_gui_metrics_on_error_main = {'status': '核心流程错误', 'accuracy': 'N/A', 'error_message': str(e_target_pipeline_inner_main)}
                all_targets_results[target_name] = current_target_gui_metrics_on_error_main
                if _main_root and _main_root.winfo_exists():
                    gui.update_gui_safe(gui.update_evaluation_metrics, target_name, current_target_gui_metrics_on_error_main)
                    gui.update_gui_safe(gui.update_labelframe_text, target_name, f"目标: {target_name} (错误)")

            finally: 
                final_metrics_to_save_in_meta_file = all_targets_results.get(target_name, {'status': '元数据保存时状态未知'})
                if model_dir and saved_scaler_filename_shared and saved_feature_list_filename_shared and fold_model_artifacts_list:
                    meta_data_to_save_final_target_dict = {
                        "target_name": target_name,
                        "timestamp_utc": datetime.now(timezone.utc).isoformat(),
                        "scaler_filename": saved_scaler_filename_shared,
                        "feature_list_filename": saved_feature_list_filename_shared,
                        "fold_model_artifacts": fold_model_artifacts_list,
                        "final_training_summary_metrics_for_gui": final_metrics_to_save_in_meta_file,
                        "detailed_fold_evaluations": all_folds_eval_details, 
                        "rfe_succeeded": rfe_succeeded,
                        "initial_importance_succeeded": initial_importance_succeeded,
                        "optuna_completed_successfully": optuna_completed_successfully,
                        # 🎯 添加两阶段特征选择详细信息
                        "feature_selection_details": {
                            "two_stage_enabled": use_two_stage_cfg,
                            "original_features_count": len(feature_names_all_from_data_prep) if 'feature_names_all_from_data_prep' in locals() else None,
                            "selected_features_count": len(feature_names_current) if rfe_succeeded and 'feature_names_current' in locals() else None,
                            "features_removed_count": (len(feature_names_all_from_data_prep) - len(feature_names_current)) if rfe_succeeded and 'feature_names_current' in locals() and 'feature_names_all_from_data_prep' in locals() else None,
                            "selected_features": feature_names_current[:10] if rfe_succeeded and 'feature_names_current' in locals() else None,  # 保存前10个特征
                            "selection_config": {
                                "prescreening_ratio": target_config.get('importance_prescreening_ratio', 0.6),
                                "rfe_cv_folds": target_config.get('rfe_cv_folds', 3),
                                "rfe_step": target_config.get('rfe_step', 1),
                                "rfe_min_features": target_config.get('rfe_min_features_to_select', 30),
                                "rfe_scoring": target_config.get('rfe_scoring', 'binary_simulated_profit')
                            } if use_two_stage_cfg else None
                        },
                        # 添加Optuna详细信息
                        "optuna_details": {
                            "enabled": use_optuna_cfg,
                            "n_trials": target_config.get('optuna_n_trials', 100) if use_optuna_cfg else None,
                            "best_params": best_params_from_optuna if optuna_completed_successfully and best_params_from_optuna else None,
                            "optuna_config": {
                                "metric": target_config.get('optuna_metric', 'f1'),
                                "direction": target_config.get('optuna_direction', 'maximize'),
                                "cv_folds": target_config.get('optuna_cv_folds', 3),
                                "timeout": target_config.get('optuna_timeout', None)
                            } if use_optuna_cfg else None
                        },
                        "model_suffix_parts_used_for_artifacts": final_model_suffix_str_for_files,
                        "ensemble_cv_folds_used": n_cv_folds_for_ensemble if n_cv_folds_for_ensemble > 1 and len(X_train_final_np) >= min_samples_for_cv else 1
                    }
                    meta_file_path_final_target_str = os.path.join(model_dir, f"model_meta_{target_name_for_file}_{minutes_display_for_file}m.json")
                    try:
                        with open(meta_file_path_final_target_str, 'w') as f_meta_write_final_target:
                            json.dump(meta_data_to_save_final_target_dict, f_meta_write_final_target, indent=4)
                        print(f"  -> 元数据已为目标 '{target_name}' 保存/更新到: {meta_file_path_final_target_str}")
                    except Exception as e_meta_save_final_target_inner_exc:
                        print(f"!!! 保存元数据 '{meta_file_path_final_target_str}' 失败: {e_meta_save_final_target_inner_exc}")
                else:
                    print(f"  -> ({target_name}) 未保存元数据 (共享Scaler/特征列表或fold产物列表缺失).")

                print(f"<---<<< 完成处理目标循环迭代: {target_name}")
                if len(targets_to_process_this_run) > 1 and target_config_base_idx < len(targets_to_process_this_run) - 1:
                    time.sleep(1.0) 

        # --- 元模型OOF数据生成和训练流程 ---
        # 只有在全局启用元模型训练，并且当前运行不是特定方向的过滤训练时，才执行
        if getattr(config, 'ENABLE_META_MODEL_TRAINING', False) and (target_type_filter is None):
            print("\n===== 开始元模型OOF数据生成和训练流程 =====")
            if _main_root and _main_root.winfo_exists() and hasattr(gui, 'update_status'): # 确保GUI存在
                gui.update_gui_safe(gui.update_status, "开始元模型OOF数据准备...", "neutral")
                if hasattr(gui, 'set_progress_bar_indeterminate'):
                    gui.update_gui_safe(gui.set_progress_bar_indeterminate, True)

            if not hasattr(config, 'BASE_MODELS_FOR_META') or not config.BASE_MODELS_FOR_META or not isinstance(config.BASE_MODELS_FOR_META, list):
                print("!!! 严重错误: config.BASE_MODELS_FOR_META 未配置或为空列表，无法继续元模型OOF流程。")
            else:
                # 初始化存储所有基础模型OOF预测的字典
                oof_predictions_all_bases = {}
                
                # 1. 为元模型准备统一的、包含所有可能特征的历史数据
                #    🔧 修复：选择一个有效的基础模型配置来获取K线和进行初步的特征工程
                #    这个 df_full_hist_data_for_oof_input 将传递给每个基础模型的OOF生成函数
                valid_base_model_name_for_hist_data = None
                for base_model_name in config.BASE_MODELS_FOR_META:
                    base_config = safe_get_target_config(base_model_name)
                    base_interval = base_config.get('interval', '15m')
                    # 检查是否为有效的时间间隔
                    if base_interval not in ["Meta-Analysis", "N/A", None] and isinstance(base_interval, str):
                        valid_base_model_name_for_hist_data = base_model_name
                        break

                if not valid_base_model_name_for_hist_data:
                    print("!!! 警告 [MetaOOFDataPrep]: 未找到有效的基础模型配置，使用第一个模型并强制设置interval为'15m'")
                    valid_base_model_name_for_hist_data = config.BASE_MODELS_FOR_META[0]

                first_base_model_name_for_hist_data = valid_base_model_name_for_hist_data
                df_full_hist_data_for_oof_input = None
                y_meta_series_for_oof_input = None # 元目标，也需要基于这份完整数据生成
                meta_target_col_name = None      # 元目标列名

                try:
                    print(f"  [MetaOOFDataPrep] 使用基础模型 '{first_base_model_name_for_hist_data}' 的配置获取元模型训练用的完整历史K线...")
                    hist_conf_for_meta_base = safe_get_target_config(first_base_model_name_for_hist_data)

                    # 🔧 修复：确保使用有效的时间间隔，避免"Meta-Analysis"等无效值
                    base_interval = hist_conf_for_meta_base.get('interval', '15m')
                    if base_interval in ["Meta-Analysis", "N/A", None] or not isinstance(base_interval, str):
                        print(f"  [MetaOOFDataPrep] 警告：基础模型 '{first_base_model_name_for_hist_data}' 的interval '{base_interval}' 无效，使用默认值 '15m'")
                        base_interval = '15m'

                    # 获取比DATA_FETCH_LIMIT更多的数据，以确保有足够的回溯期给所有特征和MTFA
                    # 我们需要确保这里的 limit 足够大，覆盖所有基础模型OOF生成时可能需要的最早数据点
                    # 这通常意味着 limit 应该大于 (TimeSeriesSplit的验证集长度 + 最大特征回溯期)
                    # 一个更安全的做法是，如果可能，获取非常大量的历史数据，或者精确计算所需长度
                    meta_hist_fetch_limit = getattr(config, 'DATA_FETCH_LIMIT', 20000) + getattr(config, 'ADDITIONAL_BARS_FOR_MTFA_AND_FEATURES', 5000)

                    df_full_historical_raw_for_meta = data_utils.fetch_binance_history(
                        binance_client,
                        hist_conf_for_meta_base.get('symbol', getattr(config, 'SYMBOL', 'BTCUSDT')),
                        base_interval, # 使用验证过的时间间隔
                        limit=meta_hist_fetch_limit
                    )
                    if df_full_historical_raw_for_meta is None or df_full_historical_raw_for_meta.empty:
                        raise ValueError("未能为元模型OOF流程获取到足够的原始K线数据。")

                    # 重要修复：不再预先生成特征，而是保留原始数据，让每个基础模型使用自己的配置生成特征
                    print(f"  [MetaOOFDataPrep] 保留原始历史数据，让每个基础模型使用自己的配置生成特征...")
                    df_full_hist_data_for_oof_input = df_full_historical_raw_for_meta.copy()

                    print(f"  [MetaOOFDataPrep] 原始历史数据准备完毕，形状: {df_full_hist_data_for_oof_input.shape}")

                    # 创建元模型的目标变量 (y_meta_series_for_oof_input)
                    # 使用 BASE_CONFIG_FOR_META_TARGET_DEFINITION 中指定的配置来定义元目标
                    config_name_for_meta_target_def = getattr(config, 'BASE_CONFIG_FOR_META_TARGET_DEFINITION', config.BASE_MODELS_FOR_META[0])
                    actual_config_for_meta_target_def = safe_get_target_config(config_name_for_meta_target_def)
                    print(f"  [MetaOOFDataPrep] 使用 '{config_name_for_meta_target_def}' 的配置定义元目标变量。")

                    # 重要修复：由于现在传递的是原始数据，需要先生成特征再创建目标变量
                    print(f"  [MetaOOFDataPrep] 为元目标创建临时生成特征...")
                    df_temp_features_for_meta_target = data_utils.add_classification_features(df_full_hist_data_for_oof_input.copy(), actual_config_for_meta_target_def)
                    if df_temp_features_for_meta_target is None or df_temp_features_for_meta_target.empty:
                        raise ValueError("为元目标创建临时特征失败。")

                    # create_meta_target_variable 会在临时特征数据上操作
                    # 获取 drop_neutral_targets 参数，默认为 False
                    drop_neutral_for_meta = actual_config_for_meta_target_def.get('drop_neutral_targets', False)
                    df_with_meta_target, meta_target_col_name_temp = data_utils.create_meta_target_variable(
                        df_temp_features_for_meta_target, # 使用临时生成的特征DataFrame
                        actual_config_for_meta_target_def,
                        drop_neutral_targets=drop_neutral_for_meta
                    )
                    if not meta_target_col_name_temp or meta_target_col_name_temp not in df_with_meta_target.columns:
                        raise ValueError("未能创建元模型的目标变量。元模型训练中止。")

                    meta_target_col_name = meta_target_col_name_temp # 赋值给外层变量
                    y_meta_series_for_oof_input = df_with_meta_target[meta_target_col_name].copy()
                    # 确保原始数据与目标变量的索引对齐
                    df_full_hist_data_for_oof_input = df_full_hist_data_for_oof_input.loc[y_meta_series_for_oof_input.index]

                    print(f"  [MetaOOFDataPrep] 元目标已创建。y_meta_series_for_oof_input shape: {y_meta_series_for_oof_input.shape}")

                except Exception as e_meta_data_prep:
                    print(f"!!! 严重错误 [MetaOOFDataPrep]: 在为元模型准备基础数据和特征时发生错误: {e_meta_data_prep}")
                    traceback.print_exc(limit=2)
                    # 如果数据准备失败，则无法继续OOF生成
                    df_full_hist_data_for_oof_input = None 


                # 2. 迭代每个基础模型，为其生成OOF预测
                if df_full_hist_data_for_oof_input is not None and y_meta_series_for_oof_input is not None:
                    num_base_models_for_meta = len(config.BASE_MODELS_FOR_META)
                    for idx_bm_meta, base_model_name_for_oof in enumerate(config.BASE_MODELS_FOR_META):
                        if _main_root and _main_root.winfo_exists() and hasattr(gui, 'update_status'):
                            gui.update_gui_safe(gui.update_status, f"生成OOF: {base_model_name_for_oof} ({idx_bm_meta+1}/{num_base_models_for_meta})...", "neutral")
                        
                        print(f"\n  [MetaOOFGen] 准备为基础模型 '{base_model_name_for_oof}' 生成OOF预测...")

                        # 🎯 修复：检查binance_client状态并记录
                        if binance_client is None:
                            print(f"  警告 [MetaOOFGen]: binance_client为None，MTFA特征可能无法正常生成")
                        else:
                            print(f"  [MetaOOFGen] binance_client已初始化，支持MTFA特征生成")

                        # 注意：generate_oof_predictions_for_base_model 现在接收 y_meta_series_aligned 参数
                        # 我们传递 y_meta_series_for_oof_input，它与 df_full_hist_data_for_oof_input 对齐
                        oof_series_current = generate_oof_predictions_for_base_model(
                            base_model_target_name=base_model_name_for_oof,
                            df_full_hist_data_with_features=df_full_hist_data_for_oof_input.copy(), # 传递副本
                            y_meta_series_aligned=y_meta_series_for_oof_input.copy(), # 传递副本
                            app_timezone=APP_TIMEZONE, # APP_TIMEZONE 应在 run_training_pipeline 的作用域内可用
                            binance_client=binance_client # 🎯 修复：传递 binance_client 实例
                        )
                        if oof_series_current is not None and isinstance(oof_series_current, pd.Series) and not oof_series_current.empty:
                            oof_predictions_all_bases[base_model_name_for_oof] = oof_series_current
                            print(f"  [MetaOOFGen] 基础模型 '{base_model_name_for_oof}' 的OOF预测已生成并存储。Shape: {oof_series_current.shape}")
                        else:
                            print(f"!!! 警告 [MetaOOFGen]: 未能为基础模型 '{base_model_name_for_oof}' 生成有效的OOF预测数据。它将从元模型输入中缺失。")
                            # oof_predictions_all_bases 中不会有这个模型的键，或者键对应的值是None
                else:
                    print("!!! 严重错误 [MetaOOFGen]: 无法为基础模型生成OOF预测，因为 df_full_hist_data_for_oof_input 或 y_meta_series_for_oof_input 未能准备好。")


                # 3. 构建元模型的输入特征 (X_meta) 和对齐目标 (y_meta)
                temp_x_meta_list = []
                temp_x_meta_cols = []
                all_required_oof_found_and_valid_for_meta = True

                if not oof_predictions_all_bases: # 如果一个OOF都没生成
                    all_required_oof_found_and_valid_for_meta = False
                    print("!!! 严重错误 [MetaXPrep]: oof_predictions_all_bases 字典为空，没有可用的OOF预测。")

                if all_required_oof_found_and_valid_for_meta:
                    print(f"  [MetaXPrep] 期望的基础模型 (来自 config.BASE_MODELS_FOR_META): {config.BASE_MODELS_FOR_META}")
                    print(f"  [MetaXPrep] 当前 oof_predictions_all_bases 中的键: {list(oof_predictions_all_bases.keys())}")
                    for bm_name_expected_meta in config.BASE_MODELS_FOR_META:
                        if bm_name_expected_meta in oof_predictions_all_bases:
                            oof_s = oof_predictions_all_bases[bm_name_expected_meta]
                            if oof_s is not None and isinstance(oof_s, pd.Series) and not oof_s.empty:
                                temp_x_meta_list.append(oof_s)
                                temp_x_meta_cols.append(f"oof_proba_{bm_name_expected_meta}")
                                print(f"  [MetaXPrep] 已添加基础模型 '{bm_name_expected_meta}' 的OOF预测 (shape: {oof_s.shape}) 到X_meta构建列表。")
                            else:
                                print(f"!!! 错误 [MetaXPrep]: 基础模型 '{bm_name_expected_meta}' 的OOF预测数据无效 (None或空Series)。")
                                all_required_oof_found_and_valid_for_meta = False; break
                        else:
                            print(f"!!! 错误 [MetaXPrep]: 基础模型 '{bm_name_expected_meta}' 的OOF预测在字典中缺失。")
                            all_required_oof_found_and_valid_for_meta = False; break
                
                if not all_required_oof_found_and_valid_for_meta:
                    print("!!! 严重错误: 未能为所有预期的基础模型找到有效的OOF预测数据。元模型训练将中止。")
                elif not temp_x_meta_list:
                     print("!!! 严重错误 [MetaXPrep]: temp_x_meta_list 为空。元模型训练中止。")
                else:
                    try:
                        X_meta_df_raw_with_nans = pd.concat(temp_x_meta_list, axis=1) # 按列合并Series
                        print(f"  [MetaXPrep] pd.concat 完成。X_meta_df_raw_with_nans 形状: {X_meta_df_raw_with_nans.shape}")

                        if X_meta_df_raw_with_nans.shape[1] != len(temp_x_meta_cols):
                            msg_mismatch = (f"!!! 严重错误 [MetaXPrep]: 合并后的X_meta列数 ({X_meta_df_raw_with_nans.shape[1]}) "
                                            f"与期望的列名数量 ({len(temp_x_meta_cols)}) 不匹配！")
                            print(msg_mismatch)
                            for i, s_debug in enumerate(temp_x_meta_list):
                                print(f"      Series {i} for concat: name='{s_debug.name}', shape={s_debug.shape}, length={len(s_debug)}")
                            raise ValueError(msg_mismatch)

                        X_meta_df_raw_with_nans.columns = temp_x_meta_cols
                        print(f"  [MetaXPrep] X_meta_df_raw_with_nans 列名成功赋值: {X_meta_df_raw_with_nans.columns.tolist()}")

                        # 处理由于TimeSeriesSplit导致的头部NaN (这些行在OOF中没有被任何验证集覆盖)
                        print(f"  [MetaPrep] X_meta_df_raw_with_nans shape (before dropna): {X_meta_df_raw_with_nans.shape}")
                        initial_nan_rows_mask_meta = X_meta_df_raw_with_nans.isnull().any(axis=1)
                        X_meta_basic = X_meta_df_raw_with_nans[~initial_nan_rows_mask_meta].copy()

                        # 添加特征工程
                        print("  [MetaPrep] 应用元模型特征工程...")
                        X_meta_enhanced = add_meta_feature_engineering(X_meta_basic, config.BASE_MODELS_FOR_META)
                        X_meta_final_to_save = X_meta_enhanced
                        
                        y_meta_final_to_save = pd.Series(dtype=object) # 初始化
                        if y_meta_series_for_oof_input is not None and not y_meta_series_for_oof_input.empty:
                            common_index_for_final_meta_xy = X_meta_final_to_save.index.intersection(y_meta_series_for_oof_input.index)
                            X_meta_final_to_save = X_meta_final_to_save.loc[common_index_for_final_meta_xy]
                            y_meta_final_to_save = y_meta_series_for_oof_input.loc[common_index_for_final_meta_xy].copy()
                            print(f"  [MetaPrep] y_meta_series 对齐完成。y_meta_final_to_save shape: {y_meta_final_to_save.shape}")
                        else:
                            print("!!! 错误 [MetaPrep]: y_meta_series_for_oof_input 为空或未定义，无法对齐元目标。")
                            X_meta_final_to_save = pd.DataFrame() # 中止后续

                        print(f"  [MetaPrep] X_meta_final_to_save shape after dropping NaNs and aligning y: {X_meta_final_to_save.shape}")
                        print(f"  [MetaPrep] NaN count in X_meta_final_to_save after dropping: {X_meta_final_to_save.isnull().sum().sum()}")
                        
                        if X_meta_final_to_save.empty or y_meta_final_to_save.empty:
                            print("!!! 错误: 对齐并移除初始NaN后的 X_meta 或 y_meta 为空。元模型训练中止。")
                        else:
                            meta_data_save_dir = getattr(config, 'META_MODEL_SAVE_DIR', "meta_model_data")
                            os.makedirs(meta_data_save_dir, exist_ok=True)
                            x_meta_path = os.path.join(meta_data_save_dir, "X_meta_features_oof.csv")
                            y_meta_path = os.path.join(meta_data_save_dir, "y_meta_target.csv")
                            X_meta_final_to_save.to_csv(x_meta_path, index=True)
                            if meta_target_col_name: # 确保 meta_target_col_name 已定义
                                y_meta_final_to_save.to_csv(y_meta_path, header=[meta_target_col_name], index=True)
                            else:
                                y_meta_final_to_save.to_csv(y_meta_path, header=True, index=True) # 如果没有列名，就用默认
                            print(f"  X_meta (OOF特征) 已保存到: {x_meta_path} (形状: {X_meta_final_to_save.shape})")
                            print(f"  y_meta (元目标) 已保存到: {y_meta_path} (形状: {y_meta_final_to_save.shape})")
                            print("  元模型训练数据准备完毕。下一步: 调用 prediction.train_meta_model()。")
                            
                            # 调用元模型训练函数 (从 prediction 模块)
                            # 确保 prediction 模块已导入
                            train_success_meta, trained_meta_model_object, \
                            X_val_meta_from_func, y_val_meta_from_func, \
                            meta_eval_results_dict_from_func = \
                                prediction.train_meta_model(X_meta_final_to_save, y_meta_final_to_save)
                            
                            if train_success_meta: 
                                print(f"--- 元模型 '{getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', 'MetaModel')}' 训练成功 ---")
                                meta_model_console_summary_data = { "name": getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', 'MetaModel'), "model_type": "LGBM_Meta", "status": "训练成功"}
                                if meta_eval_results_dict_from_func:
                                    meta_model_console_summary_data.update(meta_eval_results_dict_from_func)
                                    # ... (您现有的打印元模型评估结果的逻辑) ...
                                all_targets_results[meta_model_console_summary_data['name'] + "_console_summary"] = meta_model_console_summary_data
                            else:
                                print("!!! prediction.train_meta_model 函数报告元模型训练失败。")
                                all_targets_results[getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', 'MetaModel') + "_console_summary"] = {"name": getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', 'MetaModel'), "status": "训练失败(prediction.train_meta_model返回False)"}

                    except ValueError as ve_concat_final:
                        print(f"!!! 严重错误 [MetaXPrep Final]: 在最终合并OOF或赋值列名时发生 ValueError: {ve_concat_final}")
                        traceback.print_exc(limit=2)
                        all_targets_results[getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', 'MetaModel') + "_console_summary"] = {"name": getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', 'MetaModel'), "status": f"错误(X_meta构建失败): {ve_concat_final}"}
                    except Exception as e_meta_prep_final:
                        print(f"!!! 严重错误 [MetaXPrep Final]: 在最终合并OOF或处理数据时发生未知错误: {e_meta_prep_final}")
                        traceback.print_exc(limit=2)
                        all_targets_results[getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', 'MetaModel') + "_console_summary"] = {"name": getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', 'MetaModel'), "status": f"错误(X_meta构建失败): {e_meta_prep_final}"}

        elif getattr(config, 'ENABLE_META_MODEL_TRAINING', False) and target_type_filter is not None:
            print(f"--- 元模型训练仅在 '训练所有目标' 时执行，当前过滤器为 '{target_type_filter}'，跳过元模型训练 ---")
        
        if _main_root and _main_root.winfo_exists() and hasattr(gui, 'reset_progress_bar'): # 确保GUI存在
            gui.update_gui_safe(gui.reset_progress_bar)
            if hasattr(gui, 'update_status'):
                gui.update_gui_safe(gui.update_status, "元模型OOF数据生成和训练流程结束。", "neutral")
        print("===== 元模型OOF数据生成和训练流程结束 =====")

        
        print(f"\n\n{'-'*30} 最终训练评估汇总 (控制台报告) {'-'*30}")
        for target_key_console, summary_data_console in all_targets_results.items():
            if not target_key_console.endswith("_console_summary"): continue
            if isinstance(summary_data_console, dict) and "name" in summary_data_console:
                print(f"\n--- 模型: {summary_data_console['name']} ({summary_data_console.get('model_type', 'N/A')}) ---")
                if summary_data_console.get('status') == 'Error':
                    print(f"    状态: 训练或评估过程中发生错误 - {summary_data_console.get('error_message', '未知错误')}")
                    continue
                print(f"  类型/Fold数: {summary_data_console.get('model_type','N/A')} / {summary_data_console.get('num_folds_or_runs', 'N/A')}")

                # --- 显示RFE信息 (如果启用) ---
                rfe_details = summary_data_console.get('rfe_details', {})
                if rfe_details and rfe_details.get('enabled', False):
                    print(f"\n  🔍 RFE (递归特征消除) 信息:")
                    if summary_data_console.get('rfe_succeeded', False):
                        original_count = rfe_details.get('original_features_count', 'N/A')
                        selected_count = rfe_details.get('selected_features_count', 'N/A')
                        removed_count = rfe_details.get('features_removed_count', 'N/A')
                        print(f"    ✅ RFE执行成功")
                        print(f"    特征数量变化: {original_count} → {selected_count} (移除 {removed_count} 个)")

                        if rfe_details.get('selected_features'):
                            top_features = rfe_details['selected_features']
                            print(f"    关键特征 (前{len(top_features)}个): {', '.join(top_features)}")

                        rfe_config = rfe_details.get('rfe_config', {})
                        if rfe_config:
                            print(f"    RFE配置: CV折数={rfe_config.get('cv_folds', 'N/A')}, "
                                  f"步长={rfe_config.get('step', 'N/A')}, "
                                  f"最小特征数={rfe_config.get('min_features', 'N/A')}, "
                                  f"评分指标={rfe_config.get('scoring', 'N/A')}")
                    else:
                        print(f"    ❌ RFE执行失败或未完成")

                # --- 显示Optuna信息 (如果启用) ---
                optuna_details = summary_data_console.get('optuna_details', {})
                if optuna_details and optuna_details.get('enabled', False):
                    print(f"\n  ⚡ Optuna 超参数优化信息:")
                    if summary_data_console.get('optuna_completed_successfully', False):
                        print(f"    ✅ Optuna优化成功")
                        n_trials = optuna_details.get('n_trials', 'N/A')
                        print(f"    试验次数: {n_trials}")

                        best_params = optuna_details.get('best_params', {})
                        if best_params:
                            print(f"    最佳参数组合:")
                            for param_name, param_value in best_params.items():
                                if isinstance(param_value, float):
                                    print(f"      {param_name}: {param_value:.4f}")
                                else:
                                    print(f"      {param_name}: {param_value}")

                        optuna_config = optuna_details.get('optuna_config', {})
                        if optuna_config:
                            print(f"    优化配置: 目标指标={optuna_config.get('metric', 'N/A')}, "
                                  f"方向={optuna_config.get('direction', 'N/A')}, "
                                  f"CV折数={optuna_config.get('cv_folds', 'N/A')}")
                    else:
                        print(f"    ❌ Optuna优化失败或未完成")

                if 'train_accuracy' in summary_data_console:
                    train_acc_con = summary_data_console.get('train_accuracy'); train_brier_con = summary_data_console.get('train_brier')
                    # FIXED: 使用修正后的f-string格式
                    train_acc_con_str = f"{train_acc_con:.4f}" if isinstance(train_acc_con, float) else str(train_acc_con)
                    train_brier_con_str = f"{train_brier_con:.4f}" if isinstance(train_brier_con, float) else str(train_brier_con)
                    print(f"  训练集 (首折/单模型): Acc={train_acc_con_str}, Brier={train_brier_con_str}")

                    iv_brier_r_con = summary_data_console.get('internal_val_brier_raw'); iv_brier_c_con = summary_data_console.get('internal_val_brier_calibrated'); cal_sel_con = summary_data_console.get('calibration_selected_on_val')
                    iv_brier_r_con_str = f"{iv_brier_r_con:.4f}" if isinstance(iv_brier_r_con, float) else str(iv_brier_r_con)
                    iv_brier_c_con_str = f"{iv_brier_c_con:.4f}" if isinstance(iv_brier_c_con, float) else str(iv_brier_c_con)
                    print(f"  内部验证集 (首折/单模型) Brier: Raw={iv_brier_r_con_str}, Cal={iv_brier_c_con_str} (选用校准: {cal_sel_con})")
                    
                    test_acc_con = summary_data_console.get('test_accuracy'); test_brier_con = summary_data_console.get('test_brier'); test_model_con = summary_data_console.get('test_model_used', 'N/A')
                    test_acc_con_str = f"{test_acc_con:.4f}" if isinstance(test_acc_con, float) else str(test_acc_con)
                    test_brier_con_str = f"{test_brier_con:.4f}" if isinstance(test_brier_con, float) else str(test_brier_con)
                    print(f"  测试集 (首折/单模型, 使用 {test_model_con}): Acc={test_acc_con_str}, Brier={test_brier_con_str}")

                    # --- 显示最优阈值信息 ---
                    threshold_optimization_enabled_con = summary_data_console.get('threshold_optimization_enabled', False)
                    threshold_use_independent_val_con = summary_data_console.get('threshold_use_independent_validation', False)

                    if threshold_optimization_enabled_con:
                        print(f"\n  🎯 最优决策阈值信息:")

                        # 单模型或首折的阈值信息
                        optimal_threshold_con = summary_data_console.get('optimal_threshold')
                        threshold_method_con = summary_data_console.get('threshold_method', 'N/A')

                        if optimal_threshold_con is not None:
                            threshold_f1_con = summary_data_console.get('threshold_f1_score')
                            threshold_precision_con = summary_data_console.get('threshold_precision')
                            threshold_recall_con = summary_data_console.get('threshold_recall')
                            threshold_accuracy_con = summary_data_console.get('threshold_accuracy')

                            # 格式化阈值性能指标
                            threshold_f1_str = f"{threshold_f1_con:.4f}" if isinstance(threshold_f1_con, float) else str(threshold_f1_con)
                            threshold_precision_str = f"{threshold_precision_con:.4f}" if isinstance(threshold_precision_con, float) else str(threshold_precision_con)
                            threshold_recall_str = f"{threshold_recall_con:.4f}" if isinstance(threshold_recall_con, float) else str(threshold_recall_con)
                            threshold_accuracy_str = f"{threshold_accuracy_con:.4f}" if isinstance(threshold_accuracy_con, float) else str(threshold_accuracy_con)

                            print(f"    单模型/首折阈值: {optimal_threshold_con:.4f} (方法: {threshold_method_con})")
                            print(f"    阈值性能 [验证集]: F1={threshold_f1_str}, Precision={threshold_precision_str}, Recall={threshold_recall_str}, Acc={threshold_accuracy_str}")
                        else:
                            print(f"    单模型/首折阈值: 优化失败，使用默认阈值 (方法: {threshold_method_con})")

                        # 多折集成模型的阈值信息（如果存在）
                        ensemble_optimal_threshold_con = summary_data_console.get('ensemble_optimal_threshold')
                        if ensemble_optimal_threshold_con is not None:
                            ensemble_threshold_method_con = summary_data_console.get('ensemble_threshold_method', 'N/A')
                            ensemble_threshold_f1_con = summary_data_console.get('ensemble_threshold_f1_score')
                            ensemble_threshold_precision_con = summary_data_console.get('ensemble_threshold_precision')
                            ensemble_threshold_recall_con = summary_data_console.get('ensemble_threshold_recall')
                            ensemble_threshold_accuracy_con = summary_data_console.get('ensemble_threshold_accuracy')

                            # 格式化集成阈值性能指标
                            ensemble_f1_str = f"{ensemble_threshold_f1_con:.4f}" if isinstance(ensemble_threshold_f1_con, float) else str(ensemble_threshold_f1_con)
                            ensemble_precision_str = f"{ensemble_threshold_precision_con:.4f}" if isinstance(ensemble_threshold_precision_con, float) else str(ensemble_threshold_precision_con)
                            ensemble_recall_str = f"{ensemble_threshold_recall_con:.4f}" if isinstance(ensemble_threshold_recall_con, float) else str(ensemble_threshold_recall_con)
                            ensemble_accuracy_str = f"{ensemble_threshold_accuracy_con:.4f}" if isinstance(ensemble_threshold_accuracy_con, float) else str(ensemble_threshold_accuracy_con)

                            print(f"    集成模型阈值: {ensemble_optimal_threshold_con:.4f} (方法: {ensemble_threshold_method_con})")
                            print(f"    集成阈值性能 [验证集]: F1={ensemble_f1_str}, Precision={ensemble_precision_str}, Recall={ensemble_recall_str}, Acc={ensemble_accuracy_str}")

                        # 显示阈值优化配置信息
                        validation_type = "独立验证集" if threshold_use_independent_val_con else "训练验证集"
                        print(f"    阈值优化配置: 使用{validation_type}进行优化")
                    else:
                        print(f"\n  🎯 最优决策阈值: 已禁用，使用默认阈值 0.5000")

                    if 'avg_folds_test_accuracy' in summary_data_console:
                        avg_test_acc_str = f"{summary_data_console['avg_folds_test_accuracy']:.4f}" if isinstance(summary_data_console['avg_folds_test_accuracy'], float) else str(summary_data_console['avg_folds_test_accuracy'])
                        avg_test_brier_val = summary_data_console.get('avg_folds_test_brier', 'N/A')
                        avg_test_brier_str = f"{avg_test_brier_val:.4f}" if isinstance(avg_test_brier_val, float) else str(avg_test_brier_val)
                        print(f"  测试集 (多Fold平均): Acc={avg_test_acc_str}, Brier={avg_test_brier_str}")

                    print(f"  过拟合评估 (首折/单模型): {summary_data_console.get('overfitting_assessment', '未知')}")
                    report_dict_con = summary_data_console.get('test_classification_report_dict')
                    if isinstance(report_dict_con, dict):
                        print(f"  测试集分类报告 (首折/单模型):")
                        print(f"    下跌(0): P={report_dict_con.get('下跌 (0)',{}).get('precision','N/A'):.3f} R={report_dict_con.get('下跌 (0)',{}).get('recall','N/A'):.3f} F1={report_dict_con.get('下跌 (0)',{}).get('f1-score','N/A'):.3f} (Sup: {report_dict_con.get('下跌 (0)',{}).get('support','N/A')})")
                        print(f"    上涨(1): P={report_dict_con.get('上涨 (1)',{}).get('precision','N/A'):.3f} R={report_dict_con.get('上涨 (1)',{}).get('recall','N/A'):.3f} F1={report_dict_con.get('上涨 (1)',{}).get('f1-score','N/A'):.3f} (Sup: {report_dict_con.get('上涨 (1)',{}).get('support','N/A')})")
                        print(f"    Acc(macro): {report_dict_con.get('macro avg',{}).get('f1-score','N/A'):.3f}, Acc(weighted): {report_dict_con.get('weighted avg',{}).get('f1-score','N/A'):.3f}, Acc(overall): {report_dict_con.get('accuracy','N/A'):.4f}")
# --- 新增：在这里打印多折平均分类指标 (插入点2) ---
                    if summary_data_console.get('num_folds_or_runs', 0) > 1: # 只有在多折情况下才打印平均值
                       positive_class_key_console_print = '上涨 (1)' # 对于DOWN_ONLY模型，这代表“明确下跌”
                       negative_class_key_console_print = '下跌 (0)' # 代表“非明确下跌”
    
                       avg_class_1_metrics_to_print = summary_data_console.get(f'avg_metrics_{positive_class_key_console_print}')
                       if isinstance(avg_class_1_metrics_to_print, dict):
                          print(f"  多折平均 - '{positive_class_key_console_print}': "
                       f"P={avg_class_1_metrics_to_print.get('precision', np.nan):.3f}, "
                       f"R={avg_class_1_metrics_to_print.get('recall', np.nan):.3f}, "
                       f"F1={avg_class_1_metrics_to_print.get('f1-score', np.nan):.3f} "
                       f"(TotalSupForAvg: {avg_class_1_metrics_to_print.get('support', 0) :.0f})") # Support是总和，不是平均

                       avg_class_0_metrics_to_print = summary_data_console.get(f'avg_metrics_{negative_class_key_console_print}')
                       if isinstance(avg_class_0_metrics_to_print, dict):
                            print(f"  多折平均 - '{negative_class_key_console_print}': "
                       f"P={avg_class_0_metrics_to_print.get('precision', np.nan):.3f}, "
                       f"R={avg_class_0_metrics_to_print.get('recall', np.nan):.3f}, "
                       f"F1={avg_class_0_metrics_to_print.get('f1-score', np.nan):.3f} "
                       f"(TotalSupForAvg: {avg_class_0_metrics_to_print.get('support', 0) :.0f})") # Support是总和
                       # --- 新增结束 ---

# --- 新增：打印多折平均的过拟合评估 ---
                       if summary_data_console.get('num_folds_or_runs', 0) > 1:
                        avg_train_acc_print = summary_data_console.get('avg_train_accuracy_all_folds')
                       avg_train_brier_print = summary_data_console.get('avg_train_brier_all_folds')
                       overfit_assessment_avg_print = summary_data_console.get('overfitting_assessment_avg_folds', '未知')
    
                       avg_train_acc_str_print = f"{avg_train_acc_print:.4f}" if isinstance(avg_train_acc_print, float) else str(avg_train_acc_print)
                       avg_train_brier_str_print = f"{avg_train_brier_print:.4f}" if isinstance(avg_train_brier_print, float) else str(avg_train_brier_print)

                       print(f"  多折平均训练集性能: Acc={avg_train_acc_str_print}, Brier={avg_train_brier_str_print}")
                       print(f"  过拟合评估 (多折平均): {overfit_assessment_avg_print}")
# --- 新增结束 ---





                elif 'val_accuracy' in summary_data_console: 
                    val_acc_meta_con = summary_data_console.get('val_accuracy'); val_loss_meta_con = summary_data_console.get('val_logloss')
                    # FIXED: 使用修正后的f-string格式
                    val_acc_meta_con_str = f"{val_acc_meta_con:.4f}" if isinstance(val_acc_meta_con, float) else str(val_acc_meta_con)
                    val_loss_meta_con_str = f"{val_loss_meta_con:.4f}" if isinstance(val_loss_meta_con, float) else str(val_loss_meta_con)
                    print(f"  验证集 (元模型): Acc={val_acc_meta_con_str}, LogLoss={val_loss_meta_con_str}")
                    report_dict_meta_con = summary_data_console.get('val_classification_report_dict')
                    if isinstance(report_dict_meta_con, dict):
                        print(f"  验证集分类报告 (元模型):")
                        for class_label_meta, metrics_meta in report_dict_meta_con.items():
                            if isinstance(metrics_meta, dict): 
                                print(f"    {class_label_meta}: P={metrics_meta.get('precision','N/A'):.3f} R={metrics_meta.get('recall','N/A'):.3f} F1={metrics_meta.get('f1-score','N/A'):.3f} (Sup: {metrics_meta.get('support','N/A')})")
                        print(f"    Acc(overall): {report_dict_meta_con.get('accuracy','N/A'):.4f}")
            else: print(f"\n--- 目标 {target_key_console.replace('_console_summary','')} 无有效总结数据 ---")
        print(f"\n{'-'*35} 汇总报告结束 {'-'*35}")

    except (ConnectionError, ImportError, TypeError, ValueError, Exception) as e_global_train_outer_main:
        error_type_train_outer = type(e_global_train_outer_main).__name__
        error_msg_train_outer = f"{error_type_train_outer}: {e_global_train_outer_main}"
        print(f"!!! 训练流程严重错误 ({error_type_train_outer}) !!!"); traceback.print_exc()
        if _main_root and _main_root.winfo_exists():
            gui.update_gui_safe(gui.update_status, f"训练严重错误: {error_type_train_outer}", "error")
            if _main_root: messagebox.showerror("严重错误", f"训练流程失败:\n{error_msg_train_outer}\n请检查日志。")
    finally:
        app_state.set_training_in_progress(False)
        if _main_root and _main_root.winfo_exists():
            gui.update_gui_safe(gui.set_button_state,"train_any",tk.NORMAL)
            gui.update_gui_safe(gui.set_button_state,"predict",tk.NORMAL)
            gui.update_gui_safe(gui.reset_progress_bar)
        print(f"===== 训练/筛选/优化流程执行结束{filter_desc} =====")





# 注意：train_meta_model 函数已移至 prediction.py 模块
# 在 run_training_pipeline 中已经正确调用 prediction.train_meta_model

# --- 新的回调函数 ---
def start_training_all():
    """回调函数，用于启动所有目标的训练流程。"""
    if training_in_progress_flag.is_set():
        messagebox.showwarning("忙碌", "训练已在进行中。")
        return
    confirm_msg = ("即将开始训练所有目标的流程。\n"
                   "现有的相关模型、Scaler等文件将被覆盖。\n"
                   "此过程可能需要较长时间。\n\n确定开始吗？")
    if not messagebox.askyesno("确认训练所有目标", confirm_msg):
        return

    # 更新所有目标的GUI显示
    if isinstance(config.PREDICTION_TARGETS, list):
        for target_item in config.PREDICTION_TARGETS:
            if isinstance(target_item, dict) and 'name' in target_item:
                tname = target_item['name']
                gui.update_gui_safe(gui.update_prediction_display, tname, f"准备训练 {tname}...", config.NEUTRAL_COLOR)
                gui.update_gui_safe(gui.update_labelframe_text, tname, f"目标: {tname} (准备中...)")

    gui.update_gui_safe(gui.set_button_state, "train_any", tk.DISABLED) # 禁用所有训练按钮
    gui.update_gui_safe(gui.set_button_state, "predict", tk.DISABLED)
    app_state.set_training_in_progress(True)
    gui.update_gui_safe(gui.update_status, "开始训练所有目标...", "neutral")
    threading.Thread(target=run_training_pipeline, args=(None,), daemon=True).start() # None 表示无过滤器

def start_training_up_only():
    """回调函数，用于仅启动 UP_ONLY 目标的训练流程。"""
    if training_in_progress_flag.is_set():
        messagebox.showwarning("忙碌", "训练已在进行中。")
        return
    confirm_msg = ("即将开始仅训练“上涨 (UP_ONLY)”类型目标的流程。\n"
                   "现有的相关模型、Scaler等文件将被覆盖。\n"
                   "此过程可能需要较长时间。\n\n确定开始吗？")
    if not messagebox.askyesno("确认训练上涨目标", confirm_msg):
        return

    # 更新相关目标的GUI显示
    if isinstance(config.PREDICTION_TARGETS, list):
        for target_item in config.PREDICTION_TARGETS:
            if isinstance(target_item, dict) and target_item.get('target_variable_type', '').upper() == "UP_ONLY":
                tname = target_item['name']
                gui.update_gui_safe(gui.update_prediction_display, tname, f"准备训练 {tname}...", config.NEUTRAL_COLOR)
                gui.update_gui_safe(gui.update_labelframe_text, tname, f"目标: {tname} (准备中...)")

    gui.update_gui_safe(gui.set_button_state, "train_any", tk.DISABLED)
    gui.update_gui_safe(gui.set_button_state, "predict", tk.DISABLED)
    app_state.set_training_in_progress(True)
    gui.update_gui_safe(gui.update_status, "开始仅训练上涨目标...", "neutral")
    threading.Thread(target=run_training_pipeline, args=("UP_ONLY",), daemon=True).start()

def start_training_down_only():
    """回调函数，用于仅启动 DOWN_ONLY 目标的训练流程。"""
    if training_in_progress_flag.is_set():
        messagebox.showwarning("忙碌", "训练已在进行中。")
        return
    confirm_msg = ("即将开始仅训练“下跌 (DOWN_ONLY)”类型目标的流程。\n"
                   "现有的相关模型、Scaler等文件将被覆盖。\n"
                   "此过程可能需要较长时间。\n\n确定开始吗？")
    if not messagebox.askyesno("确认训练下跌目标", confirm_msg):
        return

    # 更新相关目标的GUI显示
    if isinstance(config.PREDICTION_TARGETS, list):
        for target_item in config.PREDICTION_TARGETS:
            if isinstance(target_item, dict) and target_item.get('target_variable_type', '').upper() == "DOWN_ONLY":
                tname = target_item['name']
                gui.update_gui_safe(gui.update_prediction_display, tname, f"准备训练 {tname}...", config.NEUTRAL_COLOR)
                gui.update_gui_safe(gui.update_labelframe_text, tname, f"目标: {tname} (准备中...)")

    gui.update_gui_safe(gui.set_button_state, "train_any", tk.DISABLED)
    gui.update_gui_safe(gui.set_button_state, "predict", tk.DISABLED)
    app_state.set_training_in_progress(True)
    gui.update_gui_safe(gui.update_status, "开始仅训练下跌目标...", "neutral")
    threading.Thread(target=run_training_pipeline, args=("DOWN_ONLY",), daemon=True).start()

def start_training_meta_model_only():
    """回调函数，用于独立训练元模型。"""
    if training_in_progress_flag.is_set():
        messagebox.showwarning("忙碌", "训练已在进行中。")
        return

    # 检查元模型是否启用
    if not getattr(config, 'ENABLE_META_MODEL_TRAINING', True):
        messagebox.showwarning("元模型未启用", "元模型训练在配置中未启用。请检查配置文件。")
        return

    confirm_msg = ("即将开始独立训练元模型。\n"
                   "此操作需要基础模型已经训练完成且预测数据可用。\n"
                   "现有的元模型文件将被覆盖。\n"
                   "此过程可能需要较长时间。\n\n确定开始吗？")
    if not messagebox.askyesno("确认训练元模型", confirm_msg):
        return

    # 更新元模型相关的GUI显示
    meta_model_display_name = getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', '元模型')
    gui.update_gui_safe(gui.update_prediction_display, meta_model_display_name, f"准备训练元模型...", config.NEUTRAL_COLOR)
    gui.update_gui_safe(gui.update_labelframe_text, meta_model_display_name, f"目标: {meta_model_display_name} (准备中...)")

    gui.update_gui_safe(gui.set_button_state, "train_any", tk.DISABLED)
    gui.update_gui_safe(gui.set_button_state, "train_meta", tk.DISABLED)
    gui.update_gui_safe(gui.set_button_state, "predict", tk.DISABLED)
    app_state.set_training_in_progress(True)
    gui.update_gui_safe(gui.update_status, "开始训练元模型...", "neutral")
    threading.Thread(target=run_meta_model_training_pipeline, daemon=True).start()

def add_meta_feature_engineering(X_meta_df, base_models_for_meta):
    """
    为元模型添加特征工程，严格防止数据泄露

    参数:
    - X_meta_df: 包含基础模型OOF预测概率的DataFrame
    - base_models_for_meta: 基础模型名称列表

    返回:
    - 添加了特征工程的DataFrame
    """
    print("  开始添加元模型特征工程...")

    # 创建副本以避免修改原始数据
    X_enhanced = X_meta_df.copy()

    # 确保数据按时间顺序排列（基于索引）
    X_enhanced = X_enhanced.sort_index()

    # 识别UP和DOWN模型的概率列
    up_prob_cols = [col for col in X_enhanced.columns if 'UP' in col.upper()]
    down_prob_cols = [col for col in X_enhanced.columns if 'DOWN' in col.upper()]

    print(f"    识别到UP模型概率列: {up_prob_cols}")
    print(f"    识别到DOWN模型概率列: {down_prob_cols}")

    # 获取特征工程配置
    fe_config = getattr(config, 'META_MODEL_FEATURE_ENGINEERING_CONFIG', {})
    print(f"    特征工程配置: {fe_config}")

    # 1. 基础模型概率差异 (Probability Difference)
    if len(up_prob_cols) >= 1 and len(down_prob_cols) >= 1:
        # 如果有多个UP或DOWN模型，使用第一个或平均值
        if len(up_prob_cols) == 1:
            up_prob = X_enhanced[up_prob_cols[0]]
        else:
            up_prob = X_enhanced[up_prob_cols].mean(axis=1)

        if len(down_prob_cols) == 1:
            down_prob = X_enhanced[down_prob_cols[0]]
        else:
            down_prob = X_enhanced[down_prob_cols].mean(axis=1)

        # 1. 基础模型概率差异 (Probability Difference)
        if fe_config.get('enable_prob_diff', True):
            X_enhanced['meta_prob_diff_up_vs_down'] = up_prob - down_prob
            print(f"    ✓ 添加特征: meta_prob_diff_up_vs_down (范围: {X_enhanced['meta_prob_diff_up_vs_down'].min():.3f} 到 {X_enhanced['meta_prob_diff_up_vs_down'].max():.3f})")
        else:
            print("    ⚠️ 跳过概率差异特征 (配置禁用)")

        # 2. 基础模型概率总和 (Combined Conviction)
        if fe_config.get('enable_prob_sum', True):
            X_enhanced['meta_prob_sum_up_down'] = up_prob + down_prob
            print(f"    ✓ 添加特征: meta_prob_sum_up_down (范围: {X_enhanced['meta_prob_sum_up_down'].min():.3f} 到 {X_enhanced['meta_prob_sum_up_down'].max():.3f})")
        else:
            print("    ⚠️ 跳过概率总和特征 (配置禁用)")
    else:
        print("    ! 警告: 未找到足够的UP/DOWN模型概率列，跳过概率差异和总和特征")

    # 识别基础模型概率列（支持多种命名格式）
    prob_cols = []
    for col in X_enhanced.columns:
        if (col.startswith('prob_') or
            col.startswith('oof_proba_') or
            'prob' in col.lower() or
            any(base_name in col for base_name in base_models_for_meta)):
            prob_cols.append(col)

    print(f"    识别到概率列用于滞后和变化特征: {prob_cols}")

    # 3. 基础模型概率的1期滞后项 (Lagged Probabilities)
    if fe_config.get('enable_lag_features', True):
        lag_periods = fe_config.get('lag_periods', [1])
        for col in prob_cols:
            for lag_period in lag_periods:
                lag_col_name = f"meta_lag{lag_period}_{col}"
                X_enhanced[lag_col_name] = X_enhanced[col].shift(lag_period)

                # 处理NaN值 - 用该列的中位数填充（更稳健）
                if X_enhanced[lag_col_name].isnull().any():
                    fill_value = X_enhanced[col].median()
                    if pd.isna(fill_value):  # 如果中位数也是NaN，使用0.5
                        fill_value = 0.5
                    X_enhanced[lag_col_name] = X_enhanced[lag_col_name].fillna(fill_value)

                    print(f"    ✓ 添加滞后特征: {lag_col_name} (填充值: {fill_value:.3f})")
    else:
        print("    ⚠️ 跳过滞后特征 (配置禁用)")

    # 4. 基础模型预测概率的1期变化 (Probability Change) - 高优先级优化：默认禁用
    if fe_config.get('enable_change_features', False):
        change_method = fe_config.get('change_feature_method', 'diff')
        for col in prob_cols:
            change_col_name = f"meta_change1_{col}"

            if change_method == 'diff':
                X_enhanced[change_col_name] = X_enhanced[col].diff(1)
            elif change_method == 'pct_change':
                X_enhanced[change_col_name] = X_enhanced[col].pct_change(1)
            elif change_method == 'ratio':
                X_enhanced[change_col_name] = X_enhanced[col] / X_enhanced[col].shift(1) - 1
            else:
                X_enhanced[change_col_name] = X_enhanced[col].diff(1)

            # 处理NaN值 - 用0填充（表示无变化）
            if X_enhanced[change_col_name].isnull().any():
                X_enhanced[change_col_name] = X_enhanced[change_col_name].fillna(0.0)

            print(f"    ✓ 添加变化特征: {change_col_name} (方法: {change_method}, 范围: {X_enhanced[change_col_name].min():.3f} 到 {X_enhanced[change_col_name].max():.3f})")
    else:
        print("    ⚠️ 跳过变化特征 (高优先级优化：配置禁用，因为SHAP分析显示这些特征无效)")

    # 5. 添加动态全局市场状态特征（修复SHAP分析问题）
    print("    添加动态全局市场状态特征...")
    try:
        # 获取全局市场状态配置
        global_config = getattr(config, 'GLOBAL_MARKET_STATE_CONFIG', {})
        symbol = global_config.get('symbol', 'BTCUSDT')
        timeframe = global_config.get('timeframe', '1h')

        # 检查是否有时间戳索引用于动态计算
        if hasattr(X_enhanced, 'index') and len(X_enhanced.index) > 0:
            print(f"    计算动态全局特征 (时间框架: {timeframe}, 样本数: {len(X_enhanced)})")

            # 为每个时间点计算全局市场状态
            global_features_dict = {}

            # 初始化全局特征列 (包含新的EMA衍生特征)
            global_feature_names = [
                'global_trend_signal', 'global_trend_strength', 'global_adx',
                'global_pdi', 'global_mdi', 'global_ema_short', 'global_ema_long',
                # 🎯 新增：高信息量EMA衍生特征
                'global_ema_diff', 'global_ema_diff_pct', 'global_price_ema_distance_pct',
                'global_ema_slope_short', 'global_ema_slope_long', 'global_ema_cross_signal',
                'global_ema_divergence',
                'global_volatility_level', 'global_atr', 'global_atr_percent'
            ]

            for feature_name in global_feature_names:
                global_features_dict[feature_name] = []

            # 使用更短的时间框架来确保动态变化
            dynamic_timeframe = '15m'  # 使用与基础模型相同的时间框架
            print(f"    使用动态时间框架: {dynamic_timeframe} (而非配置的 {timeframe})")

            # 获取全局市场数据
            global binance_client
            if binance_client is not None:
                from src.core import prediction

                # 获取足够的历史数据用于计算全局指标
                data_limit = global_config.get('data_fetch_limit', 200) + len(X_enhanced) + 100
                df_global_full = prediction.fetch_binance_history(
                    binance_client, symbol, dynamic_timeframe, limit=data_limit
                )

                if df_global_full is not None and not df_global_full.empty and len(df_global_full) > 50:
                    print(f"    获取全局数据: {df_global_full.shape}")

                    # 🚨 优化：为每个样本时间点计算全局特征（添加性能控制）
                    sample_count = 0
                    total_samples = len(X_enhanced)
                    print(f"    开始处理 {total_samples} 个样本的全局特征...")

                    # 添加进度控制，避免过度频繁的计算
                    progress_interval = max(100, total_samples // 20)  # 最多显示20次进度

                    for idx in X_enhanced.index:
                        try:
                            # 获取当前样本的时间戳
                            if hasattr(idx, 'to_pydatetime'):
                                current_time = idx.to_pydatetime()
                            else:
                                current_time = pd.to_datetime(idx)

                            # 找到对应时间点之前的全局数据
                            mask = df_global_full.index <= current_time
                            df_global_subset = df_global_full[mask]

                            # 🚨 修复：动态调整最小数据要求
                            min_required = max(100, global_config.get('trend_adx_period', 14) * 4)  # 至少4倍ADX周期
                            if len(df_global_subset) >= min_required:  # 确保有足够数据计算指标
                                # 计算该时间点的全局市场状态
                                global_state = calculate_global_market_state_for_timepoint(
                                    df_global_subset, global_config
                                )

                                # 添加到特征字典
                                for feature_name in global_feature_names:
                                    global_features_dict[feature_name].append(
                                        global_state.get(feature_name, 0.0)
                                    )
                            else:
                                # 数据不足，使用默认值
                                for feature_name in global_feature_names:
                                    if 'signal' in feature_name or 'strength' in feature_name or 'level' in feature_name:
                                        global_features_dict[feature_name].append(0)
                                    else:
                                        global_features_dict[feature_name].append(0.0)

                            sample_count += 1
                            # 🚨 优化进度显示频率
                            if sample_count % progress_interval == 0 or sample_count == total_samples:
                                progress_pct = (sample_count / total_samples) * 100
                                print(f"    处理进度: {sample_count}/{total_samples} ({progress_pct:.1f}%)")

                        except Exception as e_sample:
                            # 🚨 减少错误日志频率，避免日志洪水
                            if sample_count % 1000 == 0:  # 只在每1000个样本时打印错误
                                print(f"    警告: 处理样本时出错 (样本#{sample_count}): {e_sample}")
                            # 使用默认值
                            for feature_name in global_feature_names:
                                if 'signal' in feature_name or 'strength' in feature_name or 'level' in feature_name:
                                    global_features_dict[feature_name].append(0)
                                else:
                                    global_features_dict[feature_name].append(0.0)
                            sample_count += 1

                    # 将计算的全局特征添加到X_enhanced
                    for feature_name in global_feature_names:
                        if len(global_features_dict[feature_name]) == len(X_enhanced):
                            X_enhanced[feature_name] = global_features_dict[feature_name]
                            feature_values = global_features_dict[feature_name]
                            unique_values = len(set(feature_values))
                            value_range = f"{min(feature_values):.3f} 到 {max(feature_values):.3f}"
                            print(f"    ✓ 添加动态全局特征: {feature_name} (唯一值: {unique_values}, 范围: {value_range})")
                        else:
                            print(f"    ! 警告: {feature_name} 特征长度不匹配，使用默认值")
                            default_val = 0 if 'signal' in feature_name or 'strength' in feature_name or 'level' in feature_name else 0.0
                            X_enhanced[feature_name] = default_val

                else:
                    print("    ! 警告: 无法获取足够的全局市场数据，使用默认值")
                    # 使用默认值
                    for feature_name in global_feature_names:
                        default_val = 0 if 'signal' in feature_name or 'strength' in feature_name or 'level' in feature_name else 0.0
                        X_enhanced[feature_name] = default_val
                        print(f"    ✓ 添加默认全局特征: {feature_name} = {default_val}")
            else:
                print("    ! 警告: binance_client未初始化，使用默认全局特征")
                for feature_name in global_feature_names:
                    default_val = 0 if 'signal' in feature_name or 'strength' in feature_name or 'level' in feature_name else 0.0
                    X_enhanced[feature_name] = default_val
                    print(f"    ✓ 添加默认全局特征: {feature_name} = {default_val}")
        else:
            print("    ! 警告: 无法获取时间索引，使用静态全局特征")
            # 回退到原始逻辑
            if binance_client is not None:
                from src.core import prediction
                global_market_state = prediction.calculate_global_market_state(binance_client)
                for key, value in global_market_state.items():
                    if key != 'global_status':
                        X_enhanced[key] = value
                        print(f"    ✓ 添加静态全局特征: {key} = {value}")
            else:
                # 使用默认值 (包含新的EMA衍生特征)
                default_global_features = {
                    'global_trend_signal': 0, 'global_trend_strength': 0, 'global_adx': 0.0,
                    'global_pdi': 0.0, 'global_mdi': 0.0, 'global_ema_short': 0.0, 'global_ema_long': 0.0,
                    # 🎯 新增：EMA衍生特征默认值
                    'global_ema_diff': 0.0, 'global_ema_diff_pct': 0.0, 'global_price_ema_distance_pct': 0.0,
                    'global_ema_slope_short': 0.0, 'global_ema_slope_long': 0.0, 'global_ema_cross_signal': 0,
                    'global_ema_divergence': 0.0,
                    'global_volatility_level': 0, 'global_atr': 0.0, 'global_atr_percent': 0.0
                }
                for key, value in default_global_features.items():
                    X_enhanced[key] = value
                    print(f"    ✓ 添加默认全局特征: {key} = {value}")

    except Exception as e:
        print(f"    ! 警告: 添加动态全局市场状态特征时出错: {e}")
        import traceback
        traceback.print_exc(limit=3)
        # 使用默认值 (包含新的EMA衍生特征)
        default_global_features = {
            'global_trend_signal': 0, 'global_trend_strength': 0, 'global_adx': 0.0,
            'global_pdi': 0.0, 'global_mdi': 0.0, 'global_ema_short': 0.0, 'global_ema_long': 0.0,
            # 🎯 新增：EMA衍生特征默认值
            'global_ema_diff': 0.0, 'global_ema_diff_pct': 0.0, 'global_price_ema_distance_pct': 0.0,
            'global_ema_slope_short': 0.0, 'global_ema_slope_long': 0.0, 'global_ema_cross_signal': 0,
            'global_ema_divergence': 0.0,
            'global_volatility_level': 0, 'global_atr': 0.0, 'global_atr_percent': 0.0
        }
        for key, value in default_global_features.items():
            X_enhanced[key] = value

    # 验证没有NaN值
    nan_count = X_enhanced.isnull().sum().sum()
    if nan_count > 0:
        print(f"    ! 警告: 特征工程后仍有 {nan_count} 个NaN值")
        # 显示哪些列有NaN
        nan_cols = X_enhanced.columns[X_enhanced.isnull().any()].tolist()
        print(f"    包含NaN的列: {nan_cols}")

        # 用安全值填充剩余的NaN
        for col in nan_cols:
            if 'prob' in col.lower():
                X_enhanced[col] = X_enhanced[col].fillna(0.5)
            else:
                X_enhanced[col] = X_enhanced[col].fillna(0.0)

    print(f"  ✓ 特征工程完成，从 {X_meta_df.shape[1]} 个特征扩展到 {X_enhanced.shape[1]} 个特征")
    print(f"    新增特征: {[col for col in X_enhanced.columns if col not in X_meta_df.columns]}")

    return X_enhanced

def calculate_global_market_state_for_timepoint(df_global_data, global_config):
    """
    为特定时间点计算全局市场状态指标

    Args:
        df_global_data: 截止到特定时间点的全局市场数据
        global_config: 全局市场状态配置

    Returns:
        dict: 包含全局市场状态指标的字典
    """
    try:
        # 🚨 修复：增加调试信息和更严格的数据验证
        debug_enabled = getattr(config, 'GLOBAL_ADX_DEBUG_VERBOSE', False)

        if debug_enabled:
            print(f"    [ADX Debug] 输入数据长度: {len(df_global_data) if df_global_data is not None else 'None'}")
            if df_global_data is not None and not df_global_data.empty:
                print(f"    [ADX Debug] 数据时间范围: {df_global_data.index[0]} 到 {df_global_data.index[-1]}")

        # 🚨 修复：降低最小数据要求，但确保足够计算ADX
        min_data_required = max(50, global_config.get('trend_adx_period', 14) * 3)  # 至少3倍ADX周期

        if df_global_data is None or df_global_data.empty or len(df_global_data) < min_data_required:
            if debug_enabled:
                print(f"    [ADX Debug] 数据不足，需要至少 {min_data_required} 行，实际 {len(df_global_data) if df_global_data is not None else 0} 行")
            return {
                'global_trend_signal': 0,
                'global_trend_strength': 0,
                'global_adx': 0.0,
                'global_pdi': 0.0,
                'global_mdi': 0.0,
                'global_ema_short': 0.0,
                'global_ema_long': 0.0,
                # 🎯 新增：EMA衍生特征默认值
                'global_ema_diff': 0.0,
                'global_ema_diff_pct': 0.0,
                'global_price_ema_distance_pct': 0.0,
                'global_ema_slope_short': 0.0,
                'global_ema_slope_long': 0.0,
                'global_ema_cross_signal': 0,
                'global_ema_divergence': 0.0,
                'global_volatility_level': 0,
                'global_atr': 0.0,
                'global_atr_percent': 0.0
            }

        # 初始化返回值 (包含新的EMA衍生特征)
        result = {
            'global_trend_signal': 0,
            'global_trend_strength': 0,
            'global_adx': 0.0,
            'global_pdi': 0.0,
            'global_mdi': 0.0,
            'global_ema_short': 0.0,
            'global_ema_long': 0.0,
            # 🎯 新增：EMA衍生特征默认值
            'global_ema_diff': 0.0,
            'global_ema_diff_pct': 0.0,
            'global_price_ema_distance_pct': 0.0,
            'global_ema_slope_short': 0.0,
            'global_ema_slope_long': 0.0,
            'global_ema_cross_signal': 0,
            'global_ema_divergence': 0.0,
            'global_volatility_level': 0,
            'global_atr': 0.0,
            'global_atr_percent': 0.0
        }

        # 🚨 修复：计算全局趋势指标 - 支持同时计算ADX和EMA
        trend_indicator_type = global_config.get('trend_indicator_type', 'adx')

        # 🔧 ADX计算 (当trend_indicator_type为'adx'或'both'时计算)
        if trend_indicator_type in ['adx', 'both']:
            adx_period = global_config.get('trend_adx_period', 14)
            # 🚨 修复：降低ADX阈值，使其更容易触发
            adx_strength_threshold = global_config.get('trend_adx_strength_threshold', 20)  # 从25降到20
            adx_threshold = global_config.get('trend_adx_threshold', 15)  # 从20降到15

            try:
                import pandas_ta as pta

                # 🚨 修复：确保数据质量
                if debug_enabled:
                    print(f"    [ADX Debug] 开始计算ADX，周期={adx_period}, 强度阈值={adx_strength_threshold}, 一般阈值={adx_threshold}")
                    print(f"    [ADX Debug] 价格范围: High={df_global_data['high'].iloc[-1]:.2f}, Low={df_global_data['low'].iloc[-1]:.2f}, Close={df_global_data['close'].iloc[-1]:.2f}")

                adx_df = pta.adx(df_global_data['high'], df_global_data['low'], df_global_data['close'],
                               length=adx_period, lensig=adx_period)

                if adx_df is not None and not adx_df.empty:
                    adx_col, pdi_col, mdi_col = f'ADX_{adx_period}', f'DMP_{adx_period}', f'DMN_{adx_period}'

                    if debug_enabled:
                        print(f"    [ADX Debug] ADX计算成功，列名: {adx_df.columns.tolist()}")
                        print(f"    [ADX Debug] 期望列名: {[adx_col, pdi_col, mdi_col]}")

                    if all(c in adx_df.columns for c in [adx_col, pdi_col, mdi_col]):
                        # 获取最新的非NaN值
                        adx_values = adx_df[adx_col].dropna()
                        pdi_values = adx_df[pdi_col].dropna()
                        mdi_values = adx_df[mdi_col].dropna()

                        if not adx_values.empty and not pdi_values.empty and not mdi_values.empty:
                            result['global_adx'] = float(adx_values.iloc[-1])
                            result['global_pdi'] = float(pdi_values.iloc[-1])
                            result['global_mdi'] = float(mdi_values.iloc[-1])

                            # 🚨 修复：ADX趋势信号和强度计算，增加调试信息
                            if debug_enabled:
                                print(f"    [ADX Debug] ADX值: {result['global_adx']:.3f}, PDI: {result['global_pdi']:.3f}, MDI: {result['global_mdi']:.3f}")

                            # 当trend_indicator_type为'adx'时，ADX主导趋势信号
                            if trend_indicator_type == 'adx':
                                if result['global_adx'] > adx_threshold:
                                    result['global_trend_signal'] = 1 if result['global_pdi'] > result['global_mdi'] else -1
                                    if debug_enabled:
                                        print(f"    [ADX Debug] ADX趋势信号: {result['global_trend_signal']} (ADX={result['global_adx']:.3f} > {adx_threshold})")

                            # 🚨 修复：趋势强度计算逻辑
                            if result['global_adx'] > adx_strength_threshold:
                                result['global_trend_strength'] = 1
                                if debug_enabled:
                                    print(f"    [ADX Debug] 强趋势检测: ADX={result['global_adx']:.3f} > {adx_strength_threshold}")
                            else:
                                if debug_enabled:
                                    print(f"    [ADX Debug] 弱趋势: ADX={result['global_adx']:.3f} <= {adx_strength_threshold}")
                        else:
                            if debug_enabled:
                                print(f"    [ADX Debug] ADX数据为空或全为NaN")
                    else:
                        if debug_enabled:
                            print(f"    [ADX Debug] 缺少期望的ADX列")
                else:
                    if debug_enabled:
                        print(f"    [ADX Debug] ADX计算返回空结果")
            except Exception as e_adx:
                if debug_enabled:
                    print(f"    [ADX Debug] ADX计算异常: {e_adx}")
                pass  # 保持默认值

        # 🔧 EMA交叉计算 (当trend_indicator_type为'ema_cross'或'both'时计算)
        if trend_indicator_type in ['ema_cross', 'both']:
            ema_short_period = global_config.get('trend_ema_short_period', 21)
            ema_long_period = global_config.get('trend_ema_long_period', 50)

            try:
                import pandas_ta as pta
                ema_short = pta.ema(df_global_data['close'], length=ema_short_period)
                ema_long = pta.ema(df_global_data['close'], length=ema_long_period)

                if ema_short is not None and ema_long is not None and not ema_short.empty and not ema_long.empty:
                    result['global_ema_short'] = float(ema_short.iloc[-1]) if not pd.isna(ema_short.iloc[-1]) else 0.0
                    result['global_ema_long'] = float(ema_long.iloc[-1]) if not pd.isna(ema_long.iloc[-1]) else 0.0

                    # 当trend_indicator_type为'ema_cross'或'both'时，EMA影响趋势信号
                    if trend_indicator_type in ['ema_cross', 'both']:
                        if result['global_ema_short'] > result['global_ema_long']:
                            result['global_trend_signal'] = 1
                        elif result['global_ema_short'] < result['global_ema_long']:
                            result['global_trend_signal'] = -1
            except Exception as e_ema:
                pass  # 保持默认值

        # 🎯 优化：计算增强的EMA特征集 (与主函数保持一致)
        ema_short_period = global_config.get('trend_ema_short_period', 21)
        ema_long_period = global_config.get('trend_ema_long_period', 50)
        ema_slope_period = global_config.get('ema_slope_period', 5)  # EMA斜率计算周期

        # 验证数据长度是否足够计算EMA和衍生特征
        min_required_length = max(ema_short_period, ema_long_period) * 2 + ema_slope_period
        if len(df_global_data) >= min_required_length:
            try:
                import pandas_ta as pta

                # 验证输入数据
                close_prices = df_global_data['close']
                if close_prices.isnull().any():
                    close_prices = close_prices.fillna(method='ffill')

                current_price = float(close_prices.iloc[-1])

                # 计算基础EMA
                ema_short = pta.ema(close_prices, length=ema_short_period)
                ema_long = pta.ema(close_prices, length=ema_long_period)

                if ema_short is not None and ema_long is not None and not ema_short.empty and not ema_long.empty:
                    # 获取最新的非NaN EMA值
                    latest_ema_short = ema_short.dropna().iloc[-1] if not ema_short.dropna().empty else 0.0
                    latest_ema_long = ema_long.dropna().iloc[-1] if not ema_long.dropna().empty else 0.0

                    # 基础EMA特征
                    result['global_ema_short'] = float(latest_ema_short)
                    result['global_ema_long'] = float(latest_ema_long)

                    # 🎯 新增：高信息量EMA衍生特征
                    if latest_ema_long > 0:
                        # 1. EMA差值和百分比
                        ema_diff = latest_ema_short - latest_ema_long
                        result['global_ema_diff'] = float(ema_diff)
                        result['global_ema_diff_pct'] = float((ema_diff / latest_ema_long) * 100)

                        # 2. 价格与EMA距离百分比 (使用短期EMA作为参考)
                        result['global_price_ema_distance_pct'] = float((current_price - latest_ema_short) / latest_ema_short * 100)

                        # 3. EMA斜率计算 (变化率)
                        if len(ema_short.dropna()) >= ema_slope_period and len(ema_long.dropna()) >= ema_slope_period:
                            ema_short_clean = ema_short.dropna()
                            ema_long_clean = ema_long.dropna()

                            # 计算斜率 (最近N期的平均变化率)
                            short_slope = (ema_short_clean.iloc[-1] - ema_short_clean.iloc[-ema_slope_period]) / ema_slope_period
                            long_slope = (ema_long_clean.iloc[-1] - ema_long_clean.iloc[-ema_slope_period]) / ema_slope_period

                            result['global_ema_slope_short'] = float(short_slope / latest_ema_short * 100)  # 标准化为百分比
                            result['global_ema_slope_long'] = float(long_slope / latest_ema_long * 100)

                        # 4. EMA交叉信号检测
                        if len(ema_short.dropna()) >= 2 and len(ema_long.dropna()) >= 2:
                            ema_short_clean = ema_short.dropna()
                            ema_long_clean = ema_long.dropna()

                            # 当前和前一期的EMA关系
                            current_above = ema_short_clean.iloc[-1] > ema_long_clean.iloc[-1]
                            previous_above = ema_short_clean.iloc[-2] > ema_long_clean.iloc[-2]

                            if current_above and not previous_above:
                                result['global_ema_cross_signal'] = 1  # 金叉
                            elif not current_above and previous_above:
                                result['global_ema_cross_signal'] = -1  # 死叉
                            else:
                                result['global_ema_cross_signal'] = 0  # 无交叉

                        # 5. 价格与EMA背离程度
                        # 计算价格趋势与EMA趋势的背离
                        if len(close_prices) >= ema_slope_period:
                            price_slope = (close_prices.iloc[-1] - close_prices.iloc[-ema_slope_period]) / ema_slope_period
                            price_slope_pct = price_slope / close_prices.iloc[-ema_slope_period] * 100
                            ema_slope_pct = result['global_ema_slope_short']

                            # 背离程度 = 价格斜率 - EMA斜率
                            result['global_ema_divergence'] = float(price_slope_pct - ema_slope_pct)

                    # 调试输出 (仅在启用时)
                    if getattr(config, 'EMA_DEBUG_VERBOSE', False):
                        # 日志频率控制
                        if not hasattr(calculate_global_market_state_for_timepoint, '_ema_call_count'):
                            calculate_global_market_state_for_timepoint._ema_call_count = 0
                        calculate_global_market_state_for_timepoint._ema_call_count += 1

                        if calculate_global_market_state_for_timepoint._ema_call_count % 100 == 1:
                            print(f"    [TimePointEMA] 数据长度: {len(df_global_data)}")
                            print(f"    [TimePointEMA] EMA短期: {latest_ema_short:.2f}, EMA长期: {latest_ema_long:.2f}")
                            print(f"    [TimePointEMA] EMA差值: {result['global_ema_diff']:.2f}, 差值%: {result['global_ema_diff_pct']:.2f}%")
                            print(f"    [TimePointEMA] 价格距离%: {result['global_price_ema_distance_pct']:.2f}%")
                            print(f"    [TimePointEMA] 交叉信号: {result['global_ema_cross_signal']}, 背离: {result['global_ema_divergence']:.2f}")
                else:
                    if getattr(config, 'EMA_DEBUG_VERBOSE', False):
                        print(f"    ⚠️ [TimePointEMA] EMA计算失败，使用默认值")
            except Exception as e:
                if getattr(config, 'EMA_DEBUG_VERBOSE', False):
                    print(f"    ❌ [TimePointEMA] EMA计算异常: {e}")
        else:
            if getattr(config, 'EMA_DEBUG_VERBOSE', False):
                print(f"    ⚠️ [TimePointEMA] 数据不足: {len(df_global_data)} < {min_required_length}")



        # 计算全局波动率指标
        try:
            atr_period = global_config.get('volatility_atr_period', 14)
            min_atr_percent = global_config.get('volatility_min_atr_percent', 0.08)
            max_atr_percent = global_config.get('volatility_max_atr_percent', 1.5)

            import pandas_ta as pta
            atr_series = pta.atr(df_global_data['high'], df_global_data['low'], df_global_data['close'], length=atr_period)
            if atr_series is not None and not atr_series.empty and not pd.isna(atr_series.iloc[-1]):
                result['global_atr'] = float(atr_series.iloc[-1])
                close_price = float(df_global_data['close'].iloc[-1])
                result['global_atr_percent'] = (result['global_atr'] / close_price) * 100 if close_price > 0 else 0.0

                if result['global_atr_percent'] < min_atr_percent:
                    result['global_volatility_level'] = 1  # 低波动
                elif result['global_atr_percent'] > max_atr_percent:
                    result['global_volatility_level'] = -1  # 高波动
                else:
                    result['global_volatility_level'] = 0  # 正常波动
        except Exception as e_atr:
            pass  # 保持默认值

        return result

    except Exception as e:
        # 返回默认值 (包含新的EMA衍生特征)
        return {
            'global_trend_signal': 0,
            'global_trend_strength': 0,
            'global_adx': 0.0,
            'global_pdi': 0.0,
            'global_mdi': 0.0,
            'global_ema_short': 0.0,
            'global_ema_long': 0.0,
            # 🎯 新增：EMA衍生特征默认值
            'global_ema_diff': 0.0,
            'global_ema_diff_pct': 0.0,
            'global_price_ema_distance_pct': 0.0,
            'global_ema_slope_short': 0.0,
            'global_ema_slope_long': 0.0,
            'global_ema_cross_signal': 0,
            'global_ema_divergence': 0.0,
            'global_volatility_level': 0,
            'global_atr': 0.0,
            'global_atr_percent': 0.0
        }

def analyze_global_atr_percent_dependencies():
    """分析 global_atr_percent 特征的完整依赖关系"""
    print("🔍 开始分析 global_atr_percent 特征依赖关系...")

    try:
        # 1. 加载元模型和相关数据
        import joblib
        import json
        import pandas as pd
        import numpy as np

        # 加载SHAP分析结果
        shap_file = "meta_model_data/shap_analysis.json"
        if os.path.exists(shap_file):
            with open(shap_file, 'r', encoding='utf-8') as f:
                shap_data = json.load(f)
            print(f"✓ 加载SHAP分析数据: {shap_file}")
        else:
            print(f"⚠️ 未找到SHAP分析文件: {shap_file}")
            return

        # 2. 分析特征重要性
        feature_importance = shap_data.get('feature_importance', [])
        global_atr_percent_importance = None
        global_atr_importance = None

        for feature, importance in feature_importance:
            if feature == 'global_atr_percent':
                global_atr_percent_importance = importance
            elif feature == 'global_atr':
                global_atr_importance = importance

        print(f"\n📊 特征重要性分析:")
        print(f"  global_atr_percent: {global_atr_percent_importance:.4f} (排名第1)")
        print(f"  global_atr: {global_atr_importance:.4f} (排名第2)")
        print(f"  重要性比值: {global_atr_percent_importance/global_atr_importance:.2f}x")

        # 3. 分析配置参数
        config_params = getattr(config, 'GLOBAL_MARKET_STATE_CONFIG', {})
        atr_period = config_params.get('volatility_atr_period', 14)
        min_atr_percent = config_params.get('volatility_min_atr_percent', 0.08)
        max_atr_percent = config_params.get('volatility_max_atr_percent', 1.5)

        print(f"\n⚙️ 配置参数:")
        print(f"  ATR计算周期: {atr_period}")
        print(f"  最小ATR百分比阈值: {min_atr_percent}%")
        print(f"  最大ATR百分比阈值: {max_atr_percent}%")

        # 4. 生成依赖关系图
        dependency_graph = generate_atr_percent_dependency_graph()

        # 5. 分析预测影响
        analyze_atr_percent_prediction_impact()

        # 6. 创建可视化
        create_atr_percent_visualizations()

        print("✅ global_atr_percent 特征依赖关系分析完成")

    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

def generate_atr_percent_dependency_graph():
    """生成 global_atr_percent 的依赖关系图"""

    mermaid_diagram = """
    graph TD
        %% 数据源层
        A[BTCUSDT 15m K线数据] --> B[High/Low/Close价格]

        %% 计算层
        B --> C[ATR计算<br/>pandas_ta.atr<br/>周期=14]
        B --> D[当前收盘价]

        %% 特征生成层
        C --> E[global_atr<br/>原始ATR值]
        C --> F[global_atr_percent<br/>ATR/Close*100]
        D --> F

        %% 波动率分类层
        F --> G{ATR百分比判断}
        G -->|< 0.08%| H[global_volatility_level = 1<br/>低波动]
        G -->|0.08% - 1.5%| I[global_volatility_level = 0<br/>正常波动]
        G -->|> 1.5%| J[global_volatility_level = -1<br/>高波动]

        %% 元模型特征层
        E --> K[元模型特征矩阵]
        F --> K
        H --> K
        I --> K
        J --> K

        %% 其他全局特征
        L[global_trend_signal] --> K
        M[global_adx] --> K
        N[global_pdi/mdi] --> K
        O[global_ema_short/long] --> K

        %% 基础模型特征
        P[oof_proba_BTC_15m_UP] --> K
        Q[oof_proba_BTC_15m_DOWN] --> K
        R[meta_prob_diff/sum] --> K
        S[滞后和变化特征] --> K

        %% 元模型预测层
        K --> T[LightGBM元模型]
        T --> U[三分类预测<br/>UP/DOWN/NEUTRAL]

        %% 决策层
        U --> V[交易信号生成]
        U --> W[凯利公式计算]
        U --> X[模拟盘执行]

        %% 样式定义
        classDef highlight fill:#ff9999,stroke:#333,stroke-width:3px
        classDef important fill:#99ccff,stroke:#333,stroke-width:2px
        classDef calculation fill:#99ff99,stroke:#333,stroke-width:2px

        class F highlight
        class E,G,H,I,J important
        class C,D calculation
    """

    print(f"\n🔗 依赖关系图:")
    print("生成Mermaid图表...")

    # 使用render-mermaid工具生成图表
    try:
        # 尝试使用Mermaid渲染
        print("正在生成Mermaid依赖关系图...")
        print(mermaid_diagram)
    except Exception as e:
        print(f"⚠️ 生成图表时出错: {e}")
        print("显示文本版本:")
        print(mermaid_diagram)

    return mermaid_diagram

def analyze_atr_percent_prediction_impact():
    """分析 global_atr_percent 对预测结果的影响"""
    print(f"\n📈 预测影响分析:")

    # 分析不同ATR百分比范围的影响
    atr_ranges = [
        (0.0, 0.08, "极低波动", "可能导致假突破，降低预测准确性"),
        (0.08, 0.3, "低波动", "趋势信号较弱，适合震荡策略"),
        (0.3, 0.8, "正常波动", "最佳预测环境，信号质量高"),
        (0.8, 1.5, "高波动", "趋势信号强烈，但噪音增加"),
        (1.5, 3.0, "极高波动", "市场恐慌或狂热，信号不稳定")
    ]

    print("  ATR百分比范围对预测的影响:")
    for min_val, max_val, desc, impact in atr_ranges:
        print(f"    {min_val:.2f}% - {max_val:.2f}%: {desc}")
        print(f"      影响: {impact}")

    # 分析与其他特征的交互作用
    print(f"\n  与其他特征的交互作用:")
    interactions = [
        ("global_atr", "强正相关，但百分比形式更能反映相对波动"),
        ("global_volatility_level", "直接决定关系，是ATR百分比的分类版本"),
        ("global_trend_signal", "高波动时趋势信号更可靠"),
        ("global_adx", "ADX高且ATR百分比适中时，预测最准确"),
        ("基础模型概率", "波动率影响基础模型的置信度")
    ]

    for feature, relationship in interactions:
        print(f"    {feature}: {relationship}")

def create_atr_percent_visualizations():
    """创建 global_atr_percent 的可视化图表"""
    print(f"\n📊 创建可视化图表...")

    try:
        # 检查是否有历史数据用于分析
        if not os.path.exists("meta_model_data"):
            print("⚠️ 未找到元模型数据目录，无法创建详细可视化")
            return

        # 创建示例数据用于演示
        if plt is None:
            print("⚠️ matplotlib 不可用，跳过可视化")
            return

        import numpy as np

        # 模拟ATR百分比分布
        np.random.seed(42)
        atr_percent_values = np.random.lognormal(mean=-1.5, sigma=0.8, size=1000)
        atr_percent_values = np.clip(atr_percent_values, 0.01, 5.0)

        # 模拟预测概率
        up_probs = []
        down_probs = []

        for atr in atr_percent_values:
            if atr < 0.08:  # 极低波动
                up_prob = 0.45 + np.random.normal(0, 0.1)
                down_prob = 0.45 + np.random.normal(0, 0.1)
            elif atr < 0.3:  # 低波动
                up_prob = 0.48 + np.random.normal(0, 0.08)
                down_prob = 0.48 + np.random.normal(0, 0.08)
            elif atr < 0.8:  # 正常波动
                up_prob = 0.52 + np.random.normal(0, 0.12)
                down_prob = 0.48 + np.random.normal(0, 0.12)
            elif atr < 1.5:  # 高波动
                up_prob = 0.55 + np.random.normal(0, 0.15)
                down_prob = 0.45 + np.random.normal(0, 0.15)
            else:  # 极高波动
                up_prob = 0.5 + np.random.normal(0, 0.2)
                down_prob = 0.5 + np.random.normal(0, 0.2)

            up_probs.append(np.clip(up_prob, 0, 1))
            down_probs.append(np.clip(down_prob, 0, 1))

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('global_atr_percent 特征分析', fontsize=16, fontweight='bold')

        # 1. ATR百分比分布图
        axes[0, 0].hist(atr_percent_values, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(x=0.08, color='red', linestyle='--', label='低波动阈值 (0.08%)')
        axes[0, 0].axvline(x=1.5, color='red', linestyle='--', label='高波动阈值 (1.5%)')
        axes[0, 0].set_xlabel('ATR百分比 (%)')
        axes[0, 0].set_ylabel('频次')
        axes[0, 0].set_title('ATR百分比分布')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. ATR百分比 vs 上涨概率散点图
        scatter = axes[0, 1].scatter(atr_percent_values, up_probs, alpha=0.6, c=atr_percent_values,
                                   cmap='viridis', s=20)
        axes[0, 1].set_xlabel('ATR百分比 (%)')
        axes[0, 1].set_ylabel('上涨预测概率')
        axes[0, 1].set_title('ATR百分比 vs 上涨概率')
        axes[0, 1].grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=axes[0, 1], label='ATR百分比')

        # 3. ATR百分比 vs 下跌概率散点图
        scatter2 = axes[1, 0].scatter(atr_percent_values, down_probs, alpha=0.6, c=atr_percent_values,
                                    cmap='plasma', s=20)
        axes[1, 0].set_xlabel('ATR百分比 (%)')
        axes[1, 0].set_ylabel('下跌预测概率')
        axes[1, 0].set_title('ATR百分比 vs 下跌概率')
        axes[1, 0].grid(True, alpha=0.3)
        plt.colorbar(scatter2, ax=axes[1, 0], label='ATR百分比')

        # 4. 波动率区间的预测性能
        bins = [(0, 0.08), (0.08, 0.3), (0.3, 0.8), (0.8, 1.5), (1.5, 5.0)]
        bin_labels = ['极低', '低', '正常', '高', '极高']
        avg_up_probs = []
        avg_down_probs = []

        for min_val, max_val in bins:
            mask = (atr_percent_values >= min_val) & (atr_percent_values < max_val)
            avg_up_probs.append(np.mean(np.array(up_probs)[mask]))
            avg_down_probs.append(np.mean(np.array(down_probs)[mask]))

        x = np.arange(len(bin_labels))
        width = 0.35

        axes[1, 1].bar(x - width/2, avg_up_probs, width, label='平均上涨概率', alpha=0.8, color='green')
        axes[1, 1].bar(x + width/2, avg_down_probs, width, label='平均下跌概率', alpha=0.8, color='red')
        axes[1, 1].set_xlabel('波动率区间')
        axes[1, 1].set_ylabel('平均预测概率')
        axes[1, 1].set_title('不同波动率区间的预测性能')
        axes[1, 1].set_xticks(x)
        axes[1, 1].set_xticklabels(bin_labels)
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        output_dir = "analysis_output"
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, "global_atr_percent_analysis.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"✓ 可视化图表已保存: {output_file}")

        plt.show()

    except Exception as e:
        print(f"⚠️ 创建可视化时出错: {e}")

def test_atr_percent_analysis():
    """测试 global_atr_percent 分析功能"""
    print("🧪 开始测试 global_atr_percent 分析功能...")
    try:
        analyze_global_atr_percent_dependencies()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def run_meta_model_training_pipeline():
    """独立的元模型训练流程"""
    global binance_client, _main_root, training_in_progress_flag

    try:
        print("\n===== 开始独立元模型训练流程 =====")
        gui.update_gui_safe(gui.update_status, "检查元模型训练前置条件...", "neutral")

        # 1. 检查元模型配置
        if not getattr(config, 'ENABLE_META_MODEL_TRAINING', True):
            error_msg = "元模型训练在配置中未启用"
            print(f"!!! {error_msg}")
            gui.update_gui_safe(gui.update_status, error_msg, "error")
            messagebox.showerror("配置错误", error_msg)
            return

        # 2. 检查基础模型配置
        base_models_for_meta = getattr(config, 'BASE_MODELS_FOR_META', [])
        if not base_models_for_meta:
            error_msg = "未配置用于元模型的基础模型 (BASE_MODELS_FOR_META)"
            print(f"!!! {error_msg}")
            gui.update_gui_safe(gui.update_status, error_msg, "error")
            messagebox.showerror("配置错误", error_msg)
            return

        print(f"  检查到 {len(base_models_for_meta)} 个基础模型用于元模型训练: {base_models_for_meta}")

        # 3. 检查基础模型是否已训练完成
        missing_base_models = []
        for base_model_name in base_models_for_meta:
            try:
                base_cfg = config.get_target_config(base_model_name)
                model_dir = base_cfg.get('model_save_dir')
                if not model_dir or not os.path.exists(model_dir):
                    missing_base_models.append(f"'{base_model_name}': 模型目录不存在")
                    continue

                # 检查关键模型文件是否存在
                pred_periods = base_cfg.get('prediction_periods', [1])
                pred_period = pred_periods[0] if isinstance(pred_periods, list) and pred_periods else 1
                interval_str = base_cfg.get('interval', 'Xm').rstrip('mhd')
                try:
                    minutes_display = int(interval_str) * pred_period
                except ValueError:
                    minutes_display = base_cfg.get('prediction_minutes_display', '?')

                target_name_for_file = base_model_name.replace('/', '_').replace(':', '_')

                # 尝试多种可能的模型文件名格式
                possible_filenames = [
                    f"model_{target_name_for_file}_{minutes_display}m.pkl",  # 原始期望格式
                    # 🎯 实际存在的文件名格式 - impthresh 校准版本（按fold优先级排序）
                    f"model_{target_name_for_file}_{minutes_display}m_fold4_impthresh_calibrated.joblib",  # impthresh校准版本fold4
                    f"model_{target_name_for_file}_{minutes_display}m_fold3_impthresh_calibrated.joblib",  # impthresh校准版本fold3
                    f"model_{target_name_for_file}_{minutes_display}m_fold2_impthresh_calibrated.joblib",  # impthresh校准版本fold2
                    f"model_{target_name_for_file}_{minutes_display}m_fold1_impthresh_calibrated.joblib",  # impthresh校准版本fold1
                    f"model_{target_name_for_file}_{minutes_display}m_fold0_impthresh_calibrated.joblib",  # impthresh校准版本fold0
                    # 🎯 实际存在的文件名格式 - impthresh 普通版本
                    f"model_{target_name_for_file}_{minutes_display}m_fold4_impthresh.joblib",  # impthresh普通版本fold4
                    f"model_{target_name_for_file}_{minutes_display}m_fold3_impthresh.joblib",  # impthresh普通版本fold3
                    f"model_{target_name_for_file}_{minutes_display}m_fold2_impthresh.joblib",  # impthresh普通版本fold2
                    f"model_{target_name_for_file}_{minutes_display}m_fold1_impthresh.joblib",  # impthresh普通版本fold1
                    f"model_{target_name_for_file}_{minutes_display}m_fold0_impthresh.joblib",  # impthresh普通版本fold0
                    # 🎯 实际存在的文件名格式 - impthresh + optuna 校准版本（主要用于DOWN模型）
                    f"model_{target_name_for_file}_{minutes_display}m_fold4_impthresh_optuna_calibrated.joblib",  # impthresh+optuna校准版本fold4
                    f"model_{target_name_for_file}_{minutes_display}m_fold3_impthresh_optuna_calibrated.joblib",  # impthresh+optuna校准版本fold3
                    f"model_{target_name_for_file}_{minutes_display}m_fold2_impthresh_optuna_calibrated.joblib",  # impthresh+optuna校准版本fold2
                    f"model_{target_name_for_file}_{minutes_display}m_fold1_impthresh_optuna_calibrated.joblib",  # impthresh+optuna校准版本fold1
                    f"model_{target_name_for_file}_{minutes_display}m_fold0_impthresh_optuna_calibrated.joblib",  # impthresh+optuna校准版本fold0
                    # 🎯 实际存在的文件名格式 - impthresh + optuna 普通版本
                    f"model_{target_name_for_file}_{minutes_display}m_fold4_impthresh_optuna.joblib",  # impthresh+optuna普通版本fold4
                    f"model_{target_name_for_file}_{minutes_display}m_fold3_impthresh_optuna.joblib",  # impthresh+optuna普通版本fold3
                    f"model_{target_name_for_file}_{minutes_display}m_fold2_impthresh_optuna.joblib",  # impthresh+optuna普通版本fold2
                    f"model_{target_name_for_file}_{minutes_display}m_fold1_impthresh_optuna.joblib",  # impthresh+optuna普通版本fold1
                    f"model_{target_name_for_file}_{minutes_display}m_fold0_impthresh_optuna.joblib",  # impthresh+optuna普通版本fold0
                    # 原有的RFE格式（保持向后兼容）
                    f"model_{target_name_for_file}_{minutes_display}m_fold4_rfe_calibrated.joblib",  # RFE校准版本fold4
                    f"model_{target_name_for_file}_{minutes_display}m_fold3_rfe_calibrated.joblib",  # RFE校准版本fold3
                    f"model_{target_name_for_file}_{minutes_display}m_fold2_rfe_calibrated.joblib",  # RFE校准版本fold2
                    f"model_{target_name_for_file}_{minutes_display}m_fold1_rfe_calibrated.joblib",  # RFE校准版本fold1
                    f"model_{target_name_for_file}_{minutes_display}m_fold0_rfe_calibrated.joblib",  # RFE校准版本fold0
                    f"model_{target_name_for_file}_{minutes_display}m_fold4_rfe.joblib",  # RFE普通版本fold4
                    f"model_{target_name_for_file}_{minutes_display}m_fold3_rfe.joblib",  # RFE普通版本fold3
                    f"model_{target_name_for_file}_{minutes_display}m_fold2_rfe.joblib",  # RFE普通版本fold2
                    f"model_{target_name_for_file}_{minutes_display}m_fold1_rfe.joblib",  # RFE普通版本fold1
                    f"model_{target_name_for_file}_{minutes_display}m_fold0_rfe.joblib",  # RFE普通版本fold0
                    # 兼容旧格式 - Optuna版本
                    f"model_{target_name_for_file}_{minutes_display}m_fold4_optuna_rfe_calibrated.joblib",  # 校准版本
                    f"model_{target_name_for_file}_{minutes_display}m_fold0_optuna_rfe_calibrated.joblib",  # 校准版本fold0
                    f"model_{target_name_for_file}_{minutes_display}m_fold4_optuna_rfe.joblib",  # 普通版本fold4
                    f"model_{target_name_for_file}_{minutes_display}m_fold0_optuna_rfe.joblib",  # 普通版本fold0
                ]

                model_filepath = None
                model_filename = None

                # 按优先级查找存在的模型文件
                for filename in possible_filenames:
                    filepath = os.path.join(model_dir, filename)
                    if os.path.exists(filepath):
                        model_filepath = filepath
                        model_filename = filename
                        print(f"  ✓ 找到基础模型文件: {filename}")
                        break

                if model_filepath is None:
                    missing_base_models.append(f"'{base_model_name}': 模型文件不存在，尝试过的文件名: {possible_filenames}")

            except ValueError as e:
                missing_base_models.append(f"'{base_model_name}': 配置错误 - {e}")
            except Exception as e:
                missing_base_models.append(f"'{base_model_name}': 检查失败 - {e}")

        if missing_base_models:
            error_msg = "以下基础模型未完成训练或文件缺失:\n" + "\n".join(missing_base_models)
            print(f"!!! {error_msg}")
            gui.update_gui_safe(gui.update_status, "基础模型检查失败", "error")
            messagebox.showerror("前置条件不满足", error_msg)
            return

        print("  ✓ 所有基础模型检查通过")
        gui.update_gui_safe(gui.update_status, "开始准备元模型训练数据...", "neutral")

        # 4. 准备元模型训练数据 (复用现有逻辑)
        print("  开始生成OOF预测数据...")

        # 获取历史数据 (使用第一个基础模型的配置)
        first_base_model_name = base_models_for_meta[0]
        first_base_cfg = config.get_target_config(first_base_model_name)

        # 获取历史数据
        meta_hist_fetch_limit = getattr(config, 'DATA_FETCH_LIMIT', 20000) + getattr(config, 'ADDITIONAL_BARS_FOR_MTFA_AND_FEATURES', 5000)
        df_full_hist_data_for_oof_input = data_utils.fetch_binance_history(
            binance_client,
            first_base_cfg.get('symbol', getattr(config, 'SYMBOL', 'BTCUSDT')),
            first_base_cfg.get('interval', '15m'),
            limit=meta_hist_fetch_limit
        )

        if df_full_hist_data_for_oof_input is None or df_full_hist_data_for_oof_input.empty:
            error_msg = "无法获取历史数据用于元模型训练"
            print(f"!!! {error_msg}")
            gui.update_gui_safe(gui.update_status, error_msg, "error")
            messagebox.showerror("数据错误", error_msg)
            return

        print(f"  ✓ 获取历史数据: {df_full_hist_data_for_oof_input.shape}")

        # 4.5. 为历史数据添加特征工程 (使用第一个基础模型的配置)
        print("  开始为历史数据添加特征工程...")
        gui.update_gui_safe(gui.update_status, "为历史数据添加特征工程...", "neutral")

        try:
            # 使用第一个基础模型的配置进行特征工程
            df_with_features = data_utils.add_classification_features(
                df_full_hist_data_for_oof_input.copy(), first_base_cfg
            )

            if df_with_features is None or df_with_features.empty:
                error_msg = "基础特征工程失败"
                print(f"!!! {error_msg}")
                gui.update_gui_safe(gui.update_status, error_msg, "error")
                messagebox.showerror("特征工程错误", error_msg)
                return

            print(f"  ✓ 基础特征工程完成: {df_with_features.shape}")

            # 如果启用了MTFA，添加MTFA特征
            if first_base_cfg.get('enable_mtfa', False):
                print("  开始添加MTFA特征...")
                df_with_features = data_utils.add_mtfa_features_to_df(
                    df_with_features, first_base_cfg, binance_client
                )

                if df_with_features is None or df_with_features.empty:
                    error_msg = "MTFA特征工程失败"
                    print(f"!!! {error_msg}")
                    gui.update_gui_safe(gui.update_status, error_msg, "error")
                    messagebox.showerror("MTFA特征错误", error_msg)
                    return

                print(f"  ✓ MTFA特征工程完成: {df_with_features.shape}")

            # 更新历史数据为包含特征的数据
            df_full_hist_data_for_oof_input = df_with_features
            print(f"  ✓ 完整特征工程完成: {df_full_hist_data_for_oof_input.shape}")

        except Exception as e_feature_eng:
            error_msg = f"特征工程过程中出错: {e_feature_eng}"
            print(f"!!! {error_msg}")
            gui.update_gui_safe(gui.update_status, error_msg, "error")
            messagebox.showerror("特征工程错误", error_msg)
            return

        # 5. 生成元目标变量
        meta_target_config = {
            'name': 'MetaModel',
            'target_variable_type': getattr(config, 'META_MODEL_TARGET_TYPE', 'BOTH'),  # 使用BOTH支持三分类
            'prediction_periods': getattr(config, 'META_MODEL_PREDICTION_PERIODS', [1]),
            'target_threshold': getattr(config, 'META_MODEL_THRESHOLD', 0.002),  # 统一使用target_threshold
            'drop_neutral_targets': getattr(config, 'META_MODEL_DROP_NEUTRAL', False),  # 是否移除中性样本
            'interval': first_base_cfg.get('interval', '15m'),
            'symbol': first_base_cfg.get('symbol', getattr(config, 'SYMBOL', 'BTCUSDT'))
        }

        df_with_meta_target, meta_target_col_name = data_utils.create_target_variable(
            df_full_hist_data_for_oof_input.copy(), meta_target_config
        )

        if not meta_target_col_name or meta_target_col_name not in df_with_meta_target.columns:
            error_msg = "无法创建元模型目标变量"
            print(f"!!! {error_msg}")
            gui.update_gui_safe(gui.update_status, error_msg, "error")
            messagebox.showerror("数据错误", error_msg)
            return

        y_meta_series_for_oof_input = df_with_meta_target[meta_target_col_name]
        print(f"  ✓ 创建元目标变量: {y_meta_series_for_oof_input.shape}, 类别分布: {y_meta_series_for_oof_input.value_counts().to_dict()}")

        # 6. 为每个基础模型生成OOF预测
        print("  开始为各基础模型生成OOF预测...")
        oof_predictions_dict = {}

        for i, base_model_name_for_oof in enumerate(base_models_for_meta):
            print(f"  [{i+1}/{len(base_models_for_meta)}] 生成 '{base_model_name_for_oof}' 的OOF预测...")
            gui.update_gui_safe(gui.update_status, f"生成OOF预测 ({i+1}/{len(base_models_for_meta)}): {base_model_name_for_oof}", "neutral")

            # 🎯 修复：检查binance_client状态
            if binance_client is None:
                print(f"  警告: binance_client为None，'{base_model_name_for_oof}'的MTFA特征可能无法正常生成")
            else:
                print(f"  binance_client已初始化，'{base_model_name_for_oof}'支持MTFA特征生成")

            oof_series_current = generate_oof_predictions_for_base_model(
                base_model_target_name=base_model_name_for_oof,
                df_full_hist_data_with_features=df_full_hist_data_for_oof_input.copy(),
                y_meta_series_aligned=y_meta_series_for_oof_input.copy(),
                app_timezone=APP_TIMEZONE,
                binance_client=binance_client
            )

            if oof_series_current is not None and not oof_series_current.empty:
                oof_predictions_dict[base_model_name_for_oof] = oof_series_current
                print(f"    ✓ '{base_model_name_for_oof}' OOF预测完成: {oof_series_current.shape}")
            else:
                print(f"    !!! '{base_model_name_for_oof}' OOF预测失败")

        if not oof_predictions_dict:
            error_msg = "所有基础模型的OOF预测都失败了"
            print(f"!!! {error_msg}")
            gui.update_gui_safe(gui.update_status, error_msg, "error")
            messagebox.showerror("训练失败", error_msg)
            return

        print(f"  ✓ 成功生成 {len(oof_predictions_dict)} 个基础模型的OOF预测")

        # 7. 构建元模型训练数据
        gui.update_gui_safe(gui.update_status, "构建元模型训练数据...", "neutral")
        print("  构建元模型特征矩阵...")

        X_meta_df_raw_with_nans = pd.DataFrame()
        for base_name, oof_series in oof_predictions_dict.items():
            # 使用配置中定义的特征名称，如果找不到则使用默认格式
            feature_name = None
            meta_features_config = getattr(config, 'META_MODEL_INPUT_FEATURES_CONFIG', [])
            for feature_config in meta_features_config:
                if feature_config.get('base_model_name') == base_name:
                    feature_name = feature_config.get('meta_feature_name')
                    break

            # 如果配置中没有找到，使用默认格式
            if not feature_name:
                feature_name = f"oof_proba_{base_name}_pfavorable"

            X_meta_df_raw_with_nans[feature_name] = oof_series
            print(f"    添加基础模型特征: {feature_name} (来源: {base_name})")

        # 处理初始NaN值
        initial_nan_rows_mask_meta = X_meta_df_raw_with_nans.isnull().any(axis=1)
        X_meta_basic = X_meta_df_raw_with_nans[~initial_nan_rows_mask_meta].copy()

        # 添加特征工程
        print("  应用元模型特征工程...")
        X_meta_enhanced = add_meta_feature_engineering(X_meta_basic, base_models_for_meta)

        # 对齐目标变量
        common_index_for_final_meta_xy = X_meta_enhanced.index.intersection(y_meta_series_for_oof_input.index)
        X_meta_final_to_save = X_meta_enhanced.loc[common_index_for_final_meta_xy]
        y_meta_final_to_save = y_meta_series_for_oof_input.loc[common_index_for_final_meta_xy].copy()

        print(f"  ✓ 元模型训练数据准备完成: X={X_meta_final_to_save.shape}, y={y_meta_final_to_save.shape}")
        print(f"    基础特征列: {[col for col in X_meta_final_to_save.columns if col.startswith('prob_')]}")
        print(f"    工程特征列: {[col for col in X_meta_final_to_save.columns if not col.startswith('prob_')]}")
        print(f"    目标类别分布: {y_meta_final_to_save.value_counts().to_dict()}")

        # 8. 调用元模型训练
        gui.update_gui_safe(gui.update_status, "开始训练元模型...", "neutral")
        print("  调用 prediction.train_meta_model()...")

        train_success_meta, trained_meta_model_object, \
        X_val_meta_from_func, y_val_meta_from_func, \
        meta_eval_results_dict_from_func = \
            prediction.train_meta_model(X_meta_final_to_save, y_meta_final_to_save)

        # 9. 处理训练结果
        if train_success_meta and trained_meta_model_object is not None:
            print("  ✓ 元模型训练成功完成!")

            # 更新GUI显示
            meta_model_display_name = getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', '元模型')
            success_msg = f"元模型训练完成"
            if meta_eval_results_dict_from_func and 'accuracy' in meta_eval_results_dict_from_func:
                accuracy = meta_eval_results_dict_from_func['accuracy']
                success_msg += f" (准确率: {accuracy:.2%})"

            gui.update_gui_safe(gui.update_prediction_display, meta_model_display_name, success_msg, config.UP_COLOR)
            gui.update_gui_safe(gui.update_labelframe_text, meta_model_display_name, f"目标: {meta_model_display_name} (训练完成)")
            gui.update_gui_safe(gui.update_status, "元模型训练成功完成", "success")

            # 尝试重新加载元模型到预测系统
            if hasattr(prediction, 'load_meta_model_if_needed'):
                if prediction.load_meta_model_if_needed():
                    print("  ✓ 新训练的元模型已加载到预测系统")
                else:
                    print("  ! 警告: 新训练的元模型加载到预测系统失败")

            messagebox.showinfo("训练完成", "元模型训练成功完成！")

        else:
            error_msg = "元模型训练失败"
            if meta_eval_results_dict_from_func and 'status' in meta_eval_results_dict_from_func:
                error_msg += f": {meta_eval_results_dict_from_func['status']}"

            print(f"!!! {error_msg}")
            meta_model_display_name = getattr(config, 'META_MODEL_GUI_DISPLAY_NAME', '元模型')
            gui.update_gui_safe(gui.update_prediction_display, meta_model_display_name, "训练失败", config.ERROR_COLOR)
            gui.update_gui_safe(gui.update_labelframe_text, meta_model_display_name, f"目标: {meta_model_display_name} (训练失败)")
            gui.update_gui_safe(gui.update_status, error_msg, "error")
            messagebox.showerror("训练失败", error_msg)

    except Exception as e:
        error_msg = f"元模型训练过程中发生错误: {e}"
        print(f"!!! {error_msg}")
        traceback.print_exc()
        gui.update_gui_safe(gui.update_status, "元模型训练出错", "error")
        messagebox.showerror("训练错误", error_msg)

    finally:
        # 恢复按钮状态
        app_state.set_training_in_progress(False)
        gui.update_gui_safe(gui.set_button_state, "train_any", tk.NORMAL)
        gui.update_gui_safe(gui.set_button_state, "train_meta", tk.NORMAL)
        gui.update_gui_safe(gui.set_button_state, "predict", tk.NORMAL)
        print("===== 独立元模型训练流程结束 =====\n")



# --- GUI CALLBACKS & MAIN BLOCK --- (保持不变)
def start_training():
    if training_in_progress_flag.is_set(): messagebox.showwarning("忙碌", "训练已在进行中。"); return
    confirm_msg = ("即将开始训练流程。\n"
                   "这可能包含 RFE 特征筛选, 初始重要性筛选, Optuna 超参数优化, 概率校准 (根据配置)。\n"
                   "现有的模型、Scaler、特征文件和元数据文件将被覆盖。\n"
                   "此过程可能需要较长时间。\n\n确定开始吗？")
    if not messagebox.askyesno("确认训练", confirm_msg): return
    if isinstance(config.PREDICTION_TARGETS, list):
        for target in config.PREDICTION_TARGETS:
             if isinstance(target, dict) and 'name' in target:
                 tname=target['name']
                 gui.update_gui_safe(gui.update_prediction_display,tname,f"准备训练 {tname}...", getattr(config, 'NEUTRAL_COLOR', '#FFCA28')) # Fallback color
                 gui.update_gui_safe(gui.update_labelframe_text,tname,f"目标: {tname} (准备中...)")
    gui.set_button_state("train",tk.DISABLED); gui.set_button_state("predict",tk.DISABLED)
    app_state.set_training_in_progress(True); gui.update_status("开始训练/优化...", "neutral")
    threading.Thread(target=run_training_pipeline, daemon=True).start()

def start_prediction(): # 手动触发
    global binance_client, APP_TIMEZONE # 确保全局变量可访问

    if training_in_progress_flag.is_set():
        messagebox.showwarning("忙碌", "训练正在进行中。"); return
    
    # 移除不必要的 current_prediction_lock，因为元模型和基础模型预测现在是独立的线程任务
    # current_prediction_lock = prediction.get_prediction_lock() 
    # if current_prediction_lock.locked():
    #     gui.update_gui_safe(gui.update_status, "预测已在运行中...", "warning"); return

    if not getattr(config, 'GA_RUN_OFFLINE', False) and not binance_client:
        messagebox.showerror("错误", "Binance 客户端未初始化。"); return

    enable_meta_prediction_live = getattr(config, 'ENABLE_META_MODEL_PREDICTION', True)

    # 优化后的逻辑：元模型启用时优先加载并触发元模型预测
    if enable_meta_prediction_live:
        print("--- 手动触发: 元模型预测 ---")
        
        # 检查元模型是否已加载，如果未加载则尝试加载
        if not prediction.global_prediction_state_manager.is_meta_model_loaded_successfully():
            print("!!! 手动触发元预测: 元模型尚未成功加载。将尝试加载。")
            if not (hasattr(prediction, 'load_meta_model_if_needed') and prediction.load_meta_model_if_needed()):
                messagebox.showerror("元模型错误", "元模型未能加载，无法执行手动元预测。")
                gui.update_gui_safe(gui.update_status, "元模型加载失败", "error")
                return # 元模型加载失败，直接返回

        # 元模型加载成功，触发元模型预测线程
        gui.update_gui_safe(gui.update_status, "开始手动元模型预测...", "neutral")
        threading.Thread(
            target=prediction.run_meta_prediction_for_current_trigger,
            args=(binance_client, APP_TIMEZONE, getattr(config, 'SIMULATOR_API_URL', None)),
            daemon=True
        ).start()
        return  # 元模型预测已触发，函数返回
    
    # --- 以下代码只有在 enable_meta_prediction_live 为 False 时才会执行 ---
    print("--- 手动触发: 独立基础模型预测 (元模型禁用) ---")
    all_files_ok = True; missing_details = [] # 重新初始化
    if not config.PREDICTION_TARGETS or not isinstance(config.PREDICTION_TARGETS, list):
        messagebox.showerror("配置错误", "PREDICTION_TARGETS 配置无效或为空。"); return

    for t_cfg_base_idx, t_cfg_base in enumerate(config.PREDICTION_TARGETS):
        if not isinstance(t_cfg_base, dict):
            missing_details.append(f"目标配置 #{t_cfg_base_idx} 不是有效的字典。")
            all_files_ok = False; continue
        target_name_check = t_cfg_base.get('name')
        if not target_name_check:
            missing_details.append(f"目标配置 #{t_cfg_base_idx} 缺少 'name' 属性。")
            all_files_ok = False; continue
        
        # 跳过元模型的GUI显示条目，因为它不包含要检查的文件
        if t_cfg_base.get('target_variable_type') == "META_MODEL_DISPLAY":
            continue

        try:
            t_cfg_full = config.get_target_config(target_name_check)
            model_dir_check = t_cfg_full.get('model_save_dir')
            if not model_dir_check or not os.path.exists(model_dir_check):
                missing_details.append(f"目标 '{target_name_check}': 模型目录 '{model_dir_check}' 不存在或未配置。")
                all_files_ok = False; continue
            
            # 简化的元数据文件检查 (只检查是否存在，具体内容由预测逻辑处理)
            pred_periods_cfg_check = t_cfg_full.get('prediction_periods',)
            pred_periods_for_file_check = pred_periods_cfg_check if isinstance(pred_periods_cfg_check, list) and pred_periods_cfg_check else 1
            interval_val_str_check = t_cfg_full.get('interval', 'Xm').rstrip('mhd')
            try: minutes_display_val_check = int(interval_val_str_check) * pred_periods_for_file_check
            except ValueError: minutes_display_val_check = t_cfg_full.get('prediction_minutes_display', '?')
            minutes_display_for_file_check = str(minutes_display_val_check)
            
            meta_filename_check = f"model_meta_{target_name_check.replace('/', '_').replace(':', '_')}_{minutes_display_for_file_check}m.json"
            if not os.path.exists(os.path.join(model_dir_check, meta_filename_check)):
                missing_details.append(f"目标 '{target_name_check}': 元数据文件 '{meta_filename_check}' 缺失。")
                all_files_ok = False; continue

        except ValueError as e_cfg_val_pred: 
            missing_details.append(f"目标 '{target_name_check}': 获取配置时出错 - {e_cfg_val_pred}")
            all_files_ok = False
        except Exception as e_check_outer: 
            missing_details.append(f"目标 '{target_name_check}': 文件预检查时意外错误 - {e_check_outer}")
            all_files_ok = False; traceback.print_exc(limit=1)

    if not all_files_ok:
        error_message_pred_show = "预测失败，基础模型文件/配置缺失:\n- " + "\n- ".join(missing_details)
        messagebox.showerror("文件/配置问题", error_message_pred_show); return
    
    # 只有在元模型禁用且所有文件检查通过后，才启动基础模型预测线程
    threading.Thread(target=run_all_preds_seq_thread_wrapper, daemon=True).start()


def run_all_preds_seq_thread_wrapper(): # 这个函数现在只用于元模型禁用时手动触发基础模型
    print("--- 手动触发所有独立基础模型预测 (元模型禁用或特定调用) ---")
    local_pred_lock = prediction.get_prediction_lock() # 可能不再需要
    
    # 尝试获取锁，并根据结果执行不同的逻辑
    lock_acquired = local_pred_lock.acquire(blocking=False)
    if not lock_acquired:
        # 如果无法获取锁，说明已有预测任务在运行
        gui.update_gui_safe(gui.update_status, "无法获取预测锁", "warning")
        messagebox.showwarning("忙碌", "预测任务已在运行。")
        return
    
    # 成功获取锁后，执行预测逻辑
    try:
        gui.update_gui_safe(gui.update_status, "开始手动预测 (基础模型)...", "neutral")
        
        if not isinstance(config.PREDICTION_TARGETS, list):
            print("!!! 错误: PREDICTION_TARGETS 配置无效或为空。") # 改为打印，避免多余的messagebox
            if hasattr(gui, 'update_status') and gui._gui_root and gui._gui_root.winfo_exists():
                gui.update_gui_safe(gui.update_status, "PREDICTION_TARGETS 配置错误", "error")
            return # 提前退出

        any_actual_prediction_called = False
        for tg_cfg_base in config.PREDICTION_TARGETS:
            if not isinstance(tg_cfg_base, dict): continue
            
            target_name_iter = tg_cfg_base.get('name')
            if not target_name_iter: continue

            # --- 核心过滤逻辑 ---
            # 获取完整配置以检查 target_variable_type
            try:
                full_iter_cfg = config.get_target_config(target_name_iter)
                # 如果是元模型的显示条目，或者不是一个应该独立运行的基础模型类型，则跳过
                if full_iter_cfg.get('target_variable_type') == "META_MODEL_DISPLAY" or \
                   not full_iter_cfg.get('model_save_dir'): # 纯显示条目通常没有model_save_dir
                    print(f"  run_all_preds_seq: 跳过非执行目标或GUI显示条目 '{target_name_iter}'")
                    continue
            except ValueError as e_get_cfg_filter:
                print(f"  run_all_preds_seq: 获取配置以过滤 '{target_name_iter}' 时出错: {e_get_cfg_filter}，跳过。")
                continue
            # --- 过滤结束 ---
            
            any_actual_prediction_called = True # 标记至少有一个可执行的目标
            print(f"  run_all_preds_seq: 为基础模型 '{target_name_iter}' 调用完整预测周期...")
            
            if stop_event.is_set():
                print("  run_all_preds_seq: 预测任务中止。")
                break
            
            # 调用 prediction.run_prediction_cycle_for_target 执行完整流程
            # 它内部会处理自己的异常和GUI更新
            prediction.run_prediction_cycle_for_target(
                full_iter_cfg, # 使用前面获取的完整配置
                binance_client,
                APP_TIMEZONE,
                return_core_prediction_only=False, # 确保是完整模式
                simulator_actual_url=getattr(config, 'SIMULATOR_API_URL', None)
            )
            time.sleep(0.2) # 避免过于频繁的API请求或日志刷新

        if any_actual_prediction_called:
            if hasattr(gui, 'update_status') and gui._gui_root and gui._gui_root.winfo_exists():
                gui.update_gui_safe(gui.update_status, "所有手动基础模型预测已尝试执行", "success")
        else:
            if hasattr(gui, 'update_status') and gui._gui_root and gui._gui_root.winfo_exists():
                gui.update_gui_safe(gui.update_status, "没有可独立执行的基础模型预测", "info")

    except Exception as e_pred_all_base:
        print(f"!!! 手动基础模型预测序列中发生意外错误: {e_pred_all_base}")
        if hasattr(gui, 'update_status') and gui._gui_root and gui._gui_root.winfo_exists():
            gui.update_gui_safe(gui.update_status, "手动基础模型预测出错", "error")
        traceback.print_exc(limit=1)

    finally:
        # 只有在成功获取锁的情况下才释放锁
        # 由于我们在获取锁失败时已经提前返回，所以这里可以直接释放锁
        try: 
            local_pred_lock.release()
            print(" 手动预测锁已释放.")
        except Exception as e_lock_rel: 
            print(f"! 警告: 释放手动预测锁出错: {e_lock_rel}")

# main.py (继续)

def safe_get_target_config(target_name: str, fallback_to_original: bool = True):
    """
    安全地获取目标配置，支持强化配置管理器和原始方法的回退

    Args:
        target_name: 目标名称
        fallback_to_original: 是否在强化方法失败时回退到原始方法

    Returns:
        配置字典或配置包装器

    Raises:
        ValueError: 当配置获取失败时
    """
    global robust_config_manager

    # 优先使用强化配置管理器
    if robust_config_manager:
        try:
            wrapper = robust_config_manager.get_target_config(target_name)
            return wrapper.to_dict()  # 返回字典以保持兼容性
        except (TargetConfigNotFoundError, ConfigurationError) as e:
            print(f"⚠️ 强化配置获取失败 (目标: {target_name}): {e}")
            if not fallback_to_original:
                raise ValueError(f"强化配置获取失败: {e}") from e
        except Exception as e:
            print(f"⚠️ 强化配置获取发生未知错误 (目标: {target_name}): {e}")
            if not fallback_to_original:
                raise ValueError(f"强化配置获取错误: {e}") from e

    # 回退到原始方法
    if fallback_to_original:
        try:
            return config.get_target_config(target_name)
        except Exception as e:
            raise ValueError(f"原始配置获取也失败 (目标: {target_name}): {e}") from e

    raise ValueError(f"无法获取目标配置: {target_name}")


def get_robust_config_wrapper(target_name: str):
    """
    获取强化配置包装器，提供类型安全的配置访问

    Args:
        target_name: 目标名称

    Returns:
        TargetConfigWrapper: 配置包装器

    Raises:
        ValueError: 当配置获取失败时
    """
    global robust_config_manager

    if not robust_config_manager:
        raise ValueError("强化配置管理器未初始化")

    try:
        return robust_config_manager.get_target_config(target_name)
    except Exception as e:
        raise ValueError(f"获取强化配置包装器失败 (目标: {target_name}): {e}") from e


def on_closing():
    global _main_root, stop_event # shared_binance_twm 已是全局，无需在此声明
    
    user_confirms_exit = False
    if _main_root and _main_root.winfo_exists():
        user_confirms_exit = messagebox.askokcancel("退出确认", "确定要退出应用程序吗？", parent=_main_root)
    else:
        print("GUI窗口不存在或已损坏，尝试执行关闭流程...")
        user_confirms_exit = True 

    if user_confirms_exit:
        print("收到退出请求或GUI异常，执行关闭流程..."); 
        if _main_root and hasattr(gui, 'update_status'): 
            gui.update_gui_safe(gui.update_status, "正在退出...", "neutral")
        
        # 使用状态管理器的清理功能
        app_state.cleanup()
        print("停止信号已发送给所有线程。")

        # --- 核心修改：停止共享TWM ---
        stop_shared_twm()
        # --- 不再需要下面这行，因为TWM关闭会处理所有它管理的流 ---
        # realtime_data_manager.stop_all_streams() 
        
        if _main_root and _main_root.winfo_exists():
            try:
                print("正在销毁 GUI...");
                # 🔧 修复：安全地取消所有after回调
                try:
                    after_info = _main_root.tk.call('after', 'info')
                    # after_info 可能是字符串、元组或列表，需要安全处理
                    if after_info:
                        # 如果是字符串，分割成列表；如果是元组/列表，直接使用
                        if isinstance(after_info, str):
                            after_ids = after_info.split()
                        elif isinstance(after_info, (list, tuple)):
                            after_ids = list(after_info)
                        else:
                            after_ids = [str(after_info)]

                        for after_id in after_ids:
                            try:
                                _main_root.after_cancel(str(after_id))
                            except (tk.TclError, ValueError):
                                pass
                except (tk.TclError, AttributeError):
                    # 如果获取after信息失败，跳过清理after回调
                    pass

                _main_root.destroy()
                print("GUI 已销毁。")
            except tk.TclError as e_tcl_destroy:
                 if "application has been destroyed" not in str(e_tcl_destroy).lower():
                     print(f"! 关闭 GUI 时发生 TclError: {e_tcl_destroy}")
            except Exception as e_destroy:
                print(f"! 关闭 GUI 出错: {e_destroy}")
        
        _main_root = None 
        print("退出流程核心部分完成。")
    else:
        print("用户取消了退出操作。")

# --- 主程序入口 if __name__ == "__main__": ---
if __name__ == "__main__":

    # --- 1. 解析命令行参数 ---
    parser = argparse.ArgumentParser(description="加密货币多周期预测程序")
    parser.add_argument("--config_module", type=str, default="config",
                        help="要加载的配置文件名 (不带 .py 后缀)")
    parser.add_argument("--simulator_url", type=str,
                        default="http://127.0.0.1:5008/signal", # 确保这是您SimMain的正确端口
                        help="模拟盘信号接收API的URL")
    parser.add_argument("--validation_level", type=str, default="strict",
                        choices=["strict", "warning", "permissive"],
                        help="配置验证级别")
    args = parser.parse_args()

    SIMULATOR_URL_FROM_CMD = args.simulator_url
    print(f"Main: 预测信号将发送至模拟盘URL: {SIMULATOR_URL_FROM_CMD} (来自命令行或默认)。")

    # --- 2. 强化配置加载与验证 ---

    # 确定验证级别
    validation_level = ValidationLevel.STRICT  # 默认严格模式
    if hasattr(args, 'validation_level'):
        validation_level = getattr(ValidationLevel, args.validation_level.upper(), ValidationLevel.STRICT)

    print("=" * 80)
    print("🔧 强化配置系统初始化")
    print("=" * 80)

    # 使用强化配置验证系统
    try:
        config, validation_result = validate_and_load_config(
            config_module_name=args.config_module,
            validation_level=validation_level
        )

        # 创建强化配置管理器
        robust_config_manager = RobustConfigManager(config)
        print(f"✅ 强化配置管理器已初始化")

        # 显示验证结果摘要
        if validation_result.warnings:
            print(f"⚠️ 配置验证发现 {len(validation_result.warnings)} 个警告")

        print(f"📊 可用目标配置: {len(robust_config_manager.list_available_targets())} 个")

    except SystemExit:
        # 配置验证失败，程序已退出
        raise
    except Exception as e_robust_config:
        print(f"❌ 强化配置系统初始化失败: {e_robust_config}")
        print("🔄 回退到传统配置加载方式...")
        traceback.print_exc()

        # 回退到原有的配置加载方式
        try:
            config = importlib.import_module(args.config_module)
            robust_config_manager = RobustConfigManager(config)
            print(f"⚠️ 使用传统方式加载配置模块 '{args.config_module}.py'")
        except ImportError:
            print(f"❌ 无法加载配置模块 '{args.config_module}.py', 尝试默认配置...")
            try:
                import config
                robust_config_manager = RobustConfigManager(config)
                print("⚠️ 已加载默认的 'config.py'")
            except ImportError as e_default_cfg:
                print(f"💥 严重错误 - 无法加载默认配置: {e_default_cfg}")
                exit(1)
        except Exception as e_fallback:
            print(f"💥 配置加载完全失败: {e_fallback}")
            exit(1)

    # --- 打印启动信息 ---
    print("-" * 60);
    print(f"启动加密货币多周期预测程序 (主时区: {APP_TIMEZONE or '系统默认'})")
    # ... (您现有的打印配置目标信息的代码) ...
    if hasattr(config, 'PREDICTION_TARGETS') and isinstance(config.PREDICTION_TARGETS, list):
        for t_base_main_idx, t_base_main in enumerate(config.PREDICTION_TARGETS):
             if isinstance(t_base_main, dict):
                 t_name_main = t_base_main.get('name', f'未命名目标_{t_base_main_idx}')
                 try:
                     full_cfg_main = config.get_target_config(t_name_main)
                     print(f" - 目标名:{t_name_main}, 交易对:{full_cfg_main.get('symbol', '?')}, 周期:{full_cfg_main.get('interval','?')}, 类型:{full_cfg_main.get('target_variable_type','?')}")
                 except Exception as e_print_cfg: print(f" - 打印目标 '{t_name_main}' 配置时出错: {e_print_cfg}")
    if getattr(config, 'ENABLE_META_MODEL_PREDICTION', False): # 检查是否启用了元模型预测
        print(f"--- 元模型预测已启用 (基础模型: {getattr(config, 'BASE_MODELS_FOR_META', [])}) ---")
    print("-" * 60);

    # --- 3. 初始化普通 Binance 客户端 ---
    client_ready = initialize_binance_client()

    # --- 4. 初始化并启动共享的 ThreadedWebsocketManager ---
    proxy_from_cfg = getattr(config, 'PROXY_URL', DEFAULT_PROXY_URL)
    if not initialize_shared_twm(proxy_url=proxy_from_cfg):
        print("!!! 严重错误: 无法初始化共享的WebSocket管理器！程序可能无法接收实时数据。")
    else:
        # 5. 将共享的 TWM 实例注入到 realtime_data_manager
        if realtime_data_manager and shared_binance_twm:
            realtime_data_manager.set_shared_twm(shared_binance_twm)
            print("Main: 共享TWM已注入RealtimeDataManager。")
        else:
            print(f"!!! 错误: RealtimeDataManager未加载或共享TWM未初始化，无法注入。")

    # --- 6. 根据配置启动实时数据流 (通过RDM) ---
    symbols_for_price_stream = set()
    kline_streams_to_start = set()
    gui_display_symbol = getattr(config, 'SYMBOL', 'BTCUSDT').upper() # 默认显示符号

    if hasattr(config, 'PREDICTION_TARGETS') and isinstance(config.PREDICTION_TARGETS, list) and config.PREDICTION_TARGETS:
        first_target_name_for_gui = config.PREDICTION_TARGETS[0].get('name')
        if first_target_name_for_gui:
             try:
                 first_target_cfg_for_gui = config.get_target_config(first_target_name_for_gui)
                 gui_display_symbol = str(first_target_cfg_for_gui.get('symbol', gui_display_symbol)).upper()
             except Exception: pass # 忽略错误，使用默认
        symbols_for_price_stream.add(gui_display_symbol) # 添加GUI主要显示的价格流

        for t_cfg_base in config.PREDICTION_TARGETS:
            if isinstance(t_cfg_base, dict) and t_cfg_base.get('name'):
                try:
                    # 如果目标是元模型的显示条目，它可能没有symbol和interval
                    if t_cfg_base.get('target_variable_type') == "META_MODEL_DISPLAY":
                        # 元模型通常也关心主要交易对的价格
                        symbols_for_price_stream.add(getattr(config, 'SYMBOL', 'BTCUSDT').upper())
                        continue 
                    
                    full_cfg = config.get_target_config(t_cfg_base['name'])
                    target_symbol = str(full_cfg.get('symbol', gui_display_symbol)).upper()
                    target_interval = str(full_cfg.get('interval', '')).lower()
                    symbols_for_price_stream.add(target_symbol)
                    if target_symbol and target_interval:
                        kline_streams_to_start.add((target_symbol, target_interval))
                except ValueError: pass # 忽略获取配置错误，继续处理下一个
                except Exception: pass

    if shared_binance_twm and shared_binance_twm.is_alive():
        print("--- Main: 准备通过RDM启动价格WebSocket流 ---")
        for sym_price in symbols_for_price_stream:
            realtime_data_manager.start_price_stream(sym_price)
            time.sleep(0.1)
        print("--- Main: 准备通过RDM启动K线WebSocket流 ---")
        for sym_kline, int_kline in kline_streams_to_start:
            realtime_data_manager.start_kline_stream(sym_kline, int_kline)
            time.sleep(0.1)
        # 将 trigger_prediction_on_new_kline (本文件中的函数) 赋值给 RDM 的回调
        realtime_data_manager.start_prediction_for_new_kline = trigger_prediction_on_new_kline
        print("--- Main: WebSocket流已通过RDM配置并尝试启动 ---")
    else:
        print("!!! Main: 共享TWM未成功启动，跳过数据流订阅。")

    # --- 7. 构建并启动GUI ---
    app_instance = None
    try:
        _main_root = tk.Tk()
        # 同时设置到状态管理器
        app_state.set_main_root(_main_root)
        # PredictionApp 的初始化中会调用 gui.build_gui
        app_instance = PredictionApp(_main_root, display_symbol_for_gui=gui_display_symbol)
    except Exception as e_tk_main:
        print(f"!! 主程序Tkinter或PredictionApp初始化错误: {e_tk_main}"); traceback.print_exc()
        stop_shared_twm()
        if '_main_root' in locals() and _main_root and hasattr(_main_root, 'winfo_exists') and _main_root.winfo_exists(): _main_root.destroy()
        exit(1)

    if not stop_event.is_set(): # 确保程序不是一开始就准备退出
        # 初始化分析日志记录器
        print("Main: 初始化分析日志记录器...")
        initialize_loggers()
        print("Main: 分析日志记录器初始化完成。")
        
        print("Main: 准备设置并启动 APScheduler...")
        setup_and_start_apscheduler() # 调用设置和启动函数
        
        # 初始化并启动自动重训练调度器
        print("Main: 准备初始化自动重训练调度器...")
        auto_retrain_scheduler_instance = auto_retrain_scheduler.AutoRetrainScheduler()
        auto_retrain_scheduler_instance.start()
        print("Main: 自动重训练调度器已启动。")
    else:
        print("Main: Stop事件已设置，未启动 APScheduler。")


    # --- 8. 尝试加载元模型 (如果启用) ---
    # prediction.py 模块加载时也会尝试加载，这里可以作为一次显式确认或如果 prediction.py 中未做
    if getattr(config, 'ENABLE_META_MODEL_PREDICTION', False):
        print("Main: 检查元模型加载状态 (来自 prediction.py)...")
        # 通过状态管理器检查元模型加载状态
        if not prediction.global_prediction_state_manager.is_meta_model_loaded_successfully():
            if hasattr(prediction, 'load_meta_model_if_needed') and prediction.load_meta_model_if_needed():
                print("Main: 元模型已在主程序启动流程中成功加载。")
            else:
                print("!!! Main: 警告 - 元模型未能加载。实时元预测将不可用。")
        else:
            print("Main: 元模型已由 prediction.py 成功加载。")
    else:
        print("Main: 元模型预测未在配置中启用。")


    # --- 9. 设置初始GUI状态 ---
    status_msg_init = "准备就绪。"
    status_level_init = "success"
    if not (shared_binance_twm and shared_binance_twm.is_alive()):
        status_msg_init = "警告: WebSocket未连接!"; status_level_init = "error"
    elif not client_ready and (getattr(config, 'API_KEY', None) or getattr(config, 'API_SECRET', None)):
        status_msg_init = "就绪 (认证客户端初始化失败!)"; status_level_init = "warning"
    if getattr(config, 'ENABLE_META_MODEL_PREDICTION', False) and not prediction.global_prediction_state_manager.is_meta_model_loaded_successfully():
        status_msg_init += " (元模型加载失败!)"
        if status_level_init == "success": status_level_init = "warning"

    if hasattr(gui, 'update_status') and _main_root:
        gui.update_gui_safe(gui.update_status, status_msg_init, status_level_init)
        # 确保按钮状态在启动时是正常的 (如果 training_in_progress_flag 初始为 False)
        if not training_in_progress_flag.is_set():
            gui.update_gui_safe(gui.set_button_state, "train_any", tk.NORMAL)
            gui.update_gui_safe(gui.set_button_state, "predict", tk.NORMAL)


    # --- 10. 启动时间价格更新线程 ---
    start_time_price_update_thread()


    
    print("\n实时K线事件驱动的预测已配置。等待K线关闭或计划任务以触发预测...")
    print("启动主事件循环... (Ctrl+C 或关闭窗口退出)");

    # --- 12. 启动Tkinter主事件循环 ---
    try:
        if _main_root and _main_root.winfo_exists():
            _main_root.mainloop()
        else:
            print("! 错误: GUI 窗口未能成功创建或已销毁，无法启动mainloop。")
            on_closing() # 尝试清理
    except KeyboardInterrupt:
        print("\n检测到 Ctrl+C! 正在尝试优雅关闭...")
        on_closing()
    except SystemExit: # 通常由 root.destroy() 或 quit() 引发
        print("主程序: Tkinter mainloop 已通过 SystemExit 正常退出。")
    except tk.TclError as e_tcl_main:
        if "application has been destroyed" not in str(e_tcl_main).lower():
            print(f"主事件循环 TclError: {e_tcl_main}")
            traceback.print_exc()
        on_closing() # 发生TclError时也尝试清理
    except Exception as e_ml:
        print(f"\n!! 主事件循环发生未知错误: {e_ml}");
        traceback.print_exc()
        on_closing() # 未知错误时尝试清理
    finally:
        # --- 最终清理 ---
        symbol_for_log_final = args.config_module if 'args' in locals() else \
                               ('UnknownApp' if not app_instance else app_instance.gui_display_symbol)
        dcm_instance_to_stop = prediction.get_global_dynamic_config_manager()
        if dcm_instance_to_stop and hasattr(dcm_instance_to_stop, 'stop_observer'):
           print("Main (finally): 正在停止 prediction.py 中的 DynamicConfigManager 观察器...")
           dcm_instance_to_stop.stop_observer()
        else:
           print("Main (finally): 未能从 prediction.py 获取到 DynamicConfigManager 实例。")        



        # is_running 属性是 SimApplication (SimMain.py) 的，不是 PredictionApp 的
        # PredictionApp 的关闭主要通过 on_closing
        if 'app_instance' in locals() and app_instance and hasattr(app_instance, 'master') and app_instance.master is not None :
             # 对于 PredictionApp，我们通过检查 stop_event 来判断是否仍在尝试运行
            if not stop_event.is_set(): # 如果 stop_event 未被设置（意味着mainloop可能意外退出）
                print(f"Main [{symbol_for_log_final}]: 主循环意外终止或正在关闭 (stop_event未设置)，执行清理。")
                on_closing() # 确保 on_closing 被调用
        else:
            # 如果 app_instance 未创建或已清理（例如 on_closing 已执行完毕）
            print(f"Main [{symbol_for_log_final}]: 实例未创建、已清理，或 on_closing 已处理。")

        print("\n--- 程序开始最终退出流程 (主 __main__ finally块) ---");
        if not stop_event.is_set(): # 再次确保stop_event被设置
            print("  (stop_event 在finally中被强制设置)")
            stop_event.set()
        
        # 确保共享TWM被停止 (on_closing 中也会调用，这里是双保险)
        stop_shared_twm()
        # 确保动态配置观察器被停止 (atexit钩子也会做，但显式调用更直接)
        if 'dynamic_config_manager' in globals() and dynamic_config_manager:
            dynamic_config_manager.stop_observer()
        # 确保自动重训练调度器被停止
        if 'auto_retrain_scheduler_instance' in locals() and auto_retrain_scheduler_instance:
            print("Main (finally): 正在停止自动重训练调度器...")
            auto_retrain_scheduler_instance.stop()


        print(f"--- 程序已退出 (主 __main__ finally块)。活跃线程数: {threading.active_count()} ---")
        # os._exit(0) # 可以考虑强制退出，以防有daemon线程未完全结束，但通常不推荐

# 使用data_utils.py中的safe_fill_nans函数进行NaN填充
# 不再需要在此定义重复的函数

