# data_utils.py
import pandas as pd
import numpy as np
import math
import os
import json
import logging
from binance.client import Client 
from binance.exceptions import BinanceAPIException, BinanceRequestException 
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import precision_recall_curve, roc_curve, f1_score, precision_score, recall_score, accuracy_score
import traceback
import config
import time
from datetime import datetime, timedelta, timezone
import pandas_ta as pta

# 导入新的阈值优化系统
try:
    from .threshold_optimization import (
        ThresholdOptimizer,
        ThresholdMetadataManager,
        ThresholdApplicator,
        optimize_and_save_threshold,
        load_and_apply_threshold
    )
    THRESHOLD_OPTIMIZATION_AVAILABLE = True
except ImportError as e:
    logging.warning(f"阈值优化系统导入失败: {e}")
    THRESHOLD_OPTIMIZATION_AVAILABLE = False

# 配置日志
logger = logging.getLogger(__name__)
if not logger.handlers:
    # 设置日志级别
    logger.setLevel(logging.INFO)

    # 创建控制台处理器，明确指定UTF-8编码
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(console_handler)

    # 可选：添加文件处理器，明确指定UTF-8编码
    try:
        file_handler = logging.FileHandler('data_utils.log', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except (PermissionError, IOError) as e:
        logger.warning(f"无法创建日志文件: {e}")




def create_meta_target_variable(df_full_hist_data, base_model_config, meta_target_suffix="_meta_target", drop_neutral_targets=False):
    """
    为元模型创建三分类目标变量 (上涨, 下跌, 中性)。

    Args:
        df_full_hist_data (pd.DataFrame): 包含 'close' 列的K线数据。
        base_model_config (dict): 基础模型的配置字典，用于获取 prediction_periods 和 target_threshold。
                                   这些参数将决定元模型目标"明确涨跌"的定义。
        meta_target_suffix (str): 添加到目标列名后的后缀。
        drop_neutral_targets (bool): 是否移除标记为中性(2)的样本。

    Returns:
        tuple: (pd.DataFrame, str or None)
               - DataFrame: 带有新的元模型目标列的DataFrame (已移除未来价格不可用的行，可选移除中性样本)。
               - str: 元模型目标列的名称，如果创建失败则为 None。

    标签体系:
        1: 上涨 (超过阈值)
        0: 下跌 (低于阈值)
        2: 中性 (在阈值范围内)
        -1: 无效 (未来数据不足)
    """
    # 检查df_full_hist_data是否为DataFrame类型
    if not isinstance(df_full_hist_data, pd.DataFrame):
        logger.error("create_meta_target_variable: df_full_hist_data 必须是DataFrame类型，收到的是 %s", type(df_full_hist_data))
        return None, None
        
    if not isinstance(base_model_config, dict):
        logger.error("create_meta_target_variable: base_model_config 必须是字典，收到的是 %s", type(base_model_config))
        return None, None
        
    if 'close' not in df_full_hist_data.columns:
        logger.error("create_meta_target_variable: 'close' 列不存在于DataFrame中。可用列: %s", df_full_hist_data.columns.tolist())
        return None, None

    periods_list = base_model_config.get('prediction_periods', [1])
    if not isinstance(periods_list, list) or not periods_list:
        logger.warning("create_meta_target_variable: base_model_config 中的 'prediction_periods' 无效 (%s)。使用默认值 [1]。", periods_list)
        periods_list = [1]
    period = periods_list[0]

    threshold = base_model_config.get('target_threshold', 0.001) # 默认一个较小的阈值
    target_name_base = base_model_config.get('name', 'unknown_base_model')

    df = df_full_hist_data.copy()
    current_close = df['close']
    future_close_col_temp = f'future_close_{period}p_for_{target_name_base}_meta_temp'
    meta_target_col_name = f'target_{period}p_{target_name_base}{meta_target_suffix}'

    df[future_close_col_temp] = current_close.shift(-period)

    # 定义条件
    cond_invalid = df[future_close_col_temp].isna()
    cond_up = df[future_close_col_temp] > current_close * (1 + threshold)
    cond_down = df[future_close_col_temp] < current_close * (1 - threshold)

    # 标签: 1 = 上涨, 0 = 下跌, 2 = 中性
    df[meta_target_col_name] = np.select(
        [cond_invalid, cond_up, cond_down],
        [-1, 1, 0],         # -1 表示无效, 1 上涨, 0 下跌
        default=2           # 其他情况（在阈值之间）为中性 (2)
    )

    # 首先移除未来价格不可用的行 (标记为-1)
    original_len = len(df)
    df_filtered = df[df[meta_target_col_name] != -1].copy()
    invalid_removed_count = original_len - len(df_filtered)

    if invalid_removed_count > 0:
        logger.info("[MetaTargetCreation for %s]: 移除了 %d 行 (因未来价格不可用)。", target_name_base, invalid_removed_count)

    # 记录过滤前的分布
    if not df_filtered.empty:
        try:
            df_filtered[meta_target_col_name] = df_filtered[meta_target_col_name].astype(int)
            counts_before_filter = df_filtered[meta_target_col_name].value_counts(normalize=True).sort_index()
            dist_str_before = ", ".join([f"Label {idx}:{val*100:.2f}%" for idx, val in counts_before_filter.items()])
            logger.info("[MetaTargetCreation for %s]: 过滤前元目标 '%s' 分布: [%s] (样本数: %d)",
                       target_name_base, meta_target_col_name, dist_str_before, len(df_filtered))
        except (ValueError, TypeError) as e_dist_before:
            logger.warning("[MetaTargetCreation for %s]: 分析过滤前元目标分布时出错: %s", target_name_base, e_dist_before)

    # 根据 drop_neutral_targets 参数决定是否移除中性样本
    neutral_removed_count = 0
    if drop_neutral_targets:
        len_before_neutral_filter = len(df_filtered)
        df_filtered = df_filtered[df_filtered[meta_target_col_name] != 2].copy()
        neutral_removed_count = len_before_neutral_filter - len(df_filtered)

        if neutral_removed_count > 0:
            logger.info("[MetaTargetCreation for %s]: 移除了 %d 行中性样本 (drop_neutral_targets=True)。",
                       target_name_base, neutral_removed_count)

    df_filtered.drop(columns=[future_close_col_temp], inplace=True, errors='ignore')

    if meta_target_col_name not in df_filtered.columns or df_filtered[meta_target_col_name].isnull().all():
        logger.error("create_meta_target_variable (%s): 元目标列 '%s' 创建失败或全为空。", target_name_base, meta_target_col_name)
        return None, None

    # 记录最终分布和标签含义
    try:
        if not df_filtered.empty:
            counts_final = df_filtered[meta_target_col_name].value_counts(normalize=True).sort_index()
            dist_str_final = ", ".join([f"Label {idx}:{val*100:.2f}%" for idx, val in counts_final.items()])

            # 根据 drop_neutral_targets 参数说明最终标签含义
            if drop_neutral_targets:
                label_meaning = "1=上涨, 0=下跌 (已移除中性样本)"
            else:
                label_meaning = "1=上涨, 0=下跌, 2=中性"

            logger.info("[MetaTargetCreation for %s]: 最终元目标 '%s' 分布: [%s] (样本数: %d, %s)",
                       target_name_base, meta_target_col_name, dist_str_final, len(df_filtered), label_meaning)
        else:
            logger.warning("[MetaTargetCreation for %s]: 元目标创建后DataFrame为空。", target_name_base)
    except (ValueError, TypeError) as e_dist_final:
        logger.error("[MetaTargetCreation for %s]: 分析最终元目标分布时出错: %s", target_name_base, e_dist_final)

    return df_filtered, meta_target_col_name




# --- 辅助函数 (interval_to_timedelta, ms_to_dt_str) ---
def interval_to_timedelta(interval_str):
    """将时间间隔字符串转换为 Timedelta 对象
    
    Args:
        interval_str (str): 时间间隔字符串，如 '1m', '4h', '1d', '1w'
        
    Returns:
        pd.Timedelta: 对应的时间间隔对象，如果转换失败则返回默认值(1小时)
    """
    if not isinstance(interval_str, str):
        logger.warning("interval_to_timedelta: 输入不是字符串类型，而是 %s，返回默认值(1小时)", type(interval_str))
        return pd.Timedelta(hours=1)
        
    if not interval_str or len(interval_str) < 2:
        logger.warning("interval_to_timedelta: 输入字符串 '%s' 格式无效，返回默认值(1小时)", interval_str)
        return pd.Timedelta(hours=1)
        
    try:
        num = int(interval_str[:-1])
        unit = interval_str[-1].lower()
        if unit == 'm': return pd.Timedelta(minutes=num)
        elif unit == 'h': return pd.Timedelta(hours=num)
        elif unit == 'd': return pd.Timedelta(days=num)
        elif unit == 'w': return pd.Timedelta(weeks=num)
        else: 
            logger.warning("interval_to_timedelta: 未知的间隔单位: '%s'，返回默认值(1小时)", unit)
            return pd.Timedelta(hours=1)
    except (ValueError, TypeError) as e:
        logger.warning("interval_to_timedelta: 无法解析间隔字符串 '%s': %s，返回默认值(1小时)", interval_str, e)
        return pd.Timedelta(hours=1)
    except IndexError as e:
        logger.warning("interval_to_timedelta: 间隔字符串 '%s' 格式错误: %s，返回默认值(1小时)", interval_str, e)
        return pd.Timedelta(hours=1)

def ms_to_dt_str(ms):
    """将毫秒时间戳转换为可读的日期时间字符串
    
    Args:
        ms (int/float): 毫秒时间戳
        
    Returns:
        str: 格式化的日期时间字符串，如果转换失败则返回错误信息
    """
    if ms is None: 
        return "N/A"
    try:
        if not isinstance(ms, (int, float)) or pd.isna(ms): 
            return "无效输入(非数字)"
        ms_int = int(ms)
        dt_utc = datetime.fromtimestamp(ms_int / 1000, tz=timezone.utc)
        return dt_utc.strftime('%Y-%m-%d %H:%M:%S %Z%z')
    except (ValueError, OSError) as e: 
        logger.debug("ms_to_dt_str: 无效时间戳(%s): %s", ms, e)
        return f"无效时间戳({ms}):{e}"
    except OverflowError as e: 
        logger.debug("ms_to_dt_str: 时间戳超出范围(%s): %s", ms, e)
        return f"时间戳超出范围({ms})"
    except TypeError as e:
        logger.debug("ms_to_dt_str: 类型错误(%s): %s", ms, e)
        return f"类型错误({ms}):{e}"
    except Exception as e_gen: 
        logger.warning("ms_to_dt_str: 转换出错(%s): %s", ms, e_gen)
        return f"转换出错({ms}):{e_gen}"

# --- fetch_binance_history ---
def fetch_binance_history(binance_client, symbol, interval, limit, end_dt=None, start_dt=None):
    """从Binance获取历史K线数据
    
    Args:
        binance_client: Binance API客户端实例
        symbol (str): 交易对符号，如 'BTCUSDT'
        interval (str): K线间隔，如 '1m', '1h', '1d'
        limit (int): 要获取的K线数量上限
        end_dt (datetime, optional): 结束时间
        start_dt (datetime, optional): 开始时间
        
    Returns:
        pd.DataFrame: 包含K线数据的DataFrame，如果获取失败则返回None或空DataFrame
    """
    if not binance_client: 
        logger.error("fetch_binance_history: Binance 客户端未初始化")
        return None
        
    start_ts_ms, end_ts_ms = None, None
    try:
        if start_dt:
            if not isinstance(start_dt, datetime):
                logger.error("fetch_binance_history: start_dt 不是 datetime 对象，而是 %s", type(start_dt))
                return None
            start_dt_aware = start_dt.astimezone(timezone.utc) if start_dt.tzinfo else start_dt.replace(tzinfo=timezone.utc)
            start_ts_ms = int(start_dt_aware.timestamp() * 1000)
        if end_dt:
            if not isinstance(end_dt, datetime):
                logger.error("fetch_binance_history: end_dt 不是 datetime 对象，而是 %s", type(end_dt))
                return None
            end_dt_aware = end_dt.astimezone(timezone.utc) if end_dt.tzinfo else end_dt.replace(tzinfo=timezone.utc)
            end_ts_ms = int(end_dt_aware.timestamp() * 1000)
    except (AttributeError, TypeError, ValueError, OSError) as e_ts:
        logger.error("fetch_binance_history: 时间戳转换错误: %s", e_ts)
        return None
        
    all_klines = []; kline_limit_per_req = 1000; fetched_count = 0
    current_end_ts_for_req = end_ts_ms
    max_retries = 7; retry_delay = 7; total_batches = 0; reached_data_limit = False
    
    logger.info("开始获取 %s@%s 的K线数据，限制: %d 条", symbol, interval, limit)
    
    while fetched_count < limit and not reached_data_limit:
        retries = 0; success = False;
        current_req_limit = min(kline_limit_per_req, limit - fetched_count)
        if current_req_limit <= 0: break
        total_batches += 1
        
        while retries < max_retries and not success:
            try:
                kwargs = {'symbol': symbol, 'interval': interval, 'limit': current_req_limit}
                if current_end_ts_for_req: kwargs['endTime'] = current_end_ts_for_req - 1
                if start_ts_ms: kwargs['startTime'] = start_ts_ms
                
                klines = binance_client.get_klines(**kwargs)
                
                if not klines: 
                    reached_data_limit = True
                    success = True
                    logger.debug("已达到数据限制，没有更多K线数据")
                    break
                    
                if not isinstance(klines, list) or not all(isinstance(k, list) for k in klines) or not klines[0]:
                    logger.error("收到无效K线数据 (类型: %s)", type(klines))
                    break
                    
                earliest_ts_in_batch = klines[0][0]
                all_klines = klines + all_klines
                fetched_count += len(klines)
                current_end_ts_for_req = earliest_ts_in_batch
                success = True
                
                logger.debug("成功获取批次 #%d: %d 条K线，总计: %d/%d", 
                           total_batches, len(klines), fetched_count, limit)
                
            except BinanceAPIException as e:
                if e.code == -1003: 
                    logger.error("IP被禁，请稍后再试。错误代码: %d, 消息: %s", e.code, e.message)
                    return None
                logger.warning("Binance API异常 (重试 %d/%d): 代码 %d, 消息: %s", 
                             retries+1, max_retries, e.code, e.message)
                time.sleep(retry_delay * (retries + 1))
                retries += 1
                
            except BinanceRequestException as e:
                logger.warning("Binance请求异常 (重试 %d/%d): %s", retries+1, max_retries, str(e))
                time.sleep(retry_delay * (retries + 1))
                retries += 1
                
            except (ConnectionError, TimeoutError) as e_conn:
                logger.warning("连接错误 (重试 %d/%d): %s", retries+1, max_retries, str(e_conn))
                time.sleep(retry_delay * (retries + 1))
                retries += 1
                
            except Exception as e_fetch_inner:
                logger.error("未知获取错误: %s - %s", type(e_fetch_inner).__name__, e_fetch_inner)
                traceback.print_exc(limit=1)
                return None
                
        if not success and not reached_data_limit: 
            logger.error("获取批次失败 (%d次尝试)，已停止", max_retries)
            break
            
    if not all_klines: 
        logger.warning("最终未能获取到 %s@%s 的K线数据", symbol, interval)
        return pd.DataFrame()
        
    try:
        # 处理获取到的K线数据
        cols=['ts_ms','o','h','l','c','v','ct_ms','qav','n','tbbav','tbqav','ignore']
        df_out_fetch=pd.DataFrame(all_klines, columns=cols)
        df_out_fetch['timestamp']=pd.to_datetime(df_out_fetch['ts_ms'], unit='ms', utc=True)
        df_out_fetch=df_out_fetch.set_index('timestamp')
        df_out_fetch=df_out_fetch[['o','h','l','c','v','qav','n','tbbav','tbqav']]
        
        # 转换为数值类型
        num_cols = df_out_fetch.columns.tolist()
        for col in num_cols: 
            df_out_fetch[col]=pd.to_numeric(df_out_fetch[col], errors='coerce')
            
        # 处理NaN值
        nan_cols = df_out_fetch.columns[df_out_fetch.isnull().any()].tolist()
        if nan_cols:
            logger.debug("发现含有NaN值的列: %s，将进行填充", nan_cols)
            # 使用安全填充方法替代ffill().bfill()
            for col in nan_cols:
                # 为不同列设置合适的默认值
                default_val = 0
                if col in ['o', 'h', 'l', 'c']:
                    # 价格列使用第一个非NaN值
                    non_nan_vals = df_out_fetch[col].dropna()
                    default_val = non_nan_vals.iloc[0] if len(non_nan_vals) > 0 else 0
                elif col in ['v', 'qav', 'n', 'tbbav', 'tbqav']:
                    default_val = 0  # 交易量默认为0
                
                df_out_fetch[col] = safe_fill_nans(df_out_fetch[col], default_val)
                
        # 重命名列并处理重复索引
        df_out_fetch=df_out_fetch.rename(columns={'o':'open','h':'high','l':'low','c':'close','v':'volume'})
        df_out_fetch=df_out_fetch[~df_out_fetch.index.duplicated(keep='first')]
        df_out_fetch.sort_index(ascending=True, inplace=True)
        
        logger.info("成功获取并处理 %s@%s 的K线数据，共 %d 条", symbol, interval, len(df_out_fetch))
        return df_out_fetch
        
    except (KeyError, ValueError, TypeError) as e_process:
        logger.error("处理K线数据时出错: %s", e_process)
        traceback.print_exc(limit=1)
        return pd.DataFrame()
    except Exception as e_unknown:
        logger.error("处理K线数据时发生未知错误: %s", e_unknown)
        traceback.print_exc(limit=1)
        return pd.DataFrame()

# --- 模块化特征计算辅助函数 ---

def safe_fill_nans(series, default_value=0):
    """使用仅基于历史数据的方法填充NaN"""
    # 检查输入是否为None
    if series is None:
        logger.warning("safe_fill_nans: 输入series为None，返回None")
        return None

    # 检查输入是否为pandas Series
    if not isinstance(series, pd.Series):
        logger.warning("safe_fill_nans: 输入不是pandas Series，尝试转换")
        try:
            series = pd.Series(series)
        except Exception as e:
            logger.error(f"safe_fill_nans: 无法转换输入为pandas Series: {e}")
            return None

    # 如果series为空，直接返回
    if len(series) == 0:
        return series

    # 先尝试向前填充
    filled = series.ffill()
    # 对于开头的NaN，使用该列的历史均值或中位数
    if filled.isna().any():
        # 如果所有值都是NaN，使用默认值
        if series.isna().all():
            hist_value = default_value
        else:
            # 使用非NaN值的均值
            hist_value = series.dropna().mean()
            # 如果均值是NaN或无穷大，使用默认值
            if pd.isna(hist_value) or np.isinf(hist_value):
                hist_value = default_value
        filled = filled.fillna(hist_value)
    return filled

def _add_price_change_features(df_out, cfg, C, interval_str):
    """添加价格变化特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        if _get_cfg('enable_price_change', default_value=False):
            periods = _get_cfg('price_change_periods', default_value=[1, 3, 5, 10])
            if periods and isinstance(periods, list):
                for p_val in periods:
                    if isinstance(p_val, int) and p_val > 0 and len(C) > p_val:
                        try:
                            df_out[f'price_change_{p_val}p'] = C.pct_change(periods=p_val).fillna(0) * 100
                        except (ValueError, TypeError) as e_pc_inner:
                            logger.warning("_add_price_change_features: (%s) 计算price_change_%dp时出错: %s", interval_str, p_val, e_pc_inner)
    except (ValueError, TypeError) as e_pc:
        logger.error("_add_price_change_features: (%s) 计算价格变化特征时出错: %s", interval_str, e_pc)
    except Exception as e_pc_unexpected:
        logger.error("_add_price_change_features: (%s) 计算价格变化特征时发生意外错误: %s", interval_str, e_pc_unexpected)

def _add_volume_features(df_out, cfg, V_feat, interval_str):
    """添加成交量特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        enable_volume = _get_cfg('enable_volume', default_value=False)
        logger.info("_add_volume_features: (%s) enable_volume=%s, 数据长度=%d", interval_str, enable_volume, len(V_feat))

        if enable_volume:
            vol_avg_p = _get_cfg('volume_avg_period', default_value=20)
            if isinstance(vol_avg_p, int) and vol_avg_p > 0:
                # 计算成交量变化
                if len(V_feat) > 1:
                    try:
                        df_out['volume_change_1p'] = V_feat.pct_change(periods=1).fillna(0) * 100
                    except (ValueError, TypeError) as e_vol_change:
                        logger.warning("_add_volume_features: (%s) 计算volume_change_1p时出错: %s", interval_str, e_vol_change)
                        df_out['volume_change_1p'] = 0.0
                else:
                    df_out['volume_change_1p'] = 0.0

                # 计算成交量相对于平均值的比率
                if len(V_feat) >= max(1, vol_avg_p // 2):
                    try:
                        vol_avg = V_feat.rolling(window=vol_avg_p, min_periods=max(1, vol_avg_p // 2)).mean()
                        df_out['volume_vs_avg'] = (V_feat / vol_avg.replace(0, 1e-9)).fillna(1)
                    except (ValueError, TypeError, ZeroDivisionError) as e_vol_avg:
                        logger.warning("_add_volume_features: (%s) 计算volume_vs_avg时出错: %s", interval_str, e_vol_avg)
                        df_out['volume_vs_avg'] = 1.0
    except (ValueError, TypeError) as e_vol:
        logger.error("_add_volume_features: (%s) 计算成交量特征时出错: %s", interval_str, e_vol)
    except Exception as e_vol_unexpected:
        logger.error("_add_volume_features: (%s) 计算成交量特征时发生意外错误: %s", interval_str, e_vol_unexpected)

def _identify_candlestick_patterns(O, H, L, C, interval_str, pattern_thresholds=None):
    """
    识别常见的K线形态

    Args:
        O, H, L, C: 开高低收价格序列
        interval_str: 时间间隔字符串（用于日志）
        pattern_thresholds: 形态识别阈值字典

    Returns:
        pd.Series: 包含K线形态名称的序列
    """
    try:
        # 计算基础K线特征
        body_size = (C - O).abs()
        candle_range = H - L
        upper_shadow = H - C.combine(O, max)
        lower_shadow = O.combine(C, min) - L
        is_green = C > O

        # 避免除零错误
        candle_range_safe = candle_range.replace(0, 1e-9)
        body_ratio = body_size / candle_range_safe
        upper_shadow_ratio = upper_shadow / candle_range_safe
        lower_shadow_ratio = lower_shadow / candle_range_safe

        # 初始化形态名称
        patterns = pd.Series(['Unknown'] * len(C), index=C.index)

        # 使用配置的形态识别阈值，如果没有提供则使用默认值
        if pattern_thresholds is None:
            pattern_thresholds = {}

        DOJI_THRESHOLD = pattern_thresholds.get('doji_threshold', 0.1)          # 十字星：实体小于总范围的10%
        HAMMER_BODY_RATIO = pattern_thresholds.get('hammer_body_ratio', 0.3)       # 锤子线：实体小于总范围的30%
        HAMMER_SHADOW_RATIO = pattern_thresholds.get('hammer_shadow_ratio', 2.0)     # 锤子线：下影线是实体的2倍以上
        SHOOTING_STAR_RATIO = pattern_thresholds.get('shooting_star_ratio', 2.0)     # 流星线：上影线是实体的2倍以上
        MARUBOZU_THRESHOLD = pattern_thresholds.get('marubozu_threshold', 0.95)     # 光头光脚：实体占总范围的95%以上
        SPINNING_TOP_THRESHOLD = pattern_thresholds.get('spinning_top_threshold', 0.6)  # 陀螺：实体小于总范围的60%，且上下影线都存在

        # 1. 十字星 (Doji)
        doji_mask = body_ratio < DOJI_THRESHOLD
        patterns.loc[doji_mask] = 'Doji'

        # 2. 锤子线 (Hammer) - 下影线长，上影线短，实体小，出现在下跌趋势中
        hammer_mask = (
            (body_ratio < HAMMER_BODY_RATIO) &
            (lower_shadow > body_size * HAMMER_SHADOW_RATIO) &
            (upper_shadow < body_size * 0.5) &
            (~doji_mask)  # 排除已识别为十字星的
        )
        patterns.loc[hammer_mask] = 'Hammer'

        # 3. 倒锤子线 (Inverted Hammer) - 上影线长，下影线短，实体小
        inverted_hammer_mask = (
            (body_ratio < HAMMER_BODY_RATIO) &
            (upper_shadow > body_size * HAMMER_SHADOW_RATIO) &
            (lower_shadow < body_size * 0.5) &
            (~doji_mask) & (~hammer_mask)
        )
        patterns.loc[inverted_hammer_mask] = 'Inverted_Hammer'

        # 4. 流星线 (Shooting Star) - 类似倒锤子，但出现在上涨趋势中
        shooting_star_mask = (
            (body_ratio < HAMMER_BODY_RATIO) &
            (upper_shadow > body_size * SHOOTING_STAR_RATIO) &
            (lower_shadow < body_size * 0.3) &
            (~doji_mask) & (~hammer_mask) & (~inverted_hammer_mask)
        )
        patterns.loc[shooting_star_mask] = 'Shooting_Star'

        # 5. 光头光脚 (Marubozu) - 实体占据几乎整个K线
        marubozu_mask = (
            (body_ratio > MARUBOZU_THRESHOLD) &
            (upper_shadow_ratio < 0.02) &
            (lower_shadow_ratio < 0.02)
        )
        green_marubozu = marubozu_mask & is_green
        red_marubozu = marubozu_mask & (~is_green)
        patterns.loc[green_marubozu] = 'Green_Marubozu'
        patterns.loc[red_marubozu] = 'Red_Marubozu'

        # 6. 陀螺 (Spinning Top) - 实体较小，上下影线都存在
        spinning_top_mask = (
            (body_ratio < SPINNING_TOP_THRESHOLD) &
            (upper_shadow_ratio > 0.15) &
            (lower_shadow_ratio > 0.15) &
            (~doji_mask) & (~hammer_mask) & (~inverted_hammer_mask) &
            (~shooting_star_mask) & (~marubozu_mask)
        )
        patterns.loc[spinning_top_mask] = 'Spinning_Top'

        # 7. 长实体 (Long Body) - 实体较大，影线较短
        long_body_mask = (
            (body_ratio > 0.7) &
            (~marubozu_mask) &
            (upper_shadow_ratio < 0.15) &
            (lower_shadow_ratio < 0.15)
        )
        green_long_body = long_body_mask & is_green
        red_long_body = long_body_mask & (~is_green)
        patterns.loc[green_long_body] = 'Green_Long_Body'
        patterns.loc[red_long_body] = 'Red_Long_Body'

        # 8. 普通K线 (Normal) - 不符合特殊形态的K线
        normal_mask = patterns == 'Unknown'
        normal_green = normal_mask & is_green
        normal_red = normal_mask & (~is_green)
        patterns.loc[normal_green] = 'Normal_Green'
        patterns.loc[normal_red] = 'Normal_Red'

        # 统计形态分布
        pattern_counts = patterns.value_counts()
        logger.debug("_identify_candlestick_patterns: (%s) 识别的K线形态分布: %s",
                    interval_str, dict(pattern_counts))

        return patterns

    except Exception as e:
        logger.error("_identify_candlestick_patterns: (%s) K线形态识别出错: %s", interval_str, e)
        # 返回默认值
        return pd.Series(['Unknown'] * len(C), index=C.index)

def _identify_multi_candle_patterns(O, H, L, C, patterns, interval_str, pattern_thresholds=None):
    """
    识别多K线组合形态

    Args:
        O, H, L, C: 开高低收价格序列
        patterns: 单K线形态序列
        interval_str: 时间间隔字符串
        pattern_thresholds: 形态识别阈值配置字典

    Returns:
        pd.Series: 更新后的形态序列
    """
    try:
        if len(C) < 2:
            return patterns

        patterns_updated = patterns.copy()

        # 使用配置的形态识别阈值，如果没有提供则使用默认值
        if pattern_thresholds is None:
            pattern_thresholds = {}

        ENGULFING_BODY_MULTIPLIER = pattern_thresholds.get('engulfing_body_multiplier', 1.2)  # 吞没形态：当前实体比前一个大的倍数
        MORNING_EVENING_STAR_BODY_RATIO = pattern_thresholds.get('morning_evening_star_body_ratio', 0.3)  # 启明星/黄昏星：中间小实体的比例
        DOJI_THRESHOLD_BATAC = pattern_thresholds.get('doji_threshold_batac', 0.1)  # 多K线形态中十字星的阈值

        # 计算基础特征
        is_green = C > O
        body_size = (C - O).abs()

        # 1. 吞没形态 (Engulfing Pattern)
        for i in range(1, len(C)):
            prev_green = is_green.iloc[i-1]
            curr_green = is_green.iloc[i]
            prev_body = body_size.iloc[i-1]
            curr_body = body_size.iloc[i]

            # 看涨吞没：前一根红K线，当前绿K线完全吞没前一根
            if (not prev_green and curr_green and
                C.iloc[i] > O.iloc[i-1] and O.iloc[i] < C.iloc[i-1] and
                curr_body > prev_body * ENGULFING_BODY_MULTIPLIER):  # 使用配置的倍数
                patterns_updated.iloc[i] = 'Bullish_Engulfing'

            # 看跌吞没：前一根绿K线，当前红K线完全吞没前一根
            elif (prev_green and not curr_green and
                  O.iloc[i] > C.iloc[i-1] and C.iloc[i] < O.iloc[i-1] and
                  curr_body > prev_body * ENGULFING_BODY_MULTIPLIER):  # 使用配置的倍数
                patterns_updated.iloc[i] = 'Bearish_Engulfing'

        # 2. 启明星/黄昏星 (Morning/Evening Star) - 需要3根K线
        if len(C) >= 3:
            for i in range(2, len(C)):
                # 启明星：下跌 -> 小实体 -> 上涨
                if (not is_green.iloc[i-2] and  # 第一根：红K线
                    body_size.iloc[i-1] < body_size.iloc[i-2] * MORNING_EVENING_STAR_BODY_RATIO and  # 第二根：小实体，使用配置的比例
                    is_green.iloc[i] and  # 第三根：绿K线
                    C.iloc[i] > (O.iloc[i-2] + C.iloc[i-2]) / 2):  # 第三根收盘价超过第一根中点
                    patterns_updated.iloc[i] = 'Morning_Star'

                # 黄昏星：上涨 -> 小实体 -> 下跌
                elif (is_green.iloc[i-2] and  # 第一根：绿K线
                      body_size.iloc[i-1] < body_size.iloc[i-2] * MORNING_EVENING_STAR_BODY_RATIO and  # 第二根：小实体，使用配置的比例
                      not is_green.iloc[i] and  # 第三根：红K线
                      C.iloc[i] < (O.iloc[i-2] + C.iloc[i-2]) / 2):  # 第三根收盘价低于第一根中点
                    patterns_updated.iloc[i] = 'Evening_Star'

        # 统计多K线形态
        multi_patterns = patterns_updated[patterns_updated.str.contains('Engulfing|Star', na=False)]
        if len(multi_patterns) > 0:
            logger.debug("_identify_multi_candle_patterns: (%s) 识别到 %d 个多K线组合形态",
                        interval_str, len(multi_patterns))

        return patterns_updated

    except Exception as e:
        logger.error("_identify_multi_candle_patterns: (%s) 多K线形态识别出错: %s", interval_str, e)
        return patterns

def _add_candle_features(df_out, cfg, C, H, L, O, interval_str):
    """添加K线形态特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        candle_cols_to_init = ['body_size','candle_range','upper_shadow','lower_shadow','is_green_candle','is_doji','close_pos_in_candle',
                       'body_size_norm','upper_shadow_norm','lower_shadow_norm','candle_range_norm']
        default_candle_vals = {k: 0.0 for k in candle_cols_to_init}; default_candle_vals['close_pos_in_candle'] = 0.5
        for col_c, def_val_c in default_candle_vals.items(): df_out[col_c] = def_val_c

        # 初始化K线形态名称列
        df_out['candlestick_pattern_name'] = 'Unknown'

        if _get_cfg('enable_candle', default_value=False):
            try:
                if len(C)>0 and len(O)>0:
                    df_out['body_size']=(C-O).abs()
                    df_out['is_green_candle']=(C>O).astype(int)
                if len(H)>0 and len(L)>0:
                    df_out['candle_range']=H-L
                    df_out['upper_shadow']=H-C.combine(O,max)
                    df_out['lower_shadow']=O.combine(C,min) - L
                    candle_range_safe = df_out['candle_range'].replace(0,1e-9)
                    df_out['is_doji']=(df_out['body_size'] < candle_range_safe*0.1).astype(int)
                    hl_diff=df_out['candle_range'].replace(0,1e-9)
                    df_out['close_pos_in_candle']=((C-L)/hl_diff).fillna(0.5).clip(0,1)

                # K线形态识别
                if _get_cfg('enable_pattern_recognition', default_value=True):
                    try:
                        logger.debug("_add_candle_features: (%s) 开始K线形态识别", interval_str)

                        # 获取形态识别阈值配置（现在参数直接在配置顶层）
                        pattern_thresholds = {
                            'doji_threshold': _get_cfg('doji_threshold', 0.1),
                            'hammer_body_ratio': _get_cfg('hammer_body_ratio', 0.3),
                            'hammer_shadow_ratio': _get_cfg('hammer_shadow_ratio', 2.0),
                            'shooting_star_ratio': _get_cfg('shooting_star_ratio', 2.0),
                            'marubozu_threshold': _get_cfg('marubozu_threshold', 0.9),
                            'spinning_top_threshold': _get_cfg('spinning_top_threshold', 0.6),
                            'engulfing_body_multiplier': _get_cfg('engulfing_body_multiplier', 1.2),
                            'morning_evening_star_body_ratio': _get_cfg('morning_evening_star_body_ratio', 0.3),
                            'doji_threshold_batac': _get_cfg('doji_threshold_batac', 0.1)
                        }

                        # 单K线形态识别
                        single_patterns = _identify_candlestick_patterns(O, H, L, C, interval_str, pattern_thresholds)

                        # 多K线组合形态识别
                        final_patterns = _identify_multi_candle_patterns(O, H, L, C, single_patterns, interval_str, pattern_thresholds)

                        # 更新形态名称列
                        df_out['candlestick_pattern_name'] = final_patterns

                        # 统计最终形态分布
                        pattern_counts = final_patterns.value_counts()
                        top_patterns = pattern_counts.head(5)
                        logger.info("_add_candle_features: (%s) K线形态识别完成，识别到 %d 种形态，前5种: %s",
                                   interval_str, len(pattern_counts), dict(top_patterns))

                    except Exception as e_pattern:
                        logger.warning("_add_candle_features: (%s) K线形态识别出错: %s", interval_str, e_pattern)
                        df_out['candlestick_pattern_name'] = 'Unknown'

            except (ValueError, TypeError, ZeroDivisionError) as e_candle_basic:
                logger.warning("_add_candle_features: (%s) 计算基本K线特征时出错: %s", interval_str, e_candle_basic)

            atr_p_for_candle_norm = _get_cfg('atr_period', default_value=14)
            if isinstance(atr_p_for_candle_norm, int) and atr_p_for_candle_norm > 0:
                try:
                    if len(H) >= atr_p_for_candle_norm and len(L) >= atr_p_for_candle_norm and len(C) >= atr_p_for_candle_norm:
                        atr_for_candle = pta.atr(H,L,C,length=atr_p_for_candle_norm)
                        if atr_for_candle is not None and not atr_for_candle.isnull().all():
                            # 使用安全填充替代bfill
                            atr_replaced = atr_for_candle.replace(0,1e-9)
                            atr_safe = safe_fill_nans(atr_replaced, default_value=1e-9)
                            df_out['body_size_norm']=(df_out['body_size']/atr_safe).fillna(0)
                            df_out['upper_shadow_norm']=(df_out['upper_shadow']/atr_safe).fillna(0)
                            df_out['lower_shadow_norm']=(df_out['lower_shadow']/atr_safe).fillna(0)
                            df_out['candle_range_norm']=(df_out['candle_range']/atr_safe).fillna(0)
                except (ValueError, TypeError, ZeroDivisionError) as e_candle_norm:
                    logger.warning("_add_candle_features: (%s) 计算K线形态归一化部分出错: %s", interval_str, e_candle_norm)
                except Exception as e_candle_norm_unexpected:
                    logger.error("_add_candle_features: (%s) 计算K线形态归一化部分发生意外错误: %s", interval_str, e_candle_norm_unexpected)

        # 添加平滑K线特征
        if _get_cfg('enable_candle', default_value=False):
            smoothing_period_candle = 3; min_periods_candle = max(1, smoothing_period_candle -1)
            try:
                if 'upper_shadow' in df_out.columns and len(df_out['upper_shadow']) >= min_periods_candle:
                    rolling_mean = df_out['upper_shadow'].rolling(window=smoothing_period_candle, min_periods=min_periods_candle).mean()
                    df_out[f'upper_shadow_smooth{smoothing_period_candle}p'] = safe_fill_nans(rolling_mean, default_value=0)
                else:
                    df_out[f'upper_shadow_smooth{smoothing_period_candle}p'] = df_out.get('upper_shadow', pd.Series(0.0, index=df_out.index))

                if 'close_pos_in_candle' in df_out.columns and len(df_out['close_pos_in_candle']) >= min_periods_candle:
                    rolling_mean = df_out['close_pos_in_candle'].rolling(window=smoothing_period_candle, min_periods=min_periods_candle).mean()
                    df_out[f'close_pos_in_candle_smooth{smoothing_period_candle}p'] = safe_fill_nans(rolling_mean, default_value=0.5)
                else:
                    df_out[f'close_pos_in_candle_smooth{smoothing_period_candle}p'] = df_out.get('close_pos_in_candle', pd.Series(0.5, index=df_out.index))

                if 'body_size' in df_out.columns and len(df_out['body_size']) >= min_periods_candle:
                    rolling_mean = df_out['body_size'].rolling(window=smoothing_period_candle, min_periods=min_periods_candle).mean()
                    df_out[f'body_size_smooth{smoothing_period_candle}p'] = safe_fill_nans(rolling_mean, default_value=0)
                else:
                    df_out[f'body_size_smooth{smoothing_period_candle}p'] = df_out.get('body_size', pd.Series(0.0, index=df_out.index))
            except (ValueError, TypeError) as e_candle_smooth:
                logger.warning("_add_candle_features: (%s) 计算K线平滑特征时出错: %s", interval_str, e_candle_smooth)
    except (ValueError, TypeError) as e_candle_sec:
        logger.error("_add_candle_features: (%s) K线特征部分出错: %s", interval_str, e_candle_sec)
    except Exception as e_candle_sec_unexpected:
        logger.error("_add_candle_features: (%s) K线特征部分发生意外错误: %s", interval_str, e_candle_sec_unexpected)

def _add_technical_indicators(df_out, cfg, C, H, L, interval_str):
    """添加技术指标特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        enable_ta_in_cfg = _get_cfg('enable_ta', default_value=False)
        hma_period = _get_cfg('hma_period', 14)
        rsi_period = _get_cfg('rsi_period', 14)
        logger.info("_add_technical_indicators: (%s) enable_ta=%s, hma_period=%s, rsi_period=%s, 数据长度=%d",
                   interval_str, enable_ta_in_cfg, hma_period, rsi_period, len(C))

        if enable_ta_in_cfg:
            rsi_p = _get_cfg('rsi_period', 14); macd_f, macd_s, macd_sg = _get_cfg('macd_fast', 12), _get_cfg('macd_slow', 26), _get_cfg('macd_sign', 9)
            atr_p_ta = _get_cfg('atr_period', 14); stoch_k, stoch_d, stoch_sk = _get_cfg('stoch_k', 14), _get_cfg('stoch_d', 3), _get_cfg('stoch_smooth_k', 3)
            hma_p = _get_cfg('hma_period', 14); kc_p = _get_cfg('kc_period', 20); kc_atr_p = _get_cfg('kc_atr_period', 10); kc_mult = _get_cfg('kc_multiplier', 2.0)
            # 🚨 修复：为WILLR和CCI使用专门的周期参数
            willr_p = _get_cfg('willr_period', 14)  # Williams %R 专门周期
            cci_p = _get_cfg('cci_period', 14)      # CCI 专门周期
            cci_constant_cfg = _get_cfg('cci_constant', 0.015)
            enable_ta_derived = _get_cfg('enable_ta_derived_features', default_value=True)

            # 计算HMA
            if isinstance(hma_p, int) and hma_p > 0 and len(C) >= hma_p:
                try:
                    df_out[f'HMA_{hma_p}'] = pta.hma(C, length=hma_p)
                except (ValueError, TypeError) as e_hma:
                    logger.warning("_add_technical_indicators: (%s) 计算HMA时出错: %s", interval_str, e_hma)

            # 计算KC
            if isinstance(kc_p, int) and kc_p > 0 and isinstance(kc_atr_p, int) and kc_atr_p > 0 and isinstance(kc_mult, (float, int)) and kc_mult > 0 and len(C) >= max(kc_p, kc_atr_p):
                try:
                    kc_df = pta.kc(H, L, C, length=kc_p, atr_length=kc_atr_p, scalar=kc_mult, mamode="ema")
                    if kc_df is not None and not kc_df.empty:
                        for col in kc_df.columns: df_out[col] = kc_df[col]
                except (ValueError, TypeError) as e_kc:
                    logger.warning("_add_technical_indicators: (%s) 计算KC时出错: %s", interval_str, e_kc)

            # 计算RSI
            if isinstance(rsi_p, int) and rsi_p > 0 and len(C) >= rsi_p:
                try:
                    df_out[f'RSI_{rsi_p}'] = pta.rsi(C, length=rsi_p)
                except (ValueError, TypeError) as e_rsi:
                    logger.warning("_add_technical_indicators: (%s) 计算RSI时出错: %s", interval_str, e_rsi)

            # 计算MACD
            if isinstance(macd_f,int) and macd_f > 0 and isinstance(macd_s,int) and macd_s > macd_f and isinstance(macd_sg,int) and macd_sg > 0 and len(C) >= macd_s + macd_sg -1 :
                try:
                    macd_df = pta.macd(C, fast=macd_f, slow=macd_s, signal=macd_sg)
                    if macd_df is not None and not macd_df.empty:
                        for col in macd_df.columns: df_out[col] = macd_df[col]
                except (ValueError, TypeError) as e_macd:
                    logger.warning("_add_technical_indicators: (%s) 计算MACD时出错: %s", interval_str, e_macd)

            # 计算ATR
            if isinstance(atr_p_ta, int) and atr_p_ta > 0 and len(H) >= atr_p_ta and len(L) >= atr_p_ta and len(C) >= atr_p_ta:
                try:
                    df_out[f'ATRr_{atr_p_ta}'] = pta.atr(H, L, C, length=atr_p_ta)
                except (ValueError, TypeError) as e_atr:
                    logger.warning("_add_technical_indicators: (%s) 计算ATR时出错: %s", interval_str, e_atr)

            # 计算STOCH
            if isinstance(stoch_k,int) and stoch_k > 0 and isinstance(stoch_d,int) and stoch_d > 0 and isinstance(stoch_sk,int) and stoch_sk > 0 and len(H) >= stoch_k :
                try:
                    stoch_df = pta.stoch(H, L, C, k=stoch_k, d=stoch_d, smooth_k=stoch_sk)
                    if stoch_df is not None and not stoch_df.empty:
                        for col in stoch_df.columns: df_out[col] = stoch_df[col]
                except (ValueError, TypeError) as e_stoch:
                    logger.warning("_add_technical_indicators: (%s) 计算STOCH时出错: %s", interval_str, e_stoch)

            # 🚨 修复：计算WILLR，使用专门的周期参数
            if isinstance(willr_p, int) and willr_p > 0 and len(H) >= willr_p and len(L) >= willr_p and len(C) >= willr_p:
                try:
                    df_out[f'WILLR_{willr_p}'] = pta.willr(H, L, C, length=willr_p)
                    logger.debug("_add_technical_indicators: (%s) 成功计算WILLR_%d", interval_str, willr_p)
                except (ValueError, TypeError) as e_willr:
                    logger.warning("_add_technical_indicators: (%s) 计算WILLR_%d时出错: %s", interval_str, willr_p, e_willr)

            # 🚨 修复：计算CCI，使用专门的周期参数
            if isinstance(cci_p, int) and cci_p > 0 and len(H) >= cci_p and len(L) >= cci_p and len(C) >= cci_p:
                try:
                    df_out[f'CCI_{cci_p}_{cci_constant_cfg}'] = pta.cci(H, L, C, length=cci_p, constant=cci_constant_cfg)
                    logger.debug("_add_technical_indicators: (%s) 成功计算CCI_%d_%s", interval_str, cci_p, cci_constant_cfg)
                except (ValueError, TypeError) as e_cci:
                    logger.warning("_add_technical_indicators: (%s) 计算CCI_%d时出错: %s", interval_str, cci_p, e_cci)

            # 🎯 新增：EMA距离特征（适用于UP和DOWN模型）
            # 根据模型类型选择合适的参数
            ema_short_period = _get_cfg('ema_short_period_up', _get_cfg('ema_short_period_down', 10))
            ema_long_period = _get_cfg('ema_long_period_up', _get_cfg('ema_long_period_down', 30))

            if (isinstance(ema_short_period, int) and ema_short_period > 0 and
                isinstance(ema_long_period, int) and ema_long_period > 0 and
                len(C) >= max(ema_short_period, ema_long_period)):
                try:
                    ema_short = pta.ema(C, length=ema_short_period)
                    ema_long = pta.ema(C, length=ema_long_period)

                    if ema_short is not None and ema_long is not None:
                        # 计算EMA距离（绝对值和百分比）
                        ema_distance_abs = ema_short - ema_long
                        ema_distance_pct = ((ema_short - ema_long) / ema_long.replace(0, 1e-9)) * 100

                        df_out['ema_distance_abs'] = ema_distance_abs.fillna(0)
                        df_out['ema_distance_pct'] = ema_distance_pct.fillna(0)

                        # 计算多头趋势强度（距离越大且短期在上，代表越强的多头趋势）
                        bullish_strength = np.where(ema_distance_abs > 0, ema_distance_pct, 0)
                        df_out['ema_bullish_strength'] = pd.Series(bullish_strength, index=df_out.index).fillna(0)

                        # 🎯 新增：计算空头趋势强度（距离越大且短期在下，代表越强的空头趋势）
                        bearish_strength = np.where(ema_distance_abs < 0, -ema_distance_pct, 0)
                        df_out['ema_bearish_strength'] = pd.Series(bearish_strength, index=df_out.index).fillna(0)

                        logger.debug("_add_technical_indicators: (%s) 成功计算EMA距离特征 (短期:%d, 长期:%d)",
                                   interval_str, ema_short_period, ema_long_period)
                    else:
                        df_out['ema_distance_abs'] = 0.0
                        df_out['ema_distance_pct'] = 0.0
                        df_out['ema_bullish_strength'] = 0.0
                        df_out['ema_bearish_strength'] = 0.0
                except (ValueError, TypeError) as e_ema_distance:
                    logger.warning("_add_technical_indicators: (%s) 计算EMA距离特征时出错: %s", interval_str, e_ema_distance)
                    df_out['ema_distance_abs'] = 0.0
                    df_out['ema_distance_pct'] = 0.0
                    df_out['ema_bullish_strength'] = 0.0
                    df_out['ema_bearish_strength'] = 0.0
            else:
                df_out['ema_distance_abs'] = 0.0
                df_out['ema_distance_pct'] = 0.0
                df_out['ema_bullish_strength'] = 0.0
                df_out['ema_bearish_strength'] = 0.0

            # 🎯 新增：布林带突破强度特征（适用于UP和DOWN模型）
            bb_period = _get_cfg('bb_period', 20)
            bb_std = _get_cfg('bb_std', 2.0)
            if (isinstance(bb_period, int) and bb_period > 0 and
                isinstance(bb_std, (int, float)) and bb_std > 0 and
                len(C) >= bb_period):
                try:
                    bb_df = pta.bbands(C, length=bb_period, std=bb_std)

                    if bb_df is not None and not bb_df.empty:
                        # 获取布林带上轨和下轨
                        bb_upper_col = [col for col in bb_df.columns if 'BBU' in col.upper() or 'UPPER' in col.upper()]
                        bb_lower_col = [col for col in bb_df.columns if 'BBL' in col.upper() or 'LOWER' in col.upper()]

                        if bb_upper_col:
                            bb_upper = bb_df[bb_upper_col[0]]

                            # 计算价格突破布林带上轨的强度
                            # 强度 = (价格 - 上轨) / 上轨 * 100，只有当价格 > 上轨时才为正值
                            upper_breakout_strength = np.where(
                                C > bb_upper,
                                ((C - bb_upper) / bb_upper.replace(0, 1e-9)) * 100,
                                0
                            )
                            df_out['bb_upper_breakout_strength'] = pd.Series(upper_breakout_strength, index=df_out.index).fillna(0)

                            # 额外特征：价格相对于布林带上轨的位置
                            price_vs_bb_upper = ((C - bb_upper) / bb_upper.replace(0, 1e-9)) * 100
                            df_out['price_vs_bb_upper_pct'] = price_vs_bb_upper.fillna(0)
                        else:
                            df_out['bb_upper_breakout_strength'] = 0.0
                            df_out['price_vs_bb_upper_pct'] = 0.0

                        # 🎯 新增：DOWN模型专用 - 布林带下轨突破特征
                        if bb_lower_col:
                            bb_lower = bb_df[bb_lower_col[0]]

                            # 计算价格突破布林带下轨的强度
                            # 强度 = (下轨 - 价格) / 下轨 * 100，只有当价格 < 下轨时才为正值
                            lower_breakout_strength = np.where(
                                C < bb_lower,
                                ((bb_lower - C) / bb_lower.replace(0, 1e-9)) * 100,
                                0
                            )
                            df_out['bb_lower_breakout_strength'] = pd.Series(lower_breakout_strength, index=df_out.index).fillna(0)

                            # 额外特征：价格相对于布林带下轨的位置
                            price_vs_bb_lower = ((C - bb_lower) / bb_lower.replace(0, 1e-9)) * 100
                            df_out['price_vs_bb_lower_pct'] = price_vs_bb_lower.fillna(0)
                        else:
                            df_out['bb_lower_breakout_strength'] = 0.0
                            df_out['price_vs_bb_lower_pct'] = 0.0

                        logger.debug("_add_technical_indicators: (%s) 成功计算布林带突破强度特征 (周期:%d, 标准差:%.1f)",
                                   interval_str, bb_period, bb_std)
                    else:
                        df_out['bb_upper_breakout_strength'] = 0.0
                        df_out['price_vs_bb_upper_pct'] = 0.0
                        df_out['bb_lower_breakout_strength'] = 0.0
                        df_out['price_vs_bb_lower_pct'] = 0.0
                except (ValueError, TypeError) as e_bb:
                    logger.warning("_add_technical_indicators: (%s) 计算布林带突破强度特征时出错: %s", interval_str, e_bb)
                    df_out['bb_upper_breakout_strength'] = 0.0
                    df_out['price_vs_bb_upper_pct'] = 0.0
                    df_out['bb_lower_breakout_strength'] = 0.0
                    df_out['price_vs_bb_lower_pct'] = 0.0
            else:
                df_out['bb_upper_breakout_strength'] = 0.0
                df_out['price_vs_bb_upper_pct'] = 0.0
                df_out['bb_lower_breakout_strength'] = 0.0
                df_out['price_vs_bb_lower_pct'] = 0.0

            # 重命名列和设置默认值 (传入新的周期参数)
            _rename_and_set_ta_defaults(df_out, C, hma_p, kc_p, kc_mult, rsi_p, macd_f, macd_s, macd_sg, atr_p_ta, stoch_k, stoch_d, stoch_sk, willr_p, cci_p, cci_constant_cfg, interval_str)

            # 计算衍生指标（如果启用）
            if enable_ta_derived:
                _calculate_ta_derived_features(df_out, C, hma_p, kc_p, kc_mult, interval_str)

    except (ValueError, TypeError) as e_ta_sec:
        logger.error("_add_technical_indicators: (%s) TA指标部分出错: %s", interval_str, e_ta_sec)
    except Exception as e_ta_sec_unexpected:
        logger.error("_add_technical_indicators: (%s) TA指标部分发生意外错误: %s", interval_str, e_ta_sec_unexpected)
        logger.debug(traceback.format_exc(limit=1))

def _rename_and_set_ta_defaults(df_out, C, hma_p, kc_p, kc_mult, rsi_p, macd_f, macd_s, macd_sg, atr_p_ta, stoch_k, stoch_d, stoch_sk, willr_p, cci_p, cci_constant_cfg, interval_str):
    """重命名TA列并设置默认值 - 修复版，支持独立的WILLR和CCI周期"""
    try:
        # 重命名列
        rename_map = {
            f'KCLe_{kc_p}_{kc_mult}': 'KC_lower',
            f'KCBe_{kc_p}_{kc_mult}': 'KC_middle',
            f'KCUe_{kc_p}_{kc_mult}': 'KC_upper',
            f'MACD_{macd_f}_{macd_s}_{macd_sg}': 'MACD',
            f'MACDh_{macd_f}_{macd_s}_{macd_sg}': 'MACD_histogram',
            f'MACDs_{macd_f}_{macd_s}_{macd_sg}': 'MACD_signal',
            f'STOCHk_{stoch_k}_{stoch_d}_{stoch_sk}': 'STOCH_k',
            f'STOCHd_{stoch_k}_{stoch_d}_{stoch_sk}': 'STOCH_d'
        }
        df_out.rename(columns=rename_map, inplace=True)

        # 🚨 修复：设置默认值，使用正确的周期参数
        ta_defaults = {
            f'HMA_{hma_p}': C.iloc[-1] if len(C) > 0 else 0.0,
            'KC_lower': C.iloc[-1] * 0.98 if len(C) > 0 else 0.0,
            'KC_middle': C.iloc[-1] if len(C) > 0 else 0.0,
            'KC_upper': C.iloc[-1] * 1.02 if len(C) > 0 else 0.0,
            f'RSI_{rsi_p}': 50.0,
            'MACD': 0.0,
            'MACD_histogram': 0.0,
            'MACD_signal': 0.0,
            f'ATRr_{atr_p_ta}': 0.0,
            'STOCH_k': 50.0,
            'STOCH_d': 50.0,
            f'WILLR_{willr_p}': -50.0,                    # 使用专门的WILLR周期
            f'CCI_{cci_p}_{cci_constant_cfg}': 0.0,       # 使用专门的CCI周期
            # 🎯 新增：EMA距离特征默认值（UP和DOWN模型通用）
            'ema_distance_abs': 0.0,
            'ema_distance_pct': 0.0,
            'ema_bullish_strength': 0.0,
            'ema_bearish_strength': 0.0,
            # 🎯 新增：布林带突破强度特征默认值（UP和DOWN模型通用）
            'bb_upper_breakout_strength': 0.0,
            'price_vs_bb_upper_pct': 0.0,
            'bb_lower_breakout_strength': 0.0,
            'price_vs_bb_lower_pct': 0.0,
        }

        for col, default_val in ta_defaults.items():
            if col in df_out.columns:
                df_out[col] = safe_fill_nans(df_out[col], default_value=default_val)
            else:
                df_out[col] = default_val

    except Exception as e_rename:
        logger.warning("_rename_and_set_ta_defaults: (%s) 重命名和设置默认值时出错: %s", interval_str, e_rename)

def _calculate_ta_derived_features(df_out, C, hma_p, kc_p, kc_mult, interval_str):
    """计算TA衍生特征"""
    try:
        # 计算价格相对于HMA的位置
        if f'HMA_{hma_p}' in df_out.columns and len(C) > 0:
            hma_col = df_out[f'HMA_{hma_p}']
            df_out['price_vs_hma'] = ((C - hma_col) / hma_col.replace(0, 1e-9)).fillna(0)
        else:
            df_out['price_vs_hma'] = 0.0

        # 计算价格在KC通道中的位置
        if all(col in df_out.columns for col in ['KC_lower', 'KC_upper']) and len(C) > 0:
            kc_range = (df_out['KC_upper'] - df_out['KC_lower']).replace(0, 1e-9)
            df_out['price_pos_in_kc'] = ((C - df_out['KC_lower']) / kc_range).fillna(0.5).clip(0, 1)
        else:
            df_out['price_pos_in_kc'] = 0.5

        # 计算MACD信号
        if all(col in df_out.columns for col in ['MACD', 'MACD_signal']):
            df_out['macd_above_signal'] = (df_out['MACD'] > df_out['MACD_signal']).astype(int)
        else:
            df_out['macd_above_signal'] = 0

    except Exception as e_derived:
        logger.warning("_calculate_ta_derived_features: (%s) 计算衍生特征时出错: %s", interval_str, e_derived)

def _add_time_features(df_out, cfg, interval_str):
    """添加时间特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        if _get_cfg('enable_time', default_value=False):
            try:
                df_out['hour'] = df_out.index.hour
                df_out['day_of_week'] = df_out.index.dayofweek
                df_out['is_weekend'] = (df_out.index.dayofweek >= 5).astype(int)

                # 添加三角函数编码的时间特征
                if _get_cfg('enable_time_trigonometric', default_value=True):
                    # 小时的周期性编码 (24小时周期)
                    df_out['hour_sin'] = np.sin(2 * np.pi * df_out['hour'] / 24)
                    df_out['hour_cos'] = np.cos(2 * np.pi * df_out['hour'] / 24)

                    # 星期的周期性编码 (7天周期)
                    df_out['day_sin'] = np.sin(2 * np.pi * df_out['day_of_week'] / 7)
                    df_out['day_cos'] = np.cos(2 * np.pi * df_out['day_of_week'] / 7)
                else:
                    # 如果不启用三角函数编码，设置默认值
                    df_out['hour_sin'] = 0.0
                    df_out['hour_cos'] = 1.0
                    df_out['day_sin'] = 0.0
                    df_out['day_cos'] = 1.0

            except (AttributeError, ValueError, TypeError) as e_time:
                logger.warning("_add_time_features: (%s) 计算时间特征时出错: %s", interval_str, e_time)
                df_out['hour'] = 0
                df_out['day_of_week'] = 0
                df_out['is_weekend'] = 0
                df_out['hour_sin'] = 0.0
                df_out['hour_cos'] = 1.0
                df_out['day_sin'] = 0.0
                df_out['day_cos'] = 1.0
        else:
            df_out['hour'] = 0
            df_out['day_of_week'] = 0
            df_out['is_weekend'] = 0
            df_out['hour_sin'] = 0.0
            df_out['hour_cos'] = 1.0
            df_out['day_sin'] = 0.0
            df_out['day_cos'] = 1.0
    except (ValueError, TypeError) as e_time_sec:
        logger.error("_add_time_features: (%s) 时间特征部分出错: %s", interval_str, e_time_sec)
    except Exception as e_time_sec_unexpected:
        logger.error("_add_time_features: (%s) 时间特征部分发生意外错误: %s", interval_str, e_time_sec_unexpected)

def _add_fund_flow_features(df_out, cfg, interval_str):
    """添加资金流向特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 获取资金流平滑周期配置（无论是否启用fund_flow都需要）
        smoothing_period = _get_cfg('fund_flow_ratio_smoothing_period', default_value=5)

        if _get_cfg('enable_fund_flow', default_value=False):

            # 这里可以添加资金流向相关的特征计算
            # 目前设置为默认值，但保留了配置参数的使用
            df_out['fund_flow_indicator'] = 0.0

            # 示例：如果有taker_buy_ratio数据，可以进行平滑处理
            if 'tbbav' in df_out.columns and 'qav' in df_out.columns:
                try:
                    # 计算主动买入比例
                    taker_buy_ratio = df_out['tbbav'] / df_out['qav'].replace(0, 1e-9)
                    df_out['taker_buy_ratio'] = taker_buy_ratio.fillna(0.5).clip(0, 1)

                    # 应用平滑处理
                    if len(df_out) >= smoothing_period:
                        smoothed_ratio = df_out['taker_buy_ratio'].rolling(
                            window=smoothing_period,
                            min_periods=max(1, smoothing_period // 2)
                        ).mean()
                        df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = safe_fill_nans(smoothed_ratio, default_value=0.5)
                    else:
                        df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = df_out['taker_buy_ratio']

                except (ValueError, TypeError, ZeroDivisionError) as e_taker_ratio:
                    logger.warning("_add_fund_flow_features: (%s) 计算主动买入比例时出错: %s", interval_str, e_taker_ratio)
                    df_out['taker_buy_ratio'] = 0.5
                    df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = 0.5
            else:
                df_out['taker_buy_ratio'] = 0.5
                df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = 0.5
        else:
            df_out['fund_flow_indicator'] = 0.0
            df_out['taker_buy_ratio'] = 0.5
            # 使用配置中的平滑周期，而不是硬编码
            df_out[f'taker_buy_ratio_smooth{smoothing_period}p'] = 0.5
    except (ValueError, TypeError) as e_fund_sec:
        logger.error("_add_fund_flow_features: (%s) 资金流向特征部分出错: %s", interval_str, e_fund_sec)
    except Exception as e_fund_sec_unexpected:
        logger.error("_add_fund_flow_features: (%s) 资金流向特征部分发生意外错误: %s", interval_str, e_fund_sec_unexpected)

def _add_trend_features(df_out, cfg, C, H, L, interval_str):
    """添加趋势斜率特征"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        if _get_cfg('enable_trend_slope', default_value=False):
            # 获取趋势斜率周期参数
            trend_slope_period_1 = _get_cfg('trend_slope_period_1', default_value=8)
            trend_slope_period_2 = _get_cfg('trend_slope_period_2', default_value=17)

            # 计算两个趋势斜率特征
            for period_name, period_value in [('1', trend_slope_period_1), ('2', trend_slope_period_2)]:
                if isinstance(period_value, int) and period_value > 0 and len(C) >= period_value:
                    try:
                        # 计算趋势斜率
                        slope = C.rolling(window=period_value).apply(
                            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == period_value else 0,
                            raw=False
                        )
                        df_out[f'trend_slope_period_{period_name}'] = slope.fillna(0)
                    except (ValueError, TypeError) as e_trend_inner:
                        logger.warning("_add_trend_features: (%s) 计算trend_slope_period_%s时出错: %s",
                                     interval_str, period_name, e_trend_inner)
                        df_out[f'trend_slope_period_{period_name}'] = 0.0
                else:
                    df_out[f'trend_slope_period_{period_name}'] = 0.0

            # 添加ADX趋势特征
            if _get_cfg('enable_adx_trend_features', default_value=True):
                try:
                    trend_adx_period = _get_cfg('trend_adx_period', default_value=19)
                    trend_adx_threshold = _get_cfg('trend_adx_threshold', default_value=29)

                    # 计算ADX相关特征（使用pandas_ta）
                    if len(H) >= trend_adx_period and len(L) >= trend_adx_period and len(C) >= trend_adx_period:
                        try:
                            import pandas_ta as pta
                            adx_df = pta.adx(H, L, C, length=trend_adx_period)
                            if adx_df is not None and not adx_df.empty:
                                # 提取ADX相关列
                                adx_cols = [col for col in adx_df.columns if 'ADX' in col.upper()]
                                pdi_cols = [col for col in adx_df.columns if 'DMP' in col.upper() or 'PDI' in col.upper()]
                                mdi_cols = [col for col in adx_df.columns if 'DMN' in col.upper() or 'MDI' in col.upper()]

                                # 设置ADX值
                                if adx_cols:
                                    df_out['adx_value'] = adx_df[adx_cols[0]].fillna(0)
                                else:
                                    df_out['adx_value'] = 0.0

                                # 设置PDI值
                                if pdi_cols:
                                    df_out['adx_pdi'] = adx_df[pdi_cols[0]].fillna(0)
                                else:
                                    df_out['adx_pdi'] = 0.0

                                # 设置MDI值
                                if mdi_cols:
                                    df_out['adx_mdi'] = adx_df[mdi_cols[0]].fillna(0)
                                else:
                                    df_out['adx_mdi'] = 0.0

                                # 计算ADX信号（基于阈值）
                                df_out['trend_adx_signal'] = (df_out['adx_value'] > trend_adx_threshold).astype(int)
                            else:
                                # ADX计算失败，设置默认值
                                df_out['trend_adx_signal'] = 0.0
                                df_out['adx_value'] = 0.0
                                df_out['adx_pdi'] = 0.0
                                df_out['adx_mdi'] = 0.0
                        except ImportError:
                            logger.warning("_add_trend_features: (%s) pandas_ta未安装，无法计算ADX", interval_str)
                            df_out['trend_adx_signal'] = 0.0
                            df_out['adx_value'] = 0.0
                            df_out['adx_pdi'] = 0.0
                            df_out['adx_mdi'] = 0.0
                    else:
                        # 数据不足，设置默认值
                        df_out['trend_adx_signal'] = 0.0
                        df_out['adx_value'] = 0.0
                        df_out['adx_pdi'] = 0.0
                        df_out['adx_mdi'] = 0.0
                except Exception as e_adx:
                    logger.warning("_add_trend_features: (%s) 计算ADX趋势特征时出错: %s", interval_str, e_adx)
                    df_out['trend_adx_signal'] = 0.0
                    df_out['adx_value'] = 0.0
                    df_out['adx_pdi'] = 0.0
                    df_out['adx_mdi'] = 0.0
            else:
                df_out['trend_adx_signal'] = 0.0
                df_out['adx_value'] = 0.0
                df_out['adx_pdi'] = 0.0
                df_out['adx_mdi'] = 0.0

            # 添加EMA交叉趋势特征
            if _get_cfg('enable_ema_trend_features', default_value=True):
                try:
                    trend_ema_short_period = _get_cfg('trend_ema_short_period', default_value=21)
                    trend_ema_long_period = _get_cfg('trend_ema_long_period', default_value=53)

                    # 计算EMA交叉信号
                    if len(C) >= max(trend_ema_short_period, trend_ema_long_period):
                        try:
                            import pandas_ta as pta
                            ema_short = pta.ema(C, length=trend_ema_short_period)
                            ema_long = pta.ema(C, length=trend_ema_long_period)

                            if ema_short is not None and ema_long is not None:
                                # EMA交叉信号：短期EMA > 长期EMA为1，否则为0
                                df_out['trend_ema_signal'] = (ema_short > ema_long).astype(int).fillna(0)
                                # 保存EMA值用于调试
                                df_out['ema_short'] = ema_short.fillna(0)
                                df_out['ema_long'] = ema_long.fillna(0)
                            else:
                                df_out['trend_ema_signal'] = 0.0
                                df_out['ema_short'] = 0.0
                                df_out['ema_long'] = 0.0
                        except ImportError:
                            logger.warning("_add_trend_features: (%s) pandas_ta未安装，无法计算EMA", interval_str)
                            df_out['trend_ema_signal'] = 0.0
                            df_out['ema_short'] = 0.0
                            df_out['ema_long'] = 0.0
                    else:
                        df_out['trend_ema_signal'] = 0.0
                        df_out['ema_short'] = 0.0
                        df_out['ema_long'] = 0.0
                except Exception as e_ema:
                    logger.warning("_add_trend_features: (%s) 计算EMA趋势特征时出错: %s", interval_str, e_ema)
                    df_out['trend_ema_signal'] = 0.0
                    df_out['ema_short'] = 0.0
                    df_out['ema_long'] = 0.0
            else:
                df_out['trend_ema_signal'] = 0.0
                df_out['ema_short'] = 0.0
                df_out['ema_long'] = 0.0
        else:
            # 设置默认趋势特征
            df_out['trend_slope_period_1'] = 0.0
            df_out['trend_slope_period_2'] = 0.0
            df_out['trend_adx_signal'] = 0.0
            df_out['trend_ema_signal'] = 0.0
            df_out['adx_value'] = 0.0
            df_out['adx_pdi'] = 0.0
            df_out['adx_mdi'] = 0.0
            df_out['ema_short'] = 0.0
            df_out['ema_long'] = 0.0
    except (ValueError, TypeError) as e_trend_sec:
        logger.error("_add_trend_features: (%s) 趋势特征部分出错: %s", interval_str, e_trend_sec)
    except Exception as e_trend_sec_unexpected:
        logger.error("_add_trend_features: (%s) 趋势特征部分发生意外错误: %s", interval_str, e_trend_sec_unexpected)

def _add_interaction_features(df_out, cfg, C, H, L, O, V_feat, interval_str):
    """
    添加交互特征（特征组合的力量）- 优化版本

    增强功能：
    1. 改进数学稳健性和异常值处理
    2. 添加市场微观结构交互特征
    3. 引入多层次特征组合
    4. 优化计算效率和内存使用
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    def _safe_divide(numerator, denominator, default_value=0.0, min_denominator=1e-10):
        """安全除法，避免除零和异常值"""
        try:
            # 处理分母为零或极小值的情况
            safe_denominator = np.where(np.abs(denominator) < min_denominator, min_denominator, denominator)
            result = numerator / safe_denominator
            # 处理无穷大和NaN
            result = np.where(np.isfinite(result), result, default_value)
            # 异常值截断（99.5%分位数）
            if len(result) > 10:  # 只有足够数据时才进行分位数截断
                upper_bound = np.percentile(result[np.isfinite(result)], 99.5)
                lower_bound = np.percentile(result[np.isfinite(result)], 0.5)
                result = np.clip(result, lower_bound, upper_bound)
            return result
        except Exception:
            return np.full_like(numerator, default_value)

    def _safe_multiply(a, b, default_value=0.0):
        """安全乘法，处理异常值"""
        try:
            result = a * b
            result = np.where(np.isfinite(result), result, default_value)
            return result
        except Exception:
            return np.full_like(a, default_value)

    try:
        if _get_cfg('enable_interaction_features', default_value=True):
            logger.debug("_add_interaction_features: (%s) 开始计算优化版交互特征", interval_str)

            # 1. 增强版量价结合特征：成交量 × 价格变动
            try:
                price_change_cols = [col for col in df_out.columns if 'price_change_1p' in col]
                if price_change_cols and 'volume' in df_out.columns:
                    price_change_col = price_change_cols[0]
                    # 使用安全乘法
                    df_out['volume_x_price_change'] = _safe_multiply(
                        df_out['volume'], df_out[price_change_col]
                    )

                    # 🎯 新增：标准化量价特征（相对于历史波动）
                    volume_std = df_out['volume'].rolling(window=20, min_periods=5).std()
                    price_change_std = df_out[price_change_col].rolling(window=20, min_periods=5).std()
                    df_out['volume_x_price_change_normalized'] = _safe_divide(
                        df_out['volume_x_price_change'],
                        volume_std * price_change_std,
                        default_value=0.0
                    )

                    logger.debug("_add_interaction_features: (%s) 成功计算增强版量价结合特征", interval_str)
                else:
                    df_out['volume_x_price_change'] = 0.0
                    df_out['volume_x_price_change_normalized'] = 0.0
                    logger.debug("_add_interaction_features: (%s) 价格变化或成交量列不存在，使用默认值", interval_str)
            except Exception as e_volume_price:
                logger.warning("_add_interaction_features: (%s) 计算量价结合特征时出错: %s", interval_str, e_volume_price)
                df_out['volume_x_price_change'] = 0.0
                df_out['volume_x_price_change_normalized'] = 0.0

            # 2. 增强版波动率与趋势结合特征：ATR × ADX
            try:
                # 查找ATR和ADX列
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                adx_cols = [col for col in df_out.columns if 'adx_value' in col]

                if atr_cols and adx_cols:
                    atr_col = atr_cols[0]  # 使用第一个找到的ATR列
                    adx_col = adx_cols[0]  # 使用第一个找到的ADX列

                    # 基础ATR×ADX特征
                    df_out['atr_x_adx'] = _safe_multiply(df_out[atr_col], df_out[adx_col])

                    # 🎯 新增：相对波动趋势强度（ATR相对于历史均值 × ADX）
                    atr_ma = df_out[atr_col].rolling(window=20, min_periods=5).mean()
                    atr_relative = _safe_divide(df_out[atr_col], atr_ma, default_value=1.0)
                    df_out['atr_relative_x_adx'] = _safe_multiply(atr_relative, df_out[adx_col])

                    # 🎯 新增：波动率趋势一致性指标
                    atr_trend = df_out[atr_col].diff().rolling(window=5).mean()  # ATR趋势
                    adx_trend = df_out[adx_col].diff().rolling(window=5).mean()  # ADX趋势
                    # 当ATR和ADX同向变化时，市场状态更明确
                    df_out['atr_adx_trend_consistency'] = np.where(
                        atr_trend * adx_trend > 0, 1.0, -1.0
                    )

                    logger.debug("_add_interaction_features: (%s) 成功计算增强版ATR×ADX特征", interval_str)
                else:
                    df_out['atr_x_adx'] = 0.0
                    df_out['atr_relative_x_adx'] = 0.0
                    df_out['atr_adx_trend_consistency'] = 0.0
                    logger.debug("_add_interaction_features: (%s) ATR或ADX列不存在，使用默认值", interval_str)
            except Exception as e_atr_adx:
                logger.warning("_add_interaction_features: (%s) 计算ATR×ADX特征时出错: %s", interval_str, e_atr_adx)
                df_out['atr_x_adx'] = 0.0
                df_out['atr_relative_x_adx'] = 0.0
                df_out['atr_adx_trend_consistency'] = 0.0

            # 3. 增强版K线实体与波动率结合特征：实体大小 / ATR
            try:
                # 查找实体大小和ATR列
                body_size_cols = [col for col in df_out.columns if 'body_size' in col]
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]

                if body_size_cols and atr_cols:
                    body_size_col = body_size_cols[0]
                    atr_col = atr_cols[0]

                    # 使用安全除法
                    df_out['body_over_atr'] = _safe_divide(
                        df_out[body_size_col], df_out[atr_col], default_value=0.0
                    )

                    # 🎯 新增：实体强度相对排名（过去20期的百分位数）
                    df_out['body_over_atr_percentile'] = (
                        df_out['body_over_atr'].rolling(window=20, min_periods=5)
                        .rank(pct=True)
                    )

                    # 🎯 新增：实体与影线的相对强度
                    upper_shadow_cols = [col for col in df_out.columns if 'upper_shadow' in col]
                    lower_shadow_cols = [col for col in df_out.columns if 'lower_shadow' in col]

                    if upper_shadow_cols and lower_shadow_cols:
                        upper_shadow_col = upper_shadow_cols[0]
                        lower_shadow_col = lower_shadow_cols[0]
                        total_shadow = df_out[upper_shadow_col] + df_out[lower_shadow_col]
                        df_out['body_vs_shadow_strength'] = _safe_divide(
                            df_out[body_size_col], total_shadow, default_value=1.0
                        )
                    else:
                        df_out['body_vs_shadow_strength'] = 1.0

                    logger.debug("_add_interaction_features: (%s) 成功计算增强版实体/ATR特征", interval_str)
                else:
                    df_out['body_over_atr'] = 0.0
                    df_out['body_over_atr_percentile'] = 0.5
                    df_out['body_vs_shadow_strength'] = 1.0
                    logger.debug("_add_interaction_features: (%s) 实体大小或ATR列不存在，使用默认值", interval_str)
            except Exception as e_body_atr:
                logger.warning("_add_interaction_features: (%s) 计算实体/ATR特征时出错: %s", interval_str, e_body_atr)
                df_out['body_over_atr'] = 0.0
                df_out['body_over_atr_percentile'] = 0.5
                df_out['body_vs_shadow_strength'] = 1.0

            # 4. 额外的交互特征：RSI × 成交量比率
            try:
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                volume_ratio_cols = [col for col in df_out.columns if 'volume_vs_avg' in col]

                if rsi_cols and volume_ratio_cols:
                    rsi_col = rsi_cols[0]
                    volume_ratio_col = volume_ratio_cols[0]
                    df_out['rsi_x_volume_ratio'] = df_out[rsi_col] * df_out[volume_ratio_col]
                    logger.debug("_add_interaction_features: (%s) 成功计算RSI×成交量比率特征", interval_str)
                else:
                    df_out['rsi_x_volume_ratio'] = 0.0
            except Exception as e_rsi_volume:
                logger.warning("_add_interaction_features: (%s) 计算RSI×成交量比率特征时出错: %s", interval_str, e_rsi_volume)
                df_out['rsi_x_volume_ratio'] = 0.0

            # 5. MACD × 成交量特征
            try:
                macd_cols = [col for col in df_out.columns if col == 'MACD']
                if macd_cols and 'volume' in df_out.columns:
                    df_out['macd_x_volume'] = df_out['MACD'] * df_out['volume']
                    logger.debug("_add_interaction_features: (%s) 成功计算MACD×成交量特征", interval_str)
                else:
                    df_out['macd_x_volume'] = 0.0
            except Exception as e_macd_volume:
                logger.warning("_add_interaction_features: (%s) 计算MACD×成交量特征时出错: %s", interval_str, e_macd_volume)
                df_out['macd_x_volume'] = 0.0

            # 🎯 6. 新增：高级交互特征 - 挖掘更深的"阿尔法"
            try:
                # 6.1 RSI × 价格动量交互
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                price_change_cols = [col for col in df_out.columns if 'price_change_1p' in col]
                if rsi_cols and price_change_cols:
                    rsi_col = rsi_cols[0]
                    price_change_col = price_change_cols[0]
                    df_out['rsi_x_momentum'] = df_out[rsi_col] * df_out[price_change_col]
                    logger.debug("_add_interaction_features: (%s) 成功计算RSI×价格动量特征", interval_str)
                else:
                    df_out['rsi_x_momentum'] = 0.0
            except Exception as e_rsi_momentum:
                logger.warning("_add_interaction_features: (%s) 计算RSI×价格动量特征时出错: %s", interval_str, e_rsi_momentum)
                df_out['rsi_x_momentum'] = 0.0

            try:
                # 6.2 布林带宽度 × 成交量异常
                bb_upper_cols = [col for col in df_out.columns if 'bb_upper' in col.lower()]
                bb_lower_cols = [col for col in df_out.columns if 'bb_lower' in col.lower()]
                if bb_upper_cols and bb_lower_cols and 'volume' in df_out.columns:
                    bb_width = df_out[bb_upper_cols[0]] - df_out[bb_lower_cols[0]]
                    volume_ma = df_out['volume'].rolling(window=20, min_periods=1).mean()
                    volume_ratio = df_out['volume'] / volume_ma
                    df_out['bb_width_x_volume_anomaly'] = bb_width * volume_ratio
                    logger.debug("_add_interaction_features: (%s) 成功计算布林带宽度×成交量异常特征", interval_str)
                else:
                    df_out['bb_width_x_volume_anomaly'] = 0.0
            except Exception as e_bb_volume:
                logger.warning("_add_interaction_features: (%s) 计算布林带宽度×成交量异常特征时出错: %s", interval_str, e_bb_volume)
                df_out['bb_width_x_volume_anomaly'] = 0.0

            try:
                # 6.3 威廉指标 × ATR（超买超卖与波动率结合）
                willr_cols = [col for col in df_out.columns if 'willr' in col.lower()]
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                if willr_cols and atr_cols:
                    df_out['willr_x_atr'] = df_out[willr_cols[0]] * df_out[atr_cols[0]]
                    logger.debug("_add_interaction_features: (%s) 成功计算威廉指标×ATR特征", interval_str)
                else:
                    df_out['willr_x_atr'] = 0.0
            except Exception as e_willr_atr:
                logger.warning("_add_interaction_features: (%s) 计算威廉指标×ATR特征时出错: %s", interval_str, e_willr_atr)
                df_out['willr_x_atr'] = 0.0

            try:
                # 6.4 MACD信号线交叉强度 × 成交量确认
                macd_cols = [col for col in df_out.columns if col == 'MACD']
                macd_signal_cols = [col for col in df_out.columns if 'macd_signal' in col.lower()]
                if macd_cols and macd_signal_cols and 'volume' in df_out.columns:
                    macd_diff = df_out[macd_cols[0]] - df_out[macd_signal_cols[0]]
                    volume_ma = df_out['volume'].rolling(window=10, min_periods=1).mean()
                    volume_strength = df_out['volume'] / volume_ma
                    df_out['macd_cross_x_volume_confirm'] = macd_diff * volume_strength
                    logger.debug("_add_interaction_features: (%s) 成功计算MACD交叉×成交量确认特征", interval_str)
                else:
                    df_out['macd_cross_x_volume_confirm'] = 0.0
            except Exception as e_macd_cross:
                logger.warning("_add_interaction_features: (%s) 计算MACD交叉×成交量确认特征时出错: %s", interval_str, e_macd_cross)
                df_out['macd_cross_x_volume_confirm'] = 0.0

            try:
                # 6.5 价格位置 × 成交量分布（价格在区间内的位置与成交量的关系）
                if all(col in df_out.columns for col in ['high', 'low', 'close', 'volume']):
                    # 计算价格在高低点区间内的相对位置
                    high_low_range = df_out['high'] - df_out['low']
                    price_position = (df_out['close'] - df_out['low']) / (high_low_range + 1e-8)  # 避免除零

                    # 计算成交量相对强度
                    volume_ma = df_out['volume'].rolling(window=14, min_periods=1).mean()
                    volume_relative = df_out['volume'] / (volume_ma + 1e-8)

                    df_out['price_position_x_volume_strength'] = price_position * volume_relative
                    logger.debug("_add_interaction_features: (%s) 成功计算价格位置×成交量强度特征", interval_str)
                else:
                    df_out['price_position_x_volume_strength'] = 0.0
            except Exception as e_price_position:
                logger.warning("_add_interaction_features: (%s) 计算价格位置×成交量强度特征时出错: %s", interval_str, e_price_position)
                df_out['price_position_x_volume_strength'] = 0.0

            # 🚀 7. 全新市场微观结构交互特征 - 挖掘深层阿尔法
            try:
                # 7.1 买卖压力不平衡 × 价格位置
                if all(col in df_out.columns for col in ['high', 'low', 'close', 'volume']):
                    # 计算买卖压力代理指标
                    price_range = df_out['high'] - df_out['low']
                    close_position = _safe_divide(
                        df_out['close'] - df_out['low'], price_range, default_value=0.5
                    )

                    # 成交量分布不平衡（基于价格位置推断）
                    volume_imbalance = np.where(close_position > 0.5, 1, -1) * df_out['volume']
                    df_out['volume_imbalance_x_price_pos'] = _safe_multiply(
                        volume_imbalance, close_position
                    )

                    # 7.2 订单流强度 × 波动率
                    volume_velocity = df_out['volume'].diff().rolling(window=3).mean()
                    atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                    if atr_cols:
                        df_out['order_flow_x_volatility'] = _safe_multiply(
                            volume_velocity, df_out[atr_cols[0]]
                        )
                    else:
                        df_out['order_flow_x_volatility'] = 0.0

                    logger.debug("_add_interaction_features: (%s) 成功计算市场微观结构特征", interval_str)
                else:
                    df_out['volume_imbalance_x_price_pos'] = 0.0
                    df_out['order_flow_x_volatility'] = 0.0
            except Exception as e_microstructure:
                logger.warning("_add_interaction_features: (%s) 计算市场微观结构特征时出错: %s", interval_str, e_microstructure)
                df_out['volume_imbalance_x_price_pos'] = 0.0
                df_out['order_flow_x_volatility'] = 0.0

            # 🎯 8. 多层次特征组合 - 三元交互特征
            try:
                # 8.1 RSI × 成交量 × 波动率三元组合
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]

                if rsi_cols and atr_cols and 'volume' in df_out.columns:
                    rsi_normalized = (df_out[rsi_cols[0]] - 50) / 50  # 标准化到[-1,1]
                    volume_ma = df_out['volume'].rolling(window=20, min_periods=5).mean()
                    volume_normalized = _safe_divide(df_out['volume'], volume_ma, default_value=1.0)
                    atr_ma = df_out[atr_cols[0]].rolling(window=20, min_periods=5).mean()
                    atr_normalized = _safe_divide(df_out[atr_cols[0]], atr_ma, default_value=1.0)

                    df_out['rsi_volume_atr_combo'] = (
                        rsi_normalized * volume_normalized * atr_normalized
                    )

                    # 8.2 趋势一致性指标（价格趋势 × RSI趋势 × 成交量趋势）
                    price_trend = df_out['close'].diff().rolling(window=5).mean()
                    rsi_trend = df_out[rsi_cols[0]].diff().rolling(window=5).mean()
                    volume_trend = df_out['volume'].diff().rolling(window=5).mean()

                    # 计算趋势一致性（同向为正，反向为负）
                    trend_consistency = np.sign(price_trend) * np.sign(rsi_trend) * np.sign(volume_trend)
                    df_out['trend_consistency_score'] = trend_consistency

                    logger.debug("_add_interaction_features: (%s) 成功计算多层次特征组合", interval_str)
                else:
                    df_out['rsi_volume_atr_combo'] = 0.0
                    df_out['trend_consistency_score'] = 0.0
            except Exception as e_multilevel:
                logger.warning("_add_interaction_features: (%s) 计算多层次特征组合时出错: %s", interval_str, e_multilevel)
                df_out['rsi_volume_atr_combo'] = 0.0
                df_out['trend_consistency_score'] = 0.0

            logger.debug("_add_interaction_features: (%s) 优化版交互特征计算完成（包含微观结构特征）", interval_str)
        else:
            # 如果禁用交互特征，设置所有默认值
            df_out['volume_x_price_change'] = 0.0
            df_out['volume_x_price_change_normalized'] = 0.0
            df_out['atr_x_adx'] = 0.0
            df_out['atr_relative_x_adx'] = 0.0
            df_out['atr_adx_trend_consistency'] = 0.0
            df_out['body_over_atr'] = 0.0
            df_out['body_over_atr_percentile'] = 0.5
            df_out['body_vs_shadow_strength'] = 1.0
            df_out['rsi_x_volume_ratio'] = 0.0
            df_out['macd_x_volume'] = 0.0
            # 原有高级交互特征的默认值
            df_out['rsi_x_momentum'] = 0.0
            df_out['bb_width_x_volume_anomaly'] = 0.0
            df_out['willr_x_atr'] = 0.0
            df_out['macd_cross_x_volume_confirm'] = 0.0
            df_out['price_position_x_volume_strength'] = 0.0
            # 新增微观结构特征的默认值
            df_out['volume_imbalance_x_price_pos'] = 0.0
            df_out['order_flow_x_volatility'] = 0.0
            df_out['rsi_volume_atr_combo'] = 0.0
            df_out['trend_consistency_score'] = 0.0

    except (ValueError, TypeError) as e_interaction_sec:
        logger.error("_add_interaction_features: (%s) 交互特征部分出错: %s", interval_str, e_interaction_sec)
    except Exception as e_interaction_sec_unexpected:
        logger.error("_add_interaction_features: (%s) 交互特征部分发生意外错误: %s", interval_str, e_interaction_sec_unexpected)

def _add_higher_order_features(df_out, cfg, interval_str):
    """
    添加高阶特征（从"速度"到"加速度"）- 优化版本

    增强功能：
    1. 数学稳定性优化（平滑化处理）
    2. 自适应窗口大小
    3. 异常值检测和处理
    4. 新增复合高阶特征
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    def _smooth_derivative(series, window=3, method='ema'):
        """计算平滑化的导数"""
        try:
            if method == 'ema':
                # 使用EMA平滑
                alpha = 2.0 / (window + 1)
                return series.diff().ewm(alpha=alpha, adjust=False).mean()
            else:
                # 使用简单移动平均平滑
                return series.diff().rolling(window=window, min_periods=1).mean()
        except Exception:
            return series.diff().fillna(0)

    def _adaptive_window(series, base_window=20, volatility_factor=0.5):
        """根据数据波动性自适应调整窗口大小"""
        try:
            # 计算滚动标准差作为波动性指标
            volatility = series.rolling(window=base_window, min_periods=5).std()
            volatility_normalized = volatility / volatility.mean()

            # 高波动时使用更小的窗口，低波动时使用更大的窗口
            adaptive_window = np.clip(
                base_window * (1 - volatility_factor * (volatility_normalized - 1)),
                base_window * 0.5,  # 最小窗口
                base_window * 1.5   # 最大窗口
            ).astype(int)

            return adaptive_window
        except Exception:
            return np.full(len(series), base_window)

    try:
        if _get_cfg('enable_higher_order_features', default_value=True):
            logger.debug("_add_higher_order_features: (%s) 开始计算优化版高阶特征", interval_str)

            # 1. 增强版RSI的变化率（平滑化一阶导数）
            try:
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                if rsi_cols:
                    rsi_col = rsi_cols[0]  # 使用第一个找到的RSI列

                    # 基础RSI变化率
                    df_out['rsi_change_1p'] = _smooth_derivative(df_out[rsi_col], window=3)

                    # 🎯 新增：RSI动量强度（变化率的绝对值）
                    df_out['rsi_momentum_strength'] = np.abs(df_out['rsi_change_1p'])

                    # 🎯 新增：RSI变化方向一致性（连续同向变化的程度）
                    rsi_direction = np.sign(df_out['rsi_change_1p'])
                    rsi_consistency = rsi_direction.rolling(window=5).apply(
                        lambda x: np.abs(x.sum()) / len(x), raw=True
                    )
                    df_out['rsi_direction_consistency'] = rsi_consistency.fillna(0)

                    # 🎯 新增：RSI超买超卖动量（在极值区域的变化率）
                    rsi_extreme_momentum = np.where(
                        (df_out[rsi_col] > 70) | (df_out[rsi_col] < 30),
                        df_out['rsi_change_1p'],
                        0
                    )
                    df_out['rsi_extreme_momentum'] = rsi_extreme_momentum

                    logger.debug("_add_higher_order_features: (%s) 成功计算增强版RSI变化率特征", interval_str)
                else:
                    df_out['rsi_change_1p'] = 0.0
                    df_out['rsi_momentum_strength'] = 0.0
                    df_out['rsi_direction_consistency'] = 0.0
                    df_out['rsi_extreme_momentum'] = 0.0
                    logger.debug("_add_higher_order_features: (%s) RSI列不存在，使用默认值", interval_str)
            except Exception as e_rsi_change:
                logger.warning("_add_higher_order_features: (%s) 计算RSI变化率时出错: %s", interval_str, e_rsi_change)
                df_out['rsi_change_1p'] = 0.0
                df_out['rsi_momentum_strength'] = 0.0
                df_out['rsi_direction_consistency'] = 0.0
                df_out['rsi_extreme_momentum'] = 0.0

            # 2. 增强版MACD柱状图的加速度（平滑化二阶导数）
            try:
                macd_hist_cols = [col for col in df_out.columns if 'MACD' in col and ('histogram' in col.lower() or 'hist' in col.lower())]
                if not macd_hist_cols:
                    # 如果没有找到histogram列，查找MACDh列
                    macd_hist_cols = [col for col in df_out.columns if col == 'MACDh']

                if macd_hist_cols:
                    macd_hist_col = macd_hist_cols[0]

                    # 计算平滑化的一阶导数（速度）
                    macd_hist_velocity = _smooth_derivative(df_out[macd_hist_col], window=3)
                    # 计算平滑化的二阶导数（加速度）
                    df_out['macd_hist_accel'] = _smooth_derivative(macd_hist_velocity, window=3)

                    # 🎯 新增：MACD柱状图动量转折点检测
                    # 当加速度从正转负或从负转正时，可能是动量转折点
                    accel_sign_change = (
                        np.sign(df_out['macd_hist_accel']) !=
                        np.sign(df_out['macd_hist_accel'].shift(1))
                    ).astype(int)
                    df_out['macd_momentum_turning_point'] = accel_sign_change

                    # 🎯 新增：MACD柱状图强度指标（绝对值的移动平均）
                    macd_strength = np.abs(df_out[macd_hist_col]).rolling(window=10, min_periods=3).mean()
                    df_out['macd_histogram_strength'] = macd_strength.fillna(0)

                    logger.debug("_add_higher_order_features: (%s) 成功计算增强版MACD柱状图加速度", interval_str)
                else:
                    df_out['macd_hist_accel'] = 0.0
                    df_out['macd_momentum_turning_point'] = 0
                    df_out['macd_histogram_strength'] = 0.0
                    logger.debug("_add_higher_order_features: (%s) MACD柱状图列不存在，使用默认值", interval_str)
            except Exception as e_macd_accel:
                logger.warning("_add_higher_order_features: (%s) 计算MACD柱状图加速度时出错: %s", interval_str, e_macd_accel)
                df_out['macd_hist_accel'] = 0.0
                df_out['macd_momentum_turning_point'] = 0
                df_out['macd_histogram_strength'] = 0.0

            # 3. 价格变化的加速度
            try:
                price_change_cols = [col for col in df_out.columns if 'price_change_1p' in col]
                if price_change_cols:
                    price_change_col = price_change_cols[0]
                    # 计算价格变化的变化率（加速度）
                    df_out['price_change_accel'] = df_out[price_change_col].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算价格变化加速度", interval_str)
                else:
                    df_out['price_change_accel'] = 0.0
            except Exception as e_price_accel:
                logger.warning("_add_higher_order_features: (%s) 计算价格变化加速度时出错: %s", interval_str, e_price_accel)
                df_out['price_change_accel'] = 0.0

            # 4. 成交量变化率
            try:
                if 'volume' in df_out.columns:
                    df_out['volume_change_1p'] = df_out['volume'].pct_change()
                    # 成交量变化的加速度
                    df_out['volume_change_accel'] = df_out['volume_change_1p'].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算成交量变化率和加速度", interval_str)
                else:
                    df_out['volume_change_1p'] = 0.0
                    df_out['volume_change_accel'] = 0.0
            except Exception as e_volume_change:
                logger.warning("_add_higher_order_features: (%s) 计算成交量变化率时出错: %s", interval_str, e_volume_change)
                df_out['volume_change_1p'] = 0.0
                df_out['volume_change_accel'] = 0.0

            # 5. ATR的变化率（波动率趋势）
            try:
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]
                if atr_cols:
                    atr_col = atr_cols[0]
                    df_out['atr_change_1p'] = df_out[atr_col].pct_change()
                    logger.debug("_add_higher_order_features: (%s) 成功计算ATR变化率", interval_str)
                else:
                    df_out['atr_change_1p'] = 0.0
            except Exception as e_atr_change:
                logger.warning("_add_higher_order_features: (%s) 计算ATR变化率时出错: %s", interval_str, e_atr_change)
                df_out['atr_change_1p'] = 0.0

            # 🎯 6. 新增：高级高阶特征 - 捕捉市场动态的"速度"和"加速度"
            try:
                # 6.1 RSI速度（RSI变化率，别名为rsi_velocity）
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                if rsi_cols:
                    rsi_col = rsi_cols[0]
                    df_out['rsi_velocity'] = df_out[rsi_col].diff()  # RSI的一阶导数
                    df_out['rsi_acceleration'] = df_out['rsi_velocity'].diff()  # RSI的二阶导数
                    logger.debug("_add_higher_order_features: (%s) 成功计算RSI速度和加速度", interval_str)
                else:
                    df_out['rsi_velocity'] = 0.0
                    df_out['rsi_acceleration'] = 0.0
            except Exception as e_rsi_velocity:
                logger.warning("_add_higher_order_features: (%s) 计算RSI速度特征时出错: %s", interval_str, e_rsi_velocity)
                df_out['rsi_velocity'] = 0.0
                df_out['rsi_acceleration'] = 0.0

            try:
                # 6.2 MACD柱状图加速度（动量的动量）
                macd_hist_cols = [col for col in df_out.columns if 'macd_hist' in col.lower()]
                if macd_hist_cols:
                    macd_hist_col = macd_hist_cols[0]
                    df_out['macd_hist_velocity'] = df_out[macd_hist_col].diff()
                    df_out['macd_hist_acceleration'] = df_out['macd_hist_velocity'].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算MACD柱状图速度和加速度", interval_str)
                else:
                    df_out['macd_hist_velocity'] = 0.0
                    df_out['macd_hist_acceleration'] = 0.0
            except Exception as e_macd_accel:
                logger.warning("_add_higher_order_features: (%s) 计算MACD柱状图加速度时出错: %s", interval_str, e_macd_accel)
                df_out['macd_hist_velocity'] = 0.0
                df_out['macd_hist_acceleration'] = 0.0

            try:
                # 6.3 布林带位置变化率（价格在布林带中位置的变化速度）
                bb_upper_cols = [col for col in df_out.columns if 'bb_upper' in col.lower()]
                bb_lower_cols = [col for col in df_out.columns if 'bb_lower' in col.lower()]
                if bb_upper_cols and bb_lower_cols and 'close' in df_out.columns:
                    bb_position = (df_out['close'] - df_out[bb_lower_cols[0]]) / (df_out[bb_upper_cols[0]] - df_out[bb_lower_cols[0]] + 1e-8)
                    df_out['bb_position_velocity'] = bb_position.diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算布林带位置变化率", interval_str)
                else:
                    df_out['bb_position_velocity'] = 0.0
            except Exception as e_bb_velocity:
                logger.warning("_add_higher_order_features: (%s) 计算布林带位置变化率时出错: %s", interval_str, e_bb_velocity)
                df_out['bb_position_velocity'] = 0.0

            try:
                # 6.4 ADX变化率（趋势强度的变化）
                adx_cols = [col for col in df_out.columns if 'ADX' in col.upper()]
                if adx_cols:
                    adx_col = adx_cols[0]
                    df_out['adx_velocity'] = df_out[adx_col].diff()
                    df_out['adx_acceleration'] = df_out['adx_velocity'].diff()
                    logger.debug("_add_higher_order_features: (%s) 成功计算ADX速度和加速度", interval_str)
                else:
                    df_out['adx_velocity'] = 0.0
                    df_out['adx_acceleration'] = 0.0
            except Exception as e_adx_velocity:
                logger.warning("_add_higher_order_features: (%s) 计算ADX速度特征时出错: %s", interval_str, e_adx_velocity)
                df_out['adx_velocity'] = 0.0
                df_out['adx_acceleration'] = 0.0

            try:
                # 6.5 成交量动量的动量（成交量变化的加速度）
                if 'volume' in df_out.columns:
                    volume_momentum = df_out['volume'].pct_change()
                    df_out['volume_momentum_acceleration'] = volume_momentum.diff()

                    # 成交量相对强度指数（类似RSI但用于成交量）
                    volume_changes = df_out['volume'].diff()
                    volume_gains = volume_changes.where(volume_changes > 0, 0)
                    volume_losses = (-volume_changes).where(volume_changes < 0, 0)

                    avg_volume_gain = volume_gains.rolling(window=14, min_periods=1).mean()
                    avg_volume_loss = volume_losses.rolling(window=14, min_periods=1).mean()

                    volume_rs = avg_volume_gain / (avg_volume_loss + 1e-8)
                    df_out['volume_rsi'] = 100 - (100 / (1 + volume_rs))

                    logger.debug("_add_higher_order_features: (%s) 成功计算成交量高阶特征", interval_str)
                else:
                    df_out['volume_momentum_acceleration'] = 0.0
                    df_out['volume_rsi'] = 50.0  # 中性值
            except Exception as e_volume_advanced:
                logger.warning("_add_higher_order_features: (%s) 计算成交量高阶特征时出错: %s", interval_str, e_volume_advanced)
                df_out['volume_momentum_acceleration'] = 0.0
                df_out['volume_rsi'] = 50.0

            # 🚀 7. 全新复合高阶特征 - 捕捉市场动态的深层模式
            try:
                # 7.1 多指标协同加速度（RSI、MACD、价格的综合加速度）
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                macd_cols = [col for col in df_out.columns if col == 'MACD']
                price_change_cols = [col for col in df_out.columns if 'price_change_1p' in col]

                if rsi_cols and macd_cols and price_change_cols:
                    # 计算各指标的标准化加速度
                    rsi_accel = _smooth_derivative(df_out[rsi_cols[0]], window=3)
                    macd_accel = _smooth_derivative(df_out[macd_cols[0]], window=3)
                    price_accel = _smooth_derivative(df_out[price_change_cols[0]], window=3)

                    # 标准化到相同尺度
                    rsi_accel_norm = (rsi_accel - rsi_accel.mean()) / (rsi_accel.std() + 1e-8)
                    macd_accel_norm = (macd_accel - macd_accel.mean()) / (macd_accel.std() + 1e-8)
                    price_accel_norm = (price_accel - price_accel.mean()) / (price_accel.std() + 1e-8)

                    # 综合加速度指标
                    df_out['multi_indicator_acceleration'] = (
                        rsi_accel_norm + macd_accel_norm + price_accel_norm
                    ) / 3

                    # 7.2 动量分歧指标（价格动量与技术指标动量的分歧）
                    price_momentum = df_out[price_change_cols[0]].rolling(window=5).mean()
                    rsi_momentum = (df_out[rsi_cols[0]] - 50) / 50  # 标准化RSI动量

                    momentum_divergence = np.abs(
                        np.sign(price_momentum) - np.sign(rsi_momentum)
                    )
                    df_out['momentum_divergence_signal'] = momentum_divergence

                    logger.debug("_add_higher_order_features: (%s) 成功计算复合高阶特征", interval_str)
                else:
                    df_out['multi_indicator_acceleration'] = 0.0
                    df_out['momentum_divergence_signal'] = 0.0
            except Exception as e_composite:
                logger.warning("_add_higher_order_features: (%s) 计算复合高阶特征时出错: %s", interval_str, e_composite)
                df_out['multi_indicator_acceleration'] = 0.0
                df_out['momentum_divergence_signal'] = 0.0

            # 🎯 8. 自适应高阶特征 - 根据市场状态调整计算窗口
            try:
                # 8.1 自适应RSI变化率（根据波动率调整窗口）
                rsi_cols = [col for col in df_out.columns if 'RSI' in col]
                atr_cols = [col for col in df_out.columns if 'ATR' in col.upper()]

                if rsi_cols and atr_cols:
                    # 根据ATR调整RSI变化率的计算窗口
                    atr_percentile = df_out[atr_cols[0]].rolling(window=20, min_periods=5).rank(pct=True)

                    # 高波动时使用短窗口，低波动时使用长窗口
                    adaptive_window = np.where(atr_percentile > 0.7, 3,
                                             np.where(atr_percentile < 0.3, 7, 5))

                    # 计算自适应RSI变化率（简化版本，使用固定窗口近似）
                    df_out['rsi_adaptive_velocity'] = _smooth_derivative(
                        df_out[rsi_cols[0]], window=5
                    )

                    # 8.2 市场状态感知的价格加速度
                    # 在趋势市场和震荡市场中使用不同的加速度计算方法
                    adx_cols = [col for col in df_out.columns if 'adx_value' in col]
                    if adx_cols and price_change_cols:
                        adx_strength = df_out[adx_cols[0]]
                        is_trending = adx_strength > 25  # ADX > 25表示趋势市场

                        # 趋势市场：使用较长窗口平滑
                        # 震荡市场：使用较短窗口捕捉快速变化
                        trend_accel = _smooth_derivative(df_out[price_change_cols[0]], window=7)
                        range_accel = _smooth_derivative(df_out[price_change_cols[0]], window=3)

                        df_out['market_aware_price_accel'] = np.where(
                            is_trending, trend_accel, range_accel
                        )
                    else:
                        df_out['market_aware_price_accel'] = 0.0

                    logger.debug("_add_higher_order_features: (%s) 成功计算自适应高阶特征", interval_str)
                else:
                    df_out['rsi_adaptive_velocity'] = 0.0
                    df_out['market_aware_price_accel'] = 0.0
            except Exception as e_adaptive:
                logger.warning("_add_higher_order_features: (%s) 计算自适应高阶特征时出错: %s", interval_str, e_adaptive)
                df_out['rsi_adaptive_velocity'] = 0.0
                df_out['market_aware_price_accel'] = 0.0

            logger.debug("_add_higher_order_features: (%s) 优化版高阶特征计算完成（包含复合和自适应特征）", interval_str)
        else:
            # 如果禁用高阶特征，设置所有默认值
            df_out['rsi_change_1p'] = 0.0
            df_out['rsi_momentum_strength'] = 0.0
            df_out['rsi_direction_consistency'] = 0.0
            df_out['rsi_extreme_momentum'] = 0.0
            df_out['macd_hist_accel'] = 0.0
            df_out['macd_momentum_turning_point'] = 0
            df_out['macd_histogram_strength'] = 0.0
            df_out['price_change_accel'] = 0.0
            df_out['volume_change_1p'] = 0.0
            df_out['volume_change_accel'] = 0.0
            df_out['atr_change_1p'] = 0.0
            # 原有高级高阶特征的默认值
            df_out['rsi_velocity'] = 0.0
            df_out['rsi_acceleration'] = 0.0
            df_out['macd_hist_velocity'] = 0.0
            df_out['macd_hist_acceleration'] = 0.0
            df_out['bb_position_velocity'] = 0.0
            df_out['adx_velocity'] = 0.0
            df_out['adx_acceleration'] = 0.0
            df_out['volume_momentum_acceleration'] = 0.0
            df_out['volume_rsi'] = 50.0
            # 新增复合和自适应特征的默认值
            df_out['multi_indicator_acceleration'] = 0.0
            df_out['momentum_divergence_signal'] = 0.0
            df_out['rsi_adaptive_velocity'] = 0.0
            df_out['market_aware_price_accel'] = 0.0

    except (ValueError, TypeError) as e_higher_order_sec:
        logger.error("_add_higher_order_features: (%s) 高阶特征部分出错: %s", interval_str, e_higher_order_sec)
    except Exception as e_higher_order_sec_unexpected:
        logger.error("_add_higher_order_features: (%s) 高阶特征部分发生意外错误: %s", interval_str, e_higher_order_sec_unexpected)

def _add_timeframe_sensitivity_features(df_out, cfg, interval_str):
    """添加时间框架敏感度特征 - 对比15分钟与4小时数据的差异"""
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        if not _get_cfg('enable_timeframe_sensitivity', default_value=False):
            return

        reference_tf = _get_cfg('tf_sensitivity_reference_timeframe', default_value='4h')
        sensitivity_features = _get_cfg('tf_sensitivity_features', default_value=['rsi', 'close_pos_in_candle', 'macd', 'volume_ratio'])

        logger.info("_add_timeframe_sensitivity_features: (%s) 开始计算时间框架敏感度特征，参考时间框架: %s",
                   interval_str, reference_tf)

        # 检查是否有MTFA特征可用
        if not isinstance(sensitivity_features, list):
            sensitivity_features = ['rsi', 'close_pos_in_candle', 'macd', 'volume_ratio']

        # 1. RSI对比特征
        if 'rsi' in sensitivity_features:
            try:
                rsi_period = _get_cfg('rsi_period', 22)
                current_rsi_col = f'RSI_{rsi_period}'
                reference_rsi_col = f'RSI_{rsi_period}_{reference_tf}'

                if current_rsi_col in df_out.columns and reference_rsi_col in df_out.columns:
                    rsi_diff = df_out[current_rsi_col] - df_out[reference_rsi_col]
                    df_out[f'rsi_{interval_str}_vs_{reference_tf}_diff'] = rsi_diff.fillna(0)

                    # 额外特征：RSI相对强度
                    rsi_ratio = df_out[current_rsi_col] / df_out[reference_rsi_col].replace(0, 1e-9)
                    df_out[f'rsi_{interval_str}_vs_{reference_tf}_ratio'] = rsi_ratio.fillna(1.0)

                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成功计算RSI对比特征", interval_str)
                else:
                    df_out[f'rsi_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                    df_out[f'rsi_{interval_str}_vs_{reference_tf}_ratio'] = 1.0
                    logger.debug("_add_timeframe_sensitivity_features: (%s) RSI列不存在，使用默认值", interval_str)
            except Exception as e_rsi:
                logger.warning("_add_timeframe_sensitivity_features: (%s) 计算RSI对比特征时出错: %s", interval_str, e_rsi)
                df_out[f'rsi_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                df_out[f'rsi_{interval_str}_vs_{reference_tf}_ratio'] = 1.0

        # 2. 收盘价在K线中位置对比特征
        if 'close_pos_in_candle' in sensitivity_features:
            try:
                current_pos_col = 'close_pos_in_candle'
                reference_pos_col = f'close_pos_in_candle_{reference_tf}'

                if current_pos_col in df_out.columns and reference_pos_col in df_out.columns:
                    pos_diff = df_out[current_pos_col] - df_out[reference_pos_col]
                    df_out[f'close_pos_{interval_str}_vs_{reference_tf}_diff'] = pos_diff.fillna(0)

                    # 额外特征：位置偏离程度（绝对值）
                    pos_deviation = np.abs(pos_diff)
                    df_out[f'close_pos_{interval_str}_vs_{reference_tf}_deviation'] = pos_deviation.fillna(0)

                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成功计算收盘价位置对比特征", interval_str)
                else:
                    df_out[f'close_pos_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                    df_out[f'close_pos_{interval_str}_vs_{reference_tf}_deviation'] = 0.0
                    logger.debug("_add_timeframe_sensitivity_features: (%s) 收盘价位置列不存在，使用默认值", interval_str)
            except Exception as e_pos:
                logger.warning("_add_timeframe_sensitivity_features: (%s) 计算收盘价位置对比特征时出错: %s", interval_str, e_pos)
                df_out[f'close_pos_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                df_out[f'close_pos_{interval_str}_vs_{reference_tf}_deviation'] = 0.0

        # 3. MACD对比特征
        if 'macd' in sensitivity_features:
            try:
                current_macd_col = 'MACD'
                reference_macd_col = f'MACD_{reference_tf}'

                if current_macd_col in df_out.columns and reference_macd_col in df_out.columns:
                    macd_diff = df_out[current_macd_col] - df_out[reference_macd_col]
                    df_out[f'macd_{interval_str}_vs_{reference_tf}_diff'] = macd_diff.fillna(0)

                    # 额外特征：MACD信号一致性
                    current_macd_signal = (df_out[current_macd_col] > 0).astype(int)
                    reference_macd_signal = (df_out[reference_macd_col] > 0).astype(int)
                    macd_signal_agreement = (current_macd_signal == reference_macd_signal).astype(int)
                    df_out[f'macd_{interval_str}_vs_{reference_tf}_signal_agreement'] = macd_signal_agreement

                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成功计算MACD对比特征", interval_str)
                else:
                    df_out[f'macd_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                    df_out[f'macd_{interval_str}_vs_{reference_tf}_signal_agreement'] = 1
                    logger.debug("_add_timeframe_sensitivity_features: (%s) MACD列不存在，使用默认值", interval_str)
            except Exception as e_macd:
                logger.warning("_add_timeframe_sensitivity_features: (%s) 计算MACD对比特征时出错: %s", interval_str, e_macd)
                df_out[f'macd_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                df_out[f'macd_{interval_str}_vs_{reference_tf}_signal_agreement'] = 1

        # 4. 成交量比率特征
        if 'volume_ratio' in sensitivity_features:
            try:
                volume_avg_period = _get_cfg('volume_avg_period', 20)
                current_vol_col = f'volume_vs_avg_{volume_avg_period}p'
                reference_vol_col = f'volume_vs_avg_{volume_avg_period}p_{reference_tf}'

                if current_vol_col in df_out.columns and reference_vol_col in df_out.columns:
                    vol_ratio_diff = df_out[current_vol_col] - df_out[reference_vol_col]
                    df_out[f'volume_ratio_{interval_str}_vs_{reference_tf}_diff'] = vol_ratio_diff.fillna(0)

                    # 额外特征：成交量活跃度对比
                    vol_activity_ratio = df_out[current_vol_col] / df_out[reference_vol_col].replace(0, 1e-9)
                    df_out[f'volume_activity_{interval_str}_vs_{reference_tf}_ratio'] = vol_activity_ratio.fillna(1.0)

                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成功计算成交量对比特征", interval_str)
                else:
                    df_out[f'volume_ratio_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                    df_out[f'volume_activity_{interval_str}_vs_{reference_tf}_ratio'] = 1.0
                    logger.debug("_add_timeframe_sensitivity_features: (%s) 成交量比率列不存在，使用默认值", interval_str)
            except Exception as e_vol:
                logger.warning("_add_timeframe_sensitivity_features: (%s) 计算成交量对比特征时出错: %s", interval_str, e_vol)
                df_out[f'volume_ratio_{interval_str}_vs_{reference_tf}_diff'] = 0.0
                df_out[f'volume_activity_{interval_str}_vs_{reference_tf}_ratio'] = 1.0

        logger.info("_add_timeframe_sensitivity_features: (%s) 时间框架敏感度特征计算完成", interval_str)

    except (ValueError, TypeError) as e_tf_sensitivity_sec:
        logger.error("_add_timeframe_sensitivity_features: (%s) 时间框架敏感度特征部分出错: %s", interval_str, e_tf_sensitivity_sec)
    except Exception as e_tf_sensitivity_sec_unexpected:
        logger.error("_add_timeframe_sensitivity_features: (%s) 时间框架敏感度特征部分发生意外错误: %s", interval_str, e_tf_sensitivity_sec_unexpected)

def _get_intelligent_default_value(col_name, series, feature_defaults):
    """
    为特征列智能选择默认值

    Args:
        col_name (str): 列名
        series (pd.Series): 数据序列
        feature_defaults (dict): 预定义的默认值字典

    Returns:
        float: 智能选择的默认值
    """
    # 首先检查是否有预定义的默认值
    if col_name in feature_defaults:
        return feature_defaults[col_name]

    # 根据列名模式智能选择默认值
    col_lower = col_name.lower()

    # 价格变化特征 - 优先处理，避免被价格相关特征捕获
    if any(keyword in col_lower for keyword in ['change', 'pct']) and 'volume' not in col_lower:
        return 0.0  # 变化率默认为0

    # 价格相关特征
    elif any(keyword in col_lower for keyword in ['price', 'close', 'open', 'high', 'low', 'hma']) and 'change' not in col_lower:
        # 使用历史中位数，如果没有则使用0
        non_nan_values = series.dropna()
        if len(non_nan_values) > 0:
            return float(non_nan_values.median())
        return 0.0

    # 成交量相关特征
    elif any(keyword in col_lower for keyword in ['volume', 'vol']):
        if 'ratio' in col_lower or 'vs' in col_lower:
            return 1.0  # 比率默认为1
        return 0.0  # 成交量变化默认为0

    # RSI类指标 (0-100范围)
    elif any(keyword in col_lower for keyword in ['rsi', 'stoch']):
        return 50.0  # 中性值

    # Williams %R (-100到0范围)
    elif 'willr' in col_lower or 'williams' in col_lower:
        return -50.0  # 中性值

    # MACD相关
    elif 'macd' in col_lower:
        return 0.0  # MACD默认为0

    # 位置相关特征 (0-1范围)
    elif any(keyword in col_lower for keyword in ['pos_in', 'position', 'percent_b']):
        return 0.5  # 中间位置

    # 布尔型特征
    elif any(keyword in col_lower for keyword in ['is_', 'above', 'signal', 'cross']):
        return 0  # 布尔型默认为False(0)

    # 时间特征
    elif any(keyword in col_lower for keyword in ['hour', 'day', 'week', 'month']):
        return 0  # 时间特征默认为0

    # 三角函数特征
    elif any(keyword in col_lower for keyword in ['sin', 'cos']):
        if 'cos' in col_lower:
            return 1.0  # cos(0) = 1
        elif 'sin' in col_lower:
            return 0.0  # sin(0) = 0
        return 0.0

    # 趋势斜率
    elif 'slope' in col_lower or 'trend' in col_lower:
        return 0.0  # 无趋势

    # 波动率相关 (ATR等)
    elif any(keyword in col_lower for keyword in ['atr', 'volatility', 'range']):
        # 使用历史中位数
        non_nan_values = series.dropna()
        if len(non_nan_values) > 0:
            return float(non_nan_values.median())
        return 0.0

    # K线形态名称特征
    elif 'pattern' in col_lower and 'name' in col_lower:
        return 'Unknown'  # 字符串类型的默认值

    # 默认情况：尝试使用历史数据
    non_nan_values = series.dropna()
    if len(non_nan_values) > 0:
        # 对于有历史数据的列，使用中位数
        return float(non_nan_values.median())

    # 最后的兜底值
    return 0.0

def _apply_final_processing(df_out, feature_defaults, interval_str):
    """应用智能的最终NaN/Inf处理和默认值填充"""
    try:
        # 第一步：处理无穷大值并收集统计信息
        numeric_cols = df_out.select_dtypes(include=[np.number]).columns
        inf_stats = {}

        logger.debug("_apply_final_processing: (%s) 开始处理 %d 个数值列", interval_str, len(numeric_cols))

        for col in numeric_cols:
            if col in df_out.columns:
                # 统计无穷大值
                inf_count = np.isinf(df_out[col]).sum()
                if inf_count > 0:
                    inf_stats[col] = inf_count

                # 替换无穷大值为NaN
                df_out[col] = df_out[col].replace([np.inf, -np.inf], np.nan)

        # 记录无穷大值统计
        if inf_stats:
            inf_summary = ", ".join([f"{col}:{count}" for col, count in inf_stats.items()])
            logger.info("_apply_final_processing: (%s) 发现并替换无穷大值: [%s]", interval_str, inf_summary)

        # 第二步：智能填充NaN值
        nan_fill_stats = {}

        for col in numeric_cols:
            if col in df_out.columns:
                nan_count_before = df_out[col].isnull().sum()

                if nan_count_before > 0:
                    # 获取智能默认值
                    intelligent_default = _get_intelligent_default_value(col, df_out[col], feature_defaults)

                    # 使用安全填充方法
                    df_out[col] = safe_fill_nans(df_out[col], default_value=intelligent_default)

                    # 检查填充后的NaN数量
                    nan_count_after = df_out[col].isnull().sum()
                    filled_count = nan_count_before - nan_count_after

                    if filled_count > 0:
                        nan_fill_stats[col] = {
                            'filled_count': filled_count,
                            'default_value': intelligent_default,
                            'remaining_nan': nan_count_after
                        }

        # 记录NaN填充统计
        if nan_fill_stats:
            logger.info("_apply_final_processing: (%s) 智能填充NaN值统计:", interval_str)
            for col, stats in nan_fill_stats.items():
                logger.info("  %s: 填充 %d 个值 (默认值: %.4f), 剩余NaN: %d",
                           col, stats['filled_count'], stats['default_value'], stats['remaining_nan'])

        # 第三步：最终兜底处理 - 处理仍然存在的NaN值
        final_nan_stats = {}
        total_remaining_nans = 0

        for col in numeric_cols:
            if col in df_out.columns:
                remaining_nans = df_out[col].isnull().sum()
                if remaining_nans > 0:
                    total_remaining_nans += remaining_nans

                    # 使用更保守的兜底策略
                    fallback_value = _get_intelligent_default_value(col, df_out[col], feature_defaults)
                    df_out[col] = df_out[col].fillna(fallback_value)

                    final_nan_stats[col] = {
                        'count': remaining_nans,
                        'fallback_value': fallback_value
                    }

        # 记录最终兜底填充
        if final_nan_stats:
            logger.warning("_apply_final_processing: (%s) 最终兜底填充 %d 个NaN值:", interval_str, total_remaining_nans)
            for col, stats in final_nan_stats.items():
                logger.warning("  %s: %d 个NaN → %.4f", col, stats['count'], stats['fallback_value'])

        # 第四步：确保所有预期特征列都存在
        missing_features = []
        for feature_name, default_val in feature_defaults.items():
            if feature_name not in df_out.columns:
                df_out[feature_name] = default_val
                missing_features.append(feature_name)

        if missing_features:
            logger.info("_apply_final_processing: (%s) 创建缺失的特征列: %s", interval_str, missing_features[:5])
            if len(missing_features) > 5:
                logger.debug("_apply_final_processing: (%s) 总共创建了 %d 个缺失特征列", interval_str, len(missing_features))

        # 第五步：最终验证
        final_numeric_cols = df_out.select_dtypes(include=[np.number]).columns
        final_nan_count = df_out[final_numeric_cols].isnull().sum().sum()
        final_inf_count = np.isinf(df_out[final_numeric_cols]).sum().sum()

        if final_nan_count > 0:
            logger.error("_apply_final_processing: (%s) 警告：处理后仍有 %d 个NaN值！", interval_str, final_nan_count)

        if final_inf_count > 0:
            logger.error("_apply_final_processing: (%s) 警告：处理后仍有 %d 个无穷大值！", interval_str, final_inf_count)

        logger.info("_apply_final_processing: (%s) 完成智能最终处理。数值列: %d, 最终NaN: %d, 最终Inf: %d",
                   interval_str, len(final_numeric_cols), final_nan_count, final_inf_count)

    except Exception as e_final:
        logger.error("_apply_final_processing: (%s) 最终处理时出错: %s", interval_str, e_final)
        logger.debug(traceback.format_exc())

def _generate_dynamic_feature_defaults(cfg):
    """
    根据配置参数动态生成特征默认值字典，确保特征名与实际计算的特征名一致
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    # 获取配置参数
    hma_period = _get_cfg('hma_period', 14)
    rsi_period = _get_cfg('rsi_period', 14)
    atr_period = _get_cfg('atr_period', 14)
    # 🚨 修复：WILLR使用专门的周期参数（与实际计算逻辑一致）
    willr_period = _get_cfg('willr_period', 14)
    cci_constant = _get_cfg('cci_constant', 0.015)
    # 资金流平滑周期
    fund_flow_smoothing_period = _get_cfg('fund_flow_ratio_smoothing_period', 5)

    # 基础特征默认值（不依赖配置参数的）
    base_defaults = {
        # Price Change Features
        'price_change_1p': 0.0, 'price_change_3p': 0.0, 'price_change_5p': 0.0, 'price_change_10p': 0.0,
        # Volume Features
        'volume_change_1p': 0.0, 'volume_vs_avg': 1.0,
        # Candle Features
        'body_size': 0.0, 'candle_range': 0.0, 'upper_shadow': 0.0, 'lower_shadow': 0.0,
        'is_green_candle': 0, 'is_doji': 0, 'close_pos_in_candle': 0.5,
        'body_size_norm': 0.0, 'upper_shadow_norm': 0.0, 'lower_shadow_norm': 0.0, 'candle_range_norm': 0.0,
        'upper_shadow_smooth3p': 0.0, 'close_pos_in_candle_smooth3p': 0.5, 'body_size_smooth3p': 0.0,
        'candlestick_pattern_name': 'Unknown',
        # KC通道特征（不依赖周期参数）
        'KC_lower': 0.0, 'KC_middle': 0.0, 'KC_upper': 0.0,
        # MACD特征（不依赖周期参数）
        'MACD': 0.0, 'MACD_histogram': 0.0, 'MACD_signal': 0.0,
        # STOCH特征（不依赖周期参数）
        'STOCH_k': 50.0, 'STOCH_d': 50.0,
        # 衍生特征
        'price_vs_hma': 0.0, 'price_pos_in_kc': 0.5, 'macd_above_signal': 0,
        # Time Features
        'hour': 0, 'day_of_week': 0, 'is_weekend': 0,
        'hour_sin': 0.0, 'hour_cos': 1.0, 'day_sin': 0.0, 'day_cos': 1.0,
        # Fund Flow Features
        'fund_flow_indicator': 0.0, 'taker_buy_ratio': 0.5,
        # Trend Features
        'trend_slope_period_1': 0.0, 'trend_slope_period_2': 0.0,
        'trend_adx_signal': 0.0, 'trend_ema_signal': 0.0,
        'adx_value': 0.0, 'adx_pdi': 0.0, 'adx_mdi': 0.0,
        'ema_short': 0.0, 'ema_long': 0.0,
        # 🎯 新增：EMA距离特征（UP和DOWN模型通用）
        'ema_distance_abs': 0.0, 'ema_distance_pct': 0.0,
        'ema_bullish_strength': 0.0, 'ema_bearish_strength': 0.0,
        # 🎯 新增：布林带突破强度特征（UP和DOWN模型通用）
        'bb_upper_breakout_strength': 0.0, 'price_vs_bb_upper_pct': 0.0,
        'bb_lower_breakout_strength': 0.0, 'price_vs_bb_lower_pct': 0.0,
        # 🎯 新增：时间框架敏感度特征（UP和DOWN模型通用）
        'rsi_15m_vs_4h_diff': 0.0, 'rsi_15m_vs_4h_ratio': 1.0,
        'close_pos_15m_vs_4h_diff': 0.0, 'close_pos_15m_vs_4h_deviation': 0.0,
        'macd_15m_vs_4h_diff': 0.0, 'macd_15m_vs_4h_signal_agreement': 1,
        'volume_ratio_15m_vs_4h_diff': 0.0, 'volume_activity_15m_vs_4h_ratio': 1.0,
    }

    # 动态生成依赖配置参数的技术指标特征默认值
    dynamic_defaults = {}

    # HMA特征
    if isinstance(hma_period, int) and hma_period > 0:
        dynamic_defaults[f'HMA_{hma_period}'] = 0.0

    # RSI特征
    if isinstance(rsi_period, int) and rsi_period > 0:
        dynamic_defaults[f'RSI_{rsi_period}'] = 50.0

    # ATR特征
    if isinstance(atr_period, int) and atr_period > 0:
        dynamic_defaults[f'ATRr_{atr_period}'] = 0.0

    # Williams %R特征
    if isinstance(willr_period, int) and willr_period > 0:
        dynamic_defaults[f'WILLR_{willr_period}'] = -50.0

    # CCI特征 - 🚨 修复：使用专门的CCI周期参数
    cci_period = cfg.get('cci_period', 14) if cfg else 14
    if isinstance(cci_period, int) and cci_period > 0 and isinstance(cci_constant, (int, float)):
        dynamic_defaults[f'CCI_{cci_period}_{cci_constant}'] = 0.0

    # 资金流平滑特征
    if isinstance(fund_flow_smoothing_period, int) and fund_flow_smoothing_period > 0:
        dynamic_defaults[f'taker_buy_ratio_smooth{fund_flow_smoothing_period}p'] = 0.5

    # 🚨 新增：MTFA特征默认值生成
    if cfg.get('enable_mtfa', False):
        mtfa_timeframes = cfg.get('mtfa_timeframes', [])
        if mtfa_timeframes:
            for tf in mtfa_timeframes:
                if isinstance(tf, str):
                    # 为每个时间框架生成对应的特征默认值
                    # 基础特征
                    for base_feat in ['price_change_1p', 'price_change_2p', 'price_change_3p', 'price_change_5p', 'price_change_10p']:
                        dynamic_defaults[f'{base_feat}_{tf}'] = 0.0

                    # K线特征
                    for candle_feat in ['body_size', 'candle_range', 'upper_shadow', 'lower_shadow', 'close_pos_in_candle',
                                       'body_size_norm', 'upper_shadow_norm', 'lower_shadow_norm', 'candle_range_norm',
                                       'upper_shadow_smooth3p', 'close_pos_in_candle_smooth3p', 'body_size_smooth3p']:
                        dynamic_defaults[f'{candle_feat}_{tf}'] = 0.0

                    # 技术指标特征（使用配置参数）
                    if isinstance(hma_period, int) and hma_period > 0:
                        dynamic_defaults[f'HMA_{hma_period}_{tf}'] = 0.0

                    if isinstance(rsi_period, int) and rsi_period > 0:
                        dynamic_defaults[f'RSI_{rsi_period}_{tf}'] = 50.0

                    if isinstance(atr_period, int) and atr_period > 0:
                        dynamic_defaults[f'ATRr_{atr_period}_{tf}'] = 0.0

                    if isinstance(willr_period, int) and willr_period > 0:
                        dynamic_defaults[f'WILLR_{willr_period}_{tf}'] = -50.0

                    # KC通道特征
                    for kc_feat in ['KC_lower', 'KC_middle', 'KC_upper']:
                        dynamic_defaults[f'{kc_feat}_{tf}'] = 0.0

                    # MACD特征
                    for macd_feat in ['MACD', 'MACD_histogram', 'MACD_signal']:
                        dynamic_defaults[f'{macd_feat}_{tf}'] = 0.0

                    # STOCH特征
                    dynamic_defaults[f'STOCH_k_{tf}'] = 50.0
                    dynamic_defaults[f'STOCH_d_{tf}'] = 50.0

                    # 衍生特征
                    dynamic_defaults[f'price_vs_hma_{tf}'] = 0.0
                    dynamic_defaults[f'price_pos_in_kc_{tf}'] = 0.5

                    # 趋势特征
                    for trend_feat in ['trend_slope_period_1', 'trend_slope_period_2']:
                        dynamic_defaults[f'{trend_feat}_{tf}'] = 0.0

                    # ADX特征
                    for adx_feat in ['adx_value', 'adx_pdi', 'adx_mdi']:
                        dynamic_defaults[f'{adx_feat}_{tf}'] = 0.0

                    # EMA特征
                    for ema_feat in ['ema_short', 'ema_long']:
                        dynamic_defaults[f'{ema_feat}_{tf}'] = 0.0

    # 合并基础默认值和动态默认值
    feature_defaults = {**base_defaults, **dynamic_defaults}

    return feature_defaults


def add_classification_features(df, target_config):
    """
    为分类模型添加特征，采用模块化设计。

    Args:
        df (pd.DataFrame): 包含OHLCV数据的DataFrame
        target_config (dict): 目标配置字典

    Returns:
        pd.DataFrame or None: 包含特征的DataFrame，失败时返回None
    """
    # 输入验证
    if not isinstance(df, pd.DataFrame):
        logger.error("add_classification_features: df必须是DataFrame类型，而不是 %s", type(df))
        raise TypeError("Error: df必须是DataFrame类型")

    if not isinstance(target_config, dict):
        logger.error("add_classification_features: target_config必须是dict类型，而不是 %s", type(target_config))
        raise TypeError("Config Error in add_classification_features")

    interval_str = target_config.get('interval', '未知周期')
    target_name = target_config.get('name', 'unknown_target')

    # 调试信息：打印关键配置参数
    logger.info("add_classification_features: (%s) 开始特征计算，目标: %s", interval_str, target_name)
    logger.info("add_classification_features: (%s) 关键配置参数:", interval_str)
    logger.info("  enable_volume: %s", target_config.get('enable_volume', 'NOT_SET'))
    logger.info("  enable_ta: %s", target_config.get('enable_ta', 'NOT_SET'))
    logger.info("  volume_avg_period: %s", target_config.get('volume_avg_period', 'NOT_SET'))
    logger.info("  rsi_period: %s", target_config.get('rsi_period', 'NOT_SET'))
    logger.info("  hma_period: %s", target_config.get('hma_period', 'NOT_SET'))
    logger.info("  数据行数: %d", len(df))

    # 基础数据准备
    df_out = df.copy()
    feature_count_start = df_out.shape[1]
    cfg = target_config

    # 动态生成特征默认值，根据配置参数确定正确的特征名
    feature_defaults = _generate_dynamic_feature_defaults(cfg)

    # 基础列验证和预处理
    base_cols = ['open', 'high', 'low', 'close', 'volume']
    if cfg.get('enable_fund_flow', False) and 'tbbav' not in base_cols:
        base_cols.append('tbbav')

    # 检查必要的基础列是否存在
    if not all(col in df_out.columns for col in base_cols if col != 'tbbav' or cfg.get('enable_fund_flow', False)):
        missing_cols = [col for col in base_cols if (col != 'tbbav' or cfg.get('enable_fund_flow', False)) and col not in df_out.columns]
        logger.error("add_classification_features: 为 %s 计算特征缺少基础列: %s", interval_str, missing_cols)
        return None

    try:
        cols_to_process = [col for col in base_cols if col in df_out.columns]
        df_out[cols_to_process] = df_out[cols_to_process].apply(pd.to_numeric, errors='coerce')

        # 检查并处理NaN值
        if df_out[cols_to_process].isnull().values.any():
            logger.debug("add_classification_features: 为 %s 填充基础列中的NaN值", interval_str)
            # 使用安全填充方法替代ffill().bfill()
            for col in cols_to_process:
                # 为不同列设置合适的默认值
                default_val = 0
                if col in ['open', 'high', 'low', 'close']:
                    # 价格列使用第一个非NaN值
                    non_nan_vals = df_out[col].dropna()
                    default_val = non_nan_vals.iloc[0] if len(non_nan_vals) > 0 else 0
                elif col == 'volume' or col == 'tbbav':
                    default_val = 0  # 交易量默认为0

                df_out[col] = safe_fill_nans(df_out[col], default_val)

        # 再次检查是否还有NaN值
        if df_out[cols_to_process].isnull().values.any():
            logger.error("add_classification_features: (%s) 填充后基础列中仍有NaN值！", interval_str)
            return None

    except (ValueError, TypeError) as e_convert_numeric:
        logger.error("add_classification_features: (%s) 转换基础列为数值时出错: %s", interval_str, e_convert_numeric)
        return None
    except Exception as e_unexpected:
        logger.error("add_classification_features: (%s) 处理基础列时发生意外错误: %s", interval_str, e_unexpected)
        return None

    # 提取基础价格和成交量数据
    C = df_out['close']
    H = df_out['high']
    L = df_out['low']
    O = df_out['open']
    V_feat = df_out['volume']

    logger.info("add_classification_features: (%s) 开始模块化特征计算，初始列数: %d", interval_str, feature_count_start)

    # 调用模块化特征计算函数
    try:
        # 1. 价格变化特征
        _add_price_change_features(df_out, cfg, C, interval_str)

        # 2. 成交量特征
        _add_volume_features(df_out, cfg, V_feat, interval_str)

        # 3. K线形态特征
        _add_candle_features(df_out, cfg, C, H, L, O, interval_str)

        # 4. 技术指标特征
        _add_technical_indicators(df_out, cfg, C, H, L, interval_str)

        # 5. 时间特征
        _add_time_features(df_out, cfg, interval_str)

        # 6. 资金流向特征
        _add_fund_flow_features(df_out, cfg, interval_str)

        # 7. 趋势特征
        _add_trend_features(df_out, cfg, C, H, L, interval_str)

        # 8. 交互特征（特征组合）
        _add_interaction_features(df_out, cfg, C, H, L, O, V_feat, interval_str)

        # 9. 高阶特征（导数特征）
        _add_higher_order_features(df_out, cfg, interval_str)

        # 10. 🎯 新增：市场状态自适应特征
        _add_market_state_adaptive_features(df_out, cfg, C, H, L, O, V_feat, interval_str)

    except Exception as e_feature_calc:
        logger.error("add_classification_features: (%s) 模块化特征计算时出错: %s", interval_str, e_feature_calc)
        logger.debug(traceback.format_exc())
        return None

    # 最终处理：NaN/Inf处理和默认值填充
    _apply_final_processing(df_out, feature_defaults, interval_str)

    # 统计和日志
    feature_count_end = df_out.shape[1]
    new_features_count = feature_count_end - feature_count_start

    logger.info("add_classification_features: (%s) 特征计算完成。新增特征: %d, 总列数: %d",
               interval_str, new_features_count, feature_count_end)
    logger.debug("add_classification_features: (%s) EXIT for target %s, interval %s", target_name, interval_str)

    return df_out



# --- add_mtfa_features_to_df ---
def add_mtfa_features_to_df(primary_df, target_config, client):
    target_name_for_logs = target_config.get('name', 'UnknownTarget') # For clearer logs
    if not isinstance(target_config, dict):
        logger.error("add_mtfa_features_to_df: 配置类型错误，期望dict，得到%s", type(target_config))
        raise TypeError(f"Config Error in add_mtfa_features_to_df: Expected dict, got {type(target_config)}")
        
    mtfa_timeframes = target_config.get('mtfa_timeframes', [])
    primary_interval = target_config.get('interval', 'unknown')
    enable_mtfa_globally = target_config.get('enable_mtfa', False)
    
    if not enable_mtfa_globally or not mtfa_timeframes:
        if enable_mtfa_globally and not mtfa_timeframes:
            logger.warning("add_mtfa_features_to_df: MTFA已启用但未配置mtfa_timeframes，跳过MTFA特征添加")
        return primary_df
        
    try:
        df_merged = primary_df.copy()
        if not isinstance(df_merged.index, pd.DatetimeIndex): 
            df_merged.index = pd.to_datetime(df_merged.index)
        if df_merged.index.tz is None: 
            df_merged.index = df_merged.index.tz_localize('UTC')
        elif df_merged.index.tz != timezone.utc: 
            df_merged.index = df_merged.index.tz_convert('UTC')
    except (ValueError, TypeError, AttributeError) as e_index:
        logger.error("add_mtfa_features_to_df: 处理DataFrame索引时出错: %s", e_index)
        return primary_df
        
    try:
        primary_start_time = df_merged.index.min()
        primary_end_time = df_merged.index.max()
        primary_timedelta = interval_to_timedelta(primary_interval)
    except (ValueError, TypeError, AttributeError) as e_time: 
        logger.error("add_mtfa_features_to_df: 无法确定主时间范围: %s", e_time)
        return primary_df
        
    if not isinstance(mtfa_timeframes, list): 
        logger.error("add_mtfa_features_to_df: 错误: MTFA配置不是列表")
        return primary_df
        
    symbol_to_fetch = target_config.get('symbol', config.SYMBOL)
    logger.info("add_mtfa_features_to_df: (%s) 开始处理MTFA特征，时间框架: %s", target_name_for_logs, mtfa_timeframes)
    
    for tf in mtfa_timeframes:
        if not isinstance(tf, str): 
            logger.warning("add_mtfa_features_to_df: 无效MTFA时间框架 '%s', 跳过", tf)
            continue
            
        try:
            tf_timedelta = interval_to_timedelta(tf)
            if tf_timedelta <= primary_timedelta: 
                logger.warning("add_mtfa_features_to_df: (%s/%s) MTFA周期 '%s' (%s) <= 主周期 '%s' (%s), 跳过", 
                              target_name_for_logs, tf, tf, tf_timedelta, primary_interval, primary_timedelta)
                continue

            # Determine effective lookback periods for the current MTFA timeframe
            specific_lookbacks = target_config.get('mtfa_specific_lookbacks', {})
            current_tf_lookback = specific_lookbacks.get(tf)

            if current_tf_lookback is not None and isinstance(current_tf_lookback, int) and current_tf_lookback > 0:
                effective_lookback_periods = current_tf_lookback
                logger.debug(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) Using specific lookback of {effective_lookback_periods} periods for MTFA timeframe {tf}.")
            else:
                effective_lookback_periods = target_config.get('mtfa_feature_lookback_periods', 200) # Fallback
                if current_tf_lookback is not None: # Log if a value was present but invalid
                    logger.warning(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) Invalid specific lookback value '{current_tf_lookback}' for MTFA timeframe {tf}. Using fallback: {effective_lookback_periods}.")
                else:
                    logger.debug(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) Using default/fallback lookback of {effective_lookback_periods} periods for MTFA timeframe {tf}.")

            # Determine the start and end datetimes needed for MTFA data fetching
            mtfa_calc_start_dt = primary_start_time - (effective_lookback_periods * tf_timedelta)
            mtfa_fetch_end_dt = primary_end_time + tf_timedelta 

            # Estimate the number of bars needed for the MTFA timeframe
            total_duration_to_fetch = mtfa_fetch_end_dt - mtfa_calc_start_dt
            if tf_timedelta.total_seconds() > 0:
                estimated_bars_for_duration = int(total_duration_to_fetch / tf_timedelta) + 1 # +1 for safety with division
            else:
                logger.warning(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) MTFA timeframe {tf} results in zero timedelta. Cannot estimate bars accurately.")
                estimated_bars_for_duration = target_config.get('mtfa_min_bars_to_fetch', 300) # Fallback to min_bars_to_fetch
            
            estimated_bars_needed = estimated_bars_for_duration + target_config.get('mtfa_fetch_buffer', 10) # Add a small buffer
            
            # Apply overall minimum and maximum fetch limits
            final_fetch_limit = max(target_config.get('mtfa_min_bars_to_fetch', 50), estimated_bars_needed)
            final_fetch_limit = min(config.DATA_FETCH_LIMIT, final_fetch_limit)

            logger.debug(f"add_mtfa_features_to_df: ({target_name_for_logs}/{tf}) Calculated fetch parameters: "
                         f"effective_lookback={effective_lookback_periods}, mtfa_calc_start_dt={mtfa_calc_start_dt}, "
                         f"mtfa_fetch_end_dt={mtfa_fetch_end_dt}, estimated_bars_for_duration={estimated_bars_for_duration}, "
                         f"final_fetch_limit={final_fetch_limit}")
            
            try:
                df_tf = fetch_binance_history(client, symbol_to_fetch, tf, limit=final_fetch_limit, 
                                             start_dt=mtfa_calc_start_dt, end_dt=mtfa_fetch_end_dt)
                                             
                if df_tf is None or df_tf.empty or len(df_tf) < target_config.get('mtfa_min_bars_for_calc', 50):
                    logger.warning("add_mtfa_features_to_df: (%s/%s) 未获取到足够 %s 数据 (获取 %d 条, 至少需 %d 条), 跳过", 
                                  target_name_for_logs, tf, tf, len(df_tf) if df_tf is not None else 0, target_config.get('mtfa_min_bars_for_calc', 50))
                    continue
            except (ValueError, TypeError, ConnectionError) as e_fetch:
                logger.error("add_mtfa_features_to_df: (%s/%s) 获取 %s 数据时出错: %s", target_name_for_logs, tf, tf, e_fetch)
                continue
                
            try:
                mtfa_temp_config = target_config.copy() 
                mtfa_temp_config['interval'] = tf       
                mtfa_temp_config['enable_mtfa'] = False 
                mtfa_temp_config['enable_ta'] = True  
                df_tf_features_raw = add_classification_features(df_tf.copy(), mtfa_temp_config) 
                
                if df_tf_features_raw is None or df_tf_features_raw.empty: 
                    logger.warning("add_mtfa_features_to_df: (%s/%s) 未能计算 %s 特征 (add_classification_features返回空), 跳过", target_name_for_logs, tf, tf)
                    continue
            except (ValueError, TypeError) as e_features:
                logger.error("add_mtfa_features_to_df: (%s/%s) 计算 %s 特征时出错: %s", target_name_for_logs, tf, tf, e_features)
                continue
                
            try:
                base_cols_excl = {'open','high','low','close','volume','qav','n','tbbav','tbqav'}

                # 过滤列：排除基础列、目标列和字符串类型列
                cols_to_keep = []
                for col in df_tf_features_raw.columns:
                    if (col not in base_cols_excl and
                        not col.startswith('target_') and
                        not col.endswith('_name') and  # 排除形态名称列
                        col != 'candlestick_pattern_name'):  # 明确排除K线形态名称列

                        # 检查列的数据类型，只保留数值类型的列
                        if df_tf_features_raw[col].dtype in ['int64', 'float64', 'int32', 'float32', 'bool']:
                            cols_to_keep.append(col)
                        else:
                            logger.debug("add_mtfa_features_to_df: (%s/%s) 跳过非数值列: %s (类型: %s)",
                                       target_name_for_logs, tf, col, df_tf_features_raw[col].dtype)

                if not cols_to_keep:
                    logger.warning("add_mtfa_features_to_df: (%s/%s) 为 %s 未找到可用特征列 (可能因为禁用了TA且无其他特征), 跳过", target_name_for_logs, tf, tf)
                    continue

                df_tf_features = df_tf_features_raw[cols_to_keep].copy()
                df_tf_features.rename(columns=lambda x: f"{x}_{tf}", inplace=True) 
                
                if not isinstance(df_tf_features.index, pd.DatetimeIndex): 
                    df_tf_features.index = pd.to_datetime(df_tf_features.index)
                if df_tf_features.index.tz is None: 
                    df_tf_features.index = df_tf_features.index.tz_localize('UTC')
                elif df_tf_features.index.tz != timezone.utc: 
                    df_tf_features.index = df_tf_features.index.tz_convert('UTC')
                    
                df_tf_aligned = df_tf_features.reindex(df_merged.index, method='ffill')
                
                if df_tf_aligned.isnull().sum().sum() > 0:
                    df_tf_aligned.bfill(inplace=True)
                    if df_tf_aligned.isnull().sum().sum() > 0: 
                        logger.debug("add_mtfa_features_to_df: (%s/%s) %s 特征存在NaN值，使用0填充", target_name_for_logs, tf, tf)
                        df_tf_aligned.fillna(0, inplace=True)
                        
                df_merged = pd.merge(df_merged, df_tf_aligned, left_index=True, right_index=True, how='left')
                
                if df_merged.isnull().sum().sum() > 0:
                    df_merged.ffill(inplace=True).bfill(inplace=True).fillna(0, inplace=True)
                    
                logger.info("add_mtfa_features_to_df: (%s/%s) 成功添加 %s 时间框架的 %d 个特征", target_name_for_logs, tf, tf, len(cols_to_keep))
            except (ValueError, TypeError, KeyError) as e_process:
                logger.error("add_mtfa_features_to_df: (%s/%s) 处理 %s 特征数据时出错: %s", target_name_for_logs, tf, tf, e_process)
                continue
        except (ValueError, TypeError) as e_tf_proc:
            logger.error("add_mtfa_features_to_df: (%s/%s) 处理MTFA时间框架 '%s' 时出错: %s", target_name_for_logs, tf, tf, e_tf_proc)
            continue
        except Exception as e_tf_unexpected:
            logger.error("add_mtfa_features_to_df: (%s/%s) 处理MTFA时间框架 '%s' 时发生意外错误: %s", target_name_for_logs, tf, tf, e_tf_unexpected)
            continue
            
    try:
        if df_merged.isnull().sum().sum() > 0:
            logger.debug("add_mtfa_features_to_df: 最终数据存在NaN值，进行填充")
            df_merged.ffill(inplace=True).bfill(inplace=True).fillna(0, inplace=True)
    except (ValueError, TypeError) as e_final:
        logger.error("add_mtfa_features_to_df: 最终数据处理出错: %s", e_final)
        
    # 🎯 新增：在MTFA特征添加完成后，计算时间框架敏感度特征
    try:
        if target_config.get('enable_timeframe_sensitivity', False):
            logger.info("add_mtfa_features_to_df: (%s) 开始计算时间框架敏感度特征", target_name_for_logs)
            _add_timeframe_sensitivity_features(df_merged, target_config, primary_interval)
            logger.info("add_mtfa_features_to_df: (%s) 时间框架敏感度特征计算完成", target_name_for_logs)
    except Exception as e_tf_sensitivity:
        logger.error("add_mtfa_features_to_df: (%s) 计算时间框架敏感度特征时出错: %s", target_name_for_logs, e_tf_sensitivity)
        logger.debug(traceback.format_exc())

    logger.info("add_mtfa_features_to_df: (%s) 完成MTFA特征添加，最终特征数: %d", target_name_for_logs, df_merged.shape[1])
    return df_merged

# --- create_target_variable (重构统一标签体系) ---
def create_target_variable(df, target_config):
    """
    创建目标变量，采用统一的标签体系。

    Args:
        df (pd.DataFrame): 包含 'close' 列的K线数据。
        target_config (dict): 目标配置字典。

    Returns:
        tuple: (pd.DataFrame, str or None)
               - DataFrame: 带有新目标列的DataFrame (已移除无效样本，可选移除中性样本)。
               - str: 目标列的名称，如果创建失败则为 None。

    标签体系:
        1: 上涨 (超过阈值)
        0: 下跌 (低于阈值)
        2: 中性 (在阈值范围内，仅当target_variable_type="BOTH"时使用)
        -1: 无效 (未来数据不足)
    """
    # 检查df是否为DataFrame类型
    if not isinstance(df, pd.DataFrame):
        logger.error(f"Error in create_target_variable: df必须是DataFrame类型，而不是{type(df)}")
        return None, None

    if not isinstance(target_config, dict):
        logger.error(f"Config Error in create_target_variable: Expected dict, got {type(target_config)}")
        return None, None

    target_name = target_config.get('name', 'UnknownTarget')
    
    try:
        periods_list = target_config.get('prediction_periods', [1])
        if not isinstance(periods_list, list) or not periods_list: periods_list = [1]
        period = periods_list[0] # 使用列表的第一个元素
        interval = target_config.get('interval', 'unknown')

        if not isinstance(period, int) or period <= 0:
            logger.error("create_target_variable: (%s) prediction_periods 无效 (%s)", target_name, period)
            return df, None

        base_target_col = f'target_{period}p'
        # 你可能不需要根据 PREDICTION_TARGETS 长度来决定是否加后缀了，因为目标名本身就区分了UP/DOWN
        target_col = f"{base_target_col}_{target_config.get('name', interval)}" # 使用目标名确保唯一性

        if 'close' not in df.columns:
            logger.error("create_target_variable: (%s) 'close' 列不存在于DataFrame中，无法创建目标变量", target_name)
            return None, None
    except (ValueError, TypeError, KeyError) as e_config:
        logger.error("create_target_variable: (%s) 配置处理错误: %s", target_name, e_config)
        return None, None

    try:
        df_out_create_target = df.copy()
        future_close_col = f'future_close_{period}p_for_{target_col}' # 确保临时列名唯一
        C_create_target = df_out_create_target['close']
        threshold = target_config.get('target_threshold', 0.0)
        target_variable_type = target_config.get('target_variable_type', 'BOTH').upper() # 转大写方便比较
        # drop_neutral_targets 的逻辑对于二分类需要重新思考，这里我们先假设目标是生成0和1
        # drop_neutral = target_config.get('drop_neutral_targets', False)

        df_out_create_target[future_close_col] = C_create_target.shift(-period)
    except (KeyError, ValueError, TypeError) as e_prep:
        logger.error("create_target_variable: (%s) 准备目标变量数据时出错: %s", target_name, e_prep)
        return None, None

    # 定义条件
    cond_invalid = df_out_create_target[future_close_col].isna()  # 未来价格不可用（数据末尾）
    cond_up = df_out_create_target[future_close_col] > C_create_target * (1 + threshold)
    cond_down = df_out_create_target[future_close_col] < C_create_target * (1 - threshold)

    target_name_for_log = target_config.get('name', 'UnknownTarget')

    # 使用统一的标签体系创建目标变量
    if target_variable_type == "UP_ONLY":
        # UP_ONLY: 1=明确上涨, 0=非明确上涨(下跌或中性), -1=无效
        df_out_create_target[target_col] = np.select(
            [cond_invalid, cond_up],
            [-1, 1],
            default=0  # 非明确上涨（包括下跌和中性）
        )
        logger.info(f"  ({target_name_for_log}): 创建UP_ONLY目标变量 '{target_col}' (1=明确上涨, 0=非明确上涨). 阈值: {threshold:.4f}, 未来期数: {period}")

    elif target_variable_type == "DOWN_ONLY":
        # DOWN_ONLY: 1=明确下跌, 0=非明确下跌(上涨或中性), -1=无效
        df_out_create_target[target_col] = np.select(
            [cond_invalid, cond_down],
            [-1, 1],
            default=0  # 非明确下跌（包括上涨和中性）
        )
        logger.info(f"  ({target_name_for_log}): 创建DOWN_ONLY目标变量 '{target_col}' (1=明确下跌, 0=非明确下跌). 阈值: {threshold:.4f}, 未来期数: {period}")

    elif target_variable_type == "BOTH":
        # BOTH: 1=上涨, 0=下跌, 2=中性, -1=无效
        df_out_create_target[target_col] = np.select(
            [cond_invalid, cond_up, cond_down],
            [-1, 1, 0],
            default=2  # 中性（在阈值范围内）
        )
        logger.info(f"  ({target_name_for_log}): 创建BOTH目标变量 '{target_col}' (1=上涨, 0=下跌, 2=中性). 阈值: {threshold:.4f}, 未来期数: {period}")

    else:
        logger.error(f"!!! 未知的 target_variable_type: '{target_variable_type}' for target '{target_name_for_log}'. 无法创建目标列 '{target_col}'.")
        return None, None

    # 首先移除未来价格不可用的行 (标记为-1)
    original_len = len(df_out_create_target)
    df_filtered = df_out_create_target[df_out_create_target[target_col] != -1].copy()
    invalid_removed_count = original_len - len(df_filtered)

    if invalid_removed_count > 0:
        logger.info(f"    ({target_name_for_log}): 移除了 {invalid_removed_count} 行，因为未来价格不可用 (通常是数据末尾的 {period} 行).")

    # 记录过滤前的分布
    if not df_filtered.empty:
        try:
            df_filtered[target_col] = df_filtered[target_col].astype(int)
            counts_before_filter = df_filtered[target_col].value_counts(normalize=True).sort_index()
            dist_str_before = ", ".join([f"{idx}:{val*100:.2f}%" for idx, val in counts_before_filter.items()])
            logger.info(f"    ({target_name_for_log}): 过滤前目标 '{target_col}' 分布: [{dist_str_before}] (样本数: {len(df_filtered)})")
        except (ValueError, TypeError) as e_dist_before:
            logger.warning(f"    警告 ({target_name_for_log}): 分析过滤前目标分布时出错: {e_dist_before}")

    # 根据 drop_neutral_targets 参数决定是否移除中性样本
    drop_neutral_targets = target_config.get('drop_neutral_targets', False)
    neutral_removed_count = 0

    if drop_neutral_targets and target_variable_type == "BOTH":
        len_before_neutral_filter = len(df_filtered)
        df_filtered = df_filtered[df_filtered[target_col] != 2].copy()
        neutral_removed_count = len_before_neutral_filter - len(df_filtered)

        if neutral_removed_count > 0:
            logger.info(f"    ({target_name_for_log}): 移除了 {neutral_removed_count} 行中性样本 (drop_neutral_targets=True)。")

    if target_col not in df_filtered.columns or df_filtered[target_col].isnull().all():
        logger.error(f"!!! ({target_name_for_log}): 目标列 '{target_col}' 创建失败或全为空。检查阈值 ({threshold:.4f}) 和未来期数 ({period}) 是否合理。")
        return None, None

    # 记录最终分布和标签含义
    try:
        if not df_filtered.empty:
            counts_final = df_filtered[target_col].value_counts(normalize=True).sort_index()
            dist_str_final = ", ".join([f"{idx}:{val*100:.2f}%" for idx, val in counts_final.items()])

            # 根据 target_variable_type 和 drop_neutral_targets 参数说明最终标签含义
            if target_variable_type == "UP_ONLY":
                label_meaning = "1=明确上涨, 0=非明确上涨"
            elif target_variable_type == "DOWN_ONLY":
                label_meaning = "1=明确下跌, 0=非明确下跌"
            elif target_variable_type == "BOTH":
                if drop_neutral_targets:
                    label_meaning = "1=上涨, 0=下跌 (已移除中性样本)"
                else:
                    label_meaning = "1=上涨, 0=下跌, 2=中性"
            else:
                label_meaning = "未知类型"

            logger.info(f"    ({target_name_for_log}): 最终目标 '{target_col}' 分布: [{dist_str_final}] (样本数: {len(df_filtered)}, {label_meaning})")
        else:
            logger.warning(f"    警告 ({target_name_for_log}): 创建目标变量 '{target_col}' 后DataFrame为空。")
    except ValueError as ve_astype:
        logger.error(f"  错误 ({target_name_for_log}): 将目标列 '{target_col}' 转换为int时发生ValueError: {ve_astype}. 列内容: {df_filtered[target_col].unique()[:10]}...")
        logger.debug(traceback.format_exc())
        return None, None
    except Exception as e_dist_final:
        logger.warning(f"  警告 ({target_name_for_log}): 分析最终目标分布时出错: {e_dist_final}")
        logger.debug(traceback.format_exc())

    try:
        df_filtered.drop(columns=[future_close_col], inplace=True, errors='ignore')
    except Exception as e_drop:
        logger.warning(f"  警告 ({target_name_for_log}): 尝试删除临时列 '{future_close_col}' 时出错: {e_drop}")
        logger.debug(traceback.format_exc())

    return df_filtered, target_col


# --- MODIFIED: prepare_features_for_prediction ---
def prepare_features_for_prediction(df_klines_recent_primary, client, scaler, target_config, model_meta=None):
    target_name = target_config.get('name', 'unknown_target_prep_feat')

    if not isinstance(target_config, dict):
        logger.error(f"Config Error in prepare_features_for_prediction for target '{target_name}': Expected dict, got {type(target_config)}.")
        return None
    
    if not scaler or not hasattr(scaler, 'transform'):
        logger.error(f"错误 [{target_name}]: 预测需要有效 Scaler. 当前 scaler: {scaler}, 类型: {type(scaler)}.")
        return None

    if not isinstance(df_klines_recent_primary, pd.DataFrame):
        logger.error(f"错误 [{target_name}]: df_klines_recent_primary 必须是DataFrame类型，而不是 {type(df_klines_recent_primary)}.")
        return None
        
    required_primary_lookback = target_config.get('min_historical_bars_for_prediction', 100)
    if len(df_klines_recent_primary) < required_primary_lookback:
        logger.error(f"错误 [{target_name}]: 主 K 线数据不足 (获取 {len(df_klines_recent_primary)} 条, 至少需 {required_primary_lookback} 条 for target '{target_name}').")
        return None
    
    try:
        df_primary_features = add_classification_features(df_klines_recent_primary.copy(), target_config)
        if df_primary_features is None or df_primary_features.empty:
            logger.error(f"错误 [{target_name}]: add_classification_features 返回空 (primary features) for target '{target_name}'.")
            return None
    except ValueError as ve:
        logger.error(f"错误 [{target_name}]: 计算基础特征时发生 ValueError: {ve}. Input df shape: {df_klines_recent_primary.shape}")
        logger.debug(traceback.format_exc())
        return None
    except KeyError as ke:
        logger.error(f"错误 [{target_name}]: 计算基础特征时发生 KeyError: {ke}. Available columns: {df_klines_recent_primary.columns.tolist()}")
        logger.debug(traceback.format_exc())
        return None
    except Exception as e:
        logger.error(f"错误 [{target_name}]: 计算基础特征时发生未知错误: {e}")
        logger.debug(traceback.format_exc())
        return None
  
    df_combined_features_intermediate = df_primary_features
    if target_config.get('enable_mtfa', False):
        try:
            df_mtfa_added = add_mtfa_features_to_df(df_primary_features.copy(), target_config, client)
            if df_mtfa_added is None or df_mtfa_added.empty:
                logger.warning(f"警告 [{target_name}]: add_mtfa_features_to_df 返回空，但MTFA已启用。将仅使用主周期特征。 Primary features shape: {df_primary_features.shape}")
            else:
                df_combined_features_intermediate = df_mtfa_added
        except BinanceAPIException as bae:
            logger.error(f"错误 [{target_name}]: 获取MTFA数据时发生 BinanceAPIException: {bae}. 将仅使用主周期特征.")
            logger.debug(traceback.format_exc())
        except BinanceRequestException as bre:
            logger.error(f"错误 [{target_name}]: 获取MTFA数据时发生 BinanceRequestException: {bre}. 将仅使用主周期特征.")
            logger.debug(traceback.format_exc())
        except Exception as e_mtfa:
            logger.error(f"错误 [{target_name}]: 添加MTFA特征时发生未知错误: {e_mtfa}. 将仅使用主周期特征.")
            logger.debug(traceback.format_exc())
    
    try:
        if df_combined_features_intermediate.empty or df_combined_features_intermediate.iloc[-1:].empty:
            logger.error(f"错误 [{target_name}]: df_combined_features_intermediate (shape: {df_combined_features_intermediate.shape if isinstance(df_combined_features_intermediate, pd.DataFrame) else 'N/A'}) 为空或没有最后一行可供提取。")
            return None
        latest_features_row_series = df_combined_features_intermediate.iloc[-1].copy()
        latest_features_index = df_combined_features_intermediate.index[-1:]
        latest_combined_df_for_selection = pd.DataFrame([latest_features_row_series.to_dict()], index=latest_features_index)
    except IndexError as ie:
        logger.error(f"错误 [{target_name}]: 提取最新特征行时发生 IndexError: {ie}. df_combined_features_intermediate shape: {df_combined_features_intermediate.shape}")
        logger.debug(traceback.format_exc())
        return None
    except Exception as e_latest_feat:
        logger.error(f"错误 [{target_name}]: 提取最新特征行时发生未知错误: {e_latest_feat}")
        logger.debug(traceback.format_exc())
        return None
    
    expected_features = []
    model_dir = target_config.get('model_save_dir')
    
    if model_meta is not None and 'feature_list_filename' in model_meta:
        feature_list_filename = model_meta.get('feature_list_filename')
        if model_dir and feature_list_filename:
            feature_list_path = os.path.join(model_dir, feature_list_filename)
            if os.path.exists(feature_list_path):
                try:
                    with open(feature_list_path, 'r') as f_feat:
                        feature_list_from_file = json.load(f_feat)
                        if isinstance(feature_list_from_file, list) and feature_list_from_file:
                            expected_features = feature_list_from_file
                            logger.info(f"  [{target_name}] 从文件 '{feature_list_path}' 加载了 {len(expected_features)} 个特征名。")
                except json.JSONDecodeError as jde:
                    logger.warning(f"警告 [{target_name}]: 特征列表文件 '{feature_list_path}' 包含无效JSON: {jde}。将尝试使用scaler中的特征名。")
                except FileNotFoundError:
                    logger.warning(f"警告 [{target_name}]: 特征列表文件 '{feature_list_path}' 未找到。将尝试使用scaler中的特征名。") # Corrected from print
                except PermissionError:
                    logger.warning(f"警告 [{target_name}]: 无权限读取特征列表文件 '{feature_list_path}'。将尝试使用scaler中的特征名。")
                except Exception as e_feat_load:
                    logger.warning(f"警告 [{target_name}]: 从文件 '{feature_list_path}' 加载特征列表失败: {e_feat_load}。将尝试使用scaler中的特征名。")
                    logger.debug(traceback.format_exc())
            else:
                 logger.warning(f"警告 [{target_name}]: 特征列表文件路径不存在 '{feature_list_path}'. 将尝试使用scaler中的特征名.")

    if not expected_features:
        if hasattr(scaler, 'feature_names_in_') and getattr(scaler, 'feature_names_in_', None) is not None and len(getattr(scaler, 'feature_names_in_', [])) > 0:
            expected_features = list(getattr(scaler, 'feature_names_in_', []))
            logger.info(f"  [{target_name}] 使用scaler.feature_names_in_中的 {len(expected_features)} 个特征名。 ({expected_features[:5]}...)")
        else:
            # If not loaded from file and scaler.feature_names_in_ is not available or empty, then we cannot proceed.
            logger.error(f"错误 [{target_name} prep_feat]: 无法确定预期的特征列表。未从文件加载特征列表，且scaler.feature_names_in_不可用或为空。")
            return None
        # No 'pass' here, if expected_features is still empty after this, the next block handles it.

    if not expected_features: # This block should ideally not be reached if the logic above is sound.
        logger.error(f"错误 [{target_name} prep_feat]: 最终未能确定expected_features列表。这表示之前的逻辑分支存在问题，未能从文件或scaler.feature_names_in_获取有效特征列表。")
        return None

    data_for_aligned_df = {}
    missing_in_pred = []
    present_cols_in_latest = latest_combined_df_for_selection.columns

    for col_name in expected_features:
        if col_name in present_cols_in_latest:
            data_for_aligned_df[col_name] = latest_combined_df_for_selection[col_name].values 
        else:
            data_for_aligned_df[col_name] = np.array([0.0]) 
            missing_in_pred.append(col_name)
            
    try:
        aligned_df_pred = pd.DataFrame(data_for_aligned_df, index=latest_features_index)
        final_df_to_scale = aligned_df_pred[expected_features]
    except KeyError as ke:
        logger.error(f"错误 [{target_name} prep_feat]: 从字典创建对齐的DataFrame时发生 KeyError: {ke}. Expected: {expected_features}, Available in dict: {list(data_for_aligned_df.keys())}")
        logger.debug(traceback.format_exc())
        return None
    except Exception as e_df_create:
        logger.error(f"错误 [{target_name} prep_feat]: 从字典创建对齐的DataFrame失败: {e_df_create}")
        logger.debug(traceback.format_exc())
        return None

    if missing_in_pred:
        logger.warning(f"警告 [{target_name} prep_feat]: 预测时缺失Scaler期望特征 (已用0填充): {missing_in_pred}. Expected: {expected_features[:10]}..., Available: {present_cols_in_latest.tolist()[:10]}...")
    
    try:
        if final_df_to_scale.isnull().values.any() or np.isinf(final_df_to_scale.values).any():
            nan_cols = final_df_to_scale.columns[final_df_to_scale.isnull().any()].tolist()
            inf_cols = final_df_to_scale.columns[np.isinf(final_df_to_scale).any()].tolist()
            logger.warning(f"警告 [{target_name} prep_feat]: final_df_to_scale (shape {final_df_to_scale.shape}) 包含 NaN/Inf，将用0填充. NaN cols: {nan_cols}, Inf cols: {inf_cols}")
            final_df_to_scale = final_df_to_scale.fillna(0).replace([np.inf, -np.inf], 0)
    except Exception as e_fill_na:
        logger.error(f"错误 [{target_name} prep_feat]: 填充 NaN/Inf 时发生错误: {e_fill_na}")
        logger.debug(traceback.format_exc())
        return None

    X_scaled_np = None
    try:
        X_scaled_np = scaler.transform(final_df_to_scale)
    except ValueError as ve:
        logger.error(f"缩放错误 [{target_name} prep_feat]: 特征数量或顺序不匹配! Scaler期望特征: {len(expected_features)} (名称: {expected_features[:5]}...), 当前传递特征: {final_df_to_scale.shape[1]} (名称: {list(final_df_to_scale.columns)[:5]}...). 错误: {ve}")
        logger.debug(traceback.format_exc())
        return None
    except TypeError as te:
        logger.error(f"缩放时发生 TypeError [{target_name} prep_feat]: {te}. Input dtypes: {final_df_to_scale.dtypes.to_dict()}")
        logger.debug(traceback.format_exc())
        return None
    except Exception as e_scale_pred:
        logger.error(f"缩放时发生未知错误 [{target_name} prep_feat]: {e_scale_pred}")
        logger.debug(traceback.format_exc())
        return None
    
    if X_scaled_np is None or not isinstance(X_scaled_np, np.ndarray) or X_scaled_np.shape[0] != 1:
        logger.error(f"错误 [{target_name} prep_feat]: 缩放结果无效或形状不为单行 (得到 shape: {X_scaled_np.shape if hasattr(X_scaled_np, 'shape') else 'N/A'}, type: {type(X_scaled_np)}). Scaler: {type(scaler)}")
        return None
    
    try:
        feature_names_for_output_df = list(final_df_to_scale.columns)
        if X_scaled_np.shape[1] != len(feature_names_for_output_df):
            logger.error(f"错误 [{target_name} prep_feat]: 缩放后特征数量 ({X_scaled_np.shape[1]}) 与预期列名数量 ({len(feature_names_for_output_df)}) 不符! Expected names: {feature_names_for_output_df[:5]}...")
            return None
        X_scaled_df_with_names = pd.DataFrame(X_scaled_np, columns=feature_names_for_output_df, index=final_df_to_scale.index)
    except Exception as e_final_df:
        logger.error(f"错误 [{target_name} prep_feat]: 创建最终缩放后DataFrame时发生错误: {e_final_df}. X_scaled_np shape: {X_scaled_np.shape}, feature_names_for_output_df len: {len(feature_names_for_output_df) if 'feature_names_for_output_df' in locals() else 'N/A'}")
        logger.debug(traceback.format_exc())
        return None
        
    logger.debug(f"  DEBUG [{target_name} prep_feat]: 成功准备并缩放了预测特征。Shape: {X_scaled_df_with_names.shape}")
    return X_scaled_df_with_names


def find_optimal_threshold(y_true, y_proba, target_name="", method="f1", verbose=True,
                          min_precision=None, precision_constraint=None,
                          payout_ratio=0.85, min_trades=5):
    """
    寻找最优决策阈值 (支持基于盈利能力的优化)

    Args:
        y_true (array-like): 真实标签 (0或1)
        y_proba (array-like): 正类的预测概率
        target_name (str): 目标名称，用于日志记录
        method (str): 优化方法，可选:
            - "f1": 最大化F1分数 (默认)
            - "precision_recall": 基于精确率-召回率曲线的最优点
            - "youden": Youden指数 (敏感性 + 特异性 - 1)
            - "balanced": 平衡精确率和召回率
            - "precision_constrained_recall": 在精确率约束下最大化召回率
            - "simulated_profit": 🎯 基于模拟交易盈利能力优化 (新增)
            - "risk_adjusted_return": 基于风险调整收益优化 (新增)
            - "expected_profit": 基于期望收益优化 (新增)
            - "grid_search": 网格搜索优化
            - "bayesian": 贝叶斯优化
        verbose (bool): 是否输出详细信息
        min_precision (float): 最小精确率约束 (仅在method="precision_constrained_recall"时使用)
        precision_constraint (float): 精确率约束 (向后兼容参数，等同于min_precision)
        payout_ratio (float): 盈亏比，用于盈利能力计算 (默认0.85)
        min_trades (int): 最小交易次数约束，用于盈利能力计算 (默认5)

    Returns:
        dict: 包含最优阈值和相关指标的字典
            {
                'optimal_threshold': float,
                'f1_score': float,
                'precision': float,
                'recall': float,
                'accuracy': float,
                'method': str,
                'threshold_range': tuple,
                'n_samples': int,
                'precision_constraint': float (如果使用了约束),
                'valid_thresholds_count': int (满足约束的阈值数量),
                # 🎯 新增盈利能力指标 (当method为盈利相关时)
                'expected_profit_per_trade': float,
                'total_trades': int,
                'win_rate': float,
                'risk_adjusted_return': float
            }
    """
    try:
        # 输入验证
        y_true = np.array(y_true)
        y_proba = np.array(y_proba)

        if len(y_true) != len(y_proba):
            logger.error(f"find_optimal_threshold [{target_name}]: y_true和y_proba长度不匹配: {len(y_true)} vs {len(y_proba)}")
            return None

        if len(y_true) == 0:
            logger.error(f"find_optimal_threshold [{target_name}]: 输入数据为空")
            return None

        # 检查标签是否为二分类
        unique_labels = np.unique(y_true)
        if len(unique_labels) != 2 or not all(label in [0, 1] for label in unique_labels):
            logger.error(f"find_optimal_threshold [{target_name}]: 标签必须是二分类(0,1)，当前标签: {unique_labels}")
            return None

        # 检查概率范围
        if np.any(y_proba < 0) or np.any(y_proba > 1):
            logger.warning(f"find_optimal_threshold [{target_name}]: 概率值超出[0,1]范围，将进行裁剪")
            y_proba = np.clip(y_proba, 0, 1)

        # 处理精确率约束参数（向后兼容）
        precision_constraint_value = min_precision or precision_constraint
        if method == "precision_constrained_recall" and precision_constraint_value is None:
            logger.warning(f"find_optimal_threshold [{target_name}]: 使用precision_constrained_recall方法但未指定精确率约束，使用默认值0.65")
            precision_constraint_value = 0.65

        if verbose:
            logger.info(f"find_optimal_threshold [{target_name}]: 开始寻找最优阈值，方法={method}，样本数={len(y_true)}")
            pos_ratio = np.mean(y_true) * 100
            logger.info(f"find_optimal_threshold [{target_name}]: 正类比例: {pos_ratio:.2f}%")
            if precision_constraint_value is not None:
                logger.info(f"find_optimal_threshold [{target_name}]: 精确率约束: >= {precision_constraint_value:.3f}")

        # 生成候选阈值
        # 使用更细粒度的阈值范围
        thresholds_coarse = np.linspace(0.1, 0.9, 81)  # 0.1到0.9，步长0.01
        thresholds_fine = np.linspace(0.4, 0.6, 41)    # 0.4到0.6，步长0.005
        thresholds = np.unique(np.concatenate([thresholds_coarse, thresholds_fine]))

        best_threshold = 0.5
        best_score = 0.0
        best_metrics = {}
        valid_thresholds_count = 0  # 满足约束的阈值数量

        # 存储所有阈值的结果用于分析
        threshold_results = []

        for threshold in thresholds:
            y_pred = (y_proba >= threshold).astype(int)

            # 计算各种指标
            try:
                f1 = f1_score(y_true, y_pred, zero_division=0)
                precision = precision_score(y_true, y_pred, zero_division=0)
                recall = recall_score(y_true, y_pred, zero_division=0)
                accuracy = accuracy_score(y_true, y_pred)

                # 计算特异性 (真负率)
                tn = np.sum((y_true == 0) & (y_pred == 0))
                fp = np.sum((y_true == 0) & (y_pred == 1))
                specificity = tn / (tn + fp) if (tn + fp) > 0 else 0

                # 🎯 新增：计算盈利能力指标 (当使用盈利相关方法时)
                profit_metrics = {}
                if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                    profit_metrics = _calculate_binary_simulated_profit(
                        y_true, y_proba, threshold, payout_ratio
                    )

                # 🎯 核心修改：支持多种优化方法，包括盈利能力优化
                # 初始化分数为0
                score = 0.0
                min_precision_req = precision_constraint_value if precision_constraint_value is not None else 0.0

                # 检查是否满足精确率约束
                precision_constraint_satisfied = precision >= min_precision_req

                # 🎯 新增：检查最小交易次数约束 (仅对盈利相关方法)
                min_trades_satisfied = True
                if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                    min_trades_satisfied = profit_metrics.get('total_trades', 0) >= min_trades

                # 只有在满足所有约束时，才使用相应方法的评分
                if precision_constraint_satisfied and min_trades_satisfied:
                    valid_thresholds_count += 1

                    if method == "precision_constrained_recall":
                        score = recall  # 在满足精确率约束的前提下最大化召回率
                    elif method == "f1":
                        score = f1  # 在满足精确率约束的前提下最大化F1分数
                    elif method == "precision_recall":
                        # 平衡精确率和召回率
                        score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                    elif method == "youden":
                        # Youden指数 = 敏感性 + 特异性 - 1
                        score = recall + specificity - 1
                    elif method == "balanced":
                        # 平衡精确率、召回率和准确率
                        score = (precision + recall + accuracy) / 3
                    # 🎯 新增：盈利能力优化方法
                    elif method == "simulated_profit":
                        # 基于模拟交易盈利能力优化
                        score = profit_metrics.get('expected_profit_per_trade', 0.0)
                    elif method == "risk_adjusted_return":
                        # 基于风险调整收益优化
                        score = profit_metrics.get('risk_adjusted_return', 0.0)
                    elif method == "expected_profit":
                        # 基于期望收益优化 (与simulated_profit相同，但名称更明确)
                        score = profit_metrics.get('expected_profit_per_trade', 0.0)
                    else:
                        logger.warning(f"find_optimal_threshold [{target_name}]: 未知方法 '{method}'，使用F1分数")
                        score = f1
                else:
                    # 🎯 如果不满足约束，给一个巨大的负分
                    if not precision_constraint_satisfied:
                        # 精确率约束惩罚
                        score = -1 + (precision - min_precision_req)
                        score = min(score, -0.001)
                    elif not min_trades_satisfied:
                        # 最小交易次数约束惩罚
                        score = -0.5
                    else:
                        score = -0.001

                # 🎯 存储结果时包含盈利能力指标
                result_entry = {
                    'threshold': threshold,
                    'f1': f1,
                    'precision': precision,
                    'recall': recall,
                    'accuracy': accuracy,
                    'specificity': specificity,
                    'score': score
                }

                # 如果使用盈利相关方法，添加盈利指标
                if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                    result_entry.update(profit_metrics)

                threshold_results.append(result_entry)

                # 更新最佳结果
                if score > best_score:
                    best_score = score
                    best_threshold = threshold
                    best_metrics = {
                        'f1_score': f1,
                        'precision': precision,
                        'recall': recall,
                        'accuracy': accuracy,
                        'specificity': specificity
                    }

                    # 🎯 如果使用盈利相关方法，添加盈利指标到最佳结果
                    if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                        best_metrics.update(profit_metrics)

            except Exception as e_metric:
                logger.warning(f"find_optimal_threshold [{target_name}]: 计算阈值{threshold:.3f}的指标时出错: {e_metric}")
                continue

        # 🎯 硬约束机制：处理完全没有满足约束的情况
        if precision_constraint_value is not None and valid_thresholds_count == 0:
            logger.warning(f"find_optimal_threshold [{target_name}]: 没有任何阈值满足精确率约束 >= {precision_constraint_value:.3f}")

            # 降级策略：在所有阈值中，选择一个F1分数最高的，即使它不满足精确率要求
            # 这比选择一个"最接近"但可能F1很低的阈值更有参考意义
            if threshold_results:
                best_fallback_result = max(threshold_results, key=lambda x: x['f1'])
                best_threshold = best_fallback_result['threshold']
                best_metrics = {
                    'f1_score': best_fallback_result['f1'],
                    'precision': best_fallback_result['precision'],
                    'recall': best_fallback_result['recall'],
                    'accuracy': best_fallback_result['accuracy'],
                    'specificity': best_fallback_result['specificity']
                }
                logger.warning(f"find_optimal_threshold [{target_name}]: 降级策略 - 选择F1分数最高的阈值 {best_threshold:.4f} "
                             f"(P:{best_metrics['precision']:.4f}, R:{best_metrics['recall']:.4f}, F1:{best_metrics['f1_score']:.4f})")
            else:
                # 极端情况：没有任何有效结果，使用默认值
                logger.error(f"find_optimal_threshold [{target_name}]: 没有任何有效的阈值结果，使用默认阈值 0.5")
                best_threshold = 0.5
                best_metrics = {
                    'f1_score': 0.0,
                    'precision': 0.0,
                    'recall': 0.0,
                    'accuracy': 0.0,
                    'specificity': 0.0
                }

        # 构建返回结果
        result = {
            'optimal_threshold': best_threshold,
            'f1_score': best_metrics.get('f1_score', 0),
            'precision': best_metrics.get('precision', 0),
            'recall': best_metrics.get('recall', 0),
            'accuracy': best_metrics.get('accuracy', 0),
            'specificity': best_metrics.get('specificity', 0),
            'method': method,
            'threshold_range': (thresholds.min(), thresholds.max()),
            'n_samples': len(y_true),
            'positive_ratio': np.mean(y_true)
        }

        # 🎯 如果使用盈利相关方法，添加盈利指标到返回结果
        if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
            result.update({
                'expected_profit_per_trade': best_metrics.get('expected_profit_per_trade', 0.0),
                'total_trades': best_metrics.get('total_trades', 0),
                'win_rate': best_metrics.get('win_rate', 0.0),
                'total_profit': best_metrics.get('total_profit', 0.0),
                'risk_adjusted_return': best_metrics.get('risk_adjusted_return', 0.0),
                'trade_frequency': best_metrics.get('trade_frequency', 0.0)
            })

        # 添加精确率约束相关信息
        if precision_constraint_value is not None:
            result['precision_constraint'] = precision_constraint_value
            result['valid_thresholds_count'] = valid_thresholds_count
            result['constraint_satisfied'] = best_metrics.get('precision', 0) >= precision_constraint_value

        if verbose:
            logger.info(f"find_optimal_threshold [{target_name}]: 最优阈值 = {best_threshold:.4f}")
            logger.info(f"find_optimal_threshold [{target_name}]: F1={best_metrics.get('f1_score', 0):.4f}, "
                       f"Precision={best_metrics.get('precision', 0):.4f}, "
                       f"Recall={best_metrics.get('recall', 0):.4f}, "
                       f"Accuracy={best_metrics.get('accuracy', 0):.4f}")

            # 🎯 如果使用盈利相关方法，显示盈利指标
            if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                logger.info(f"find_optimal_threshold [{target_name}]: 🎯 盈利指标 - "
                           f"期望收益/交易={best_metrics.get('expected_profit_per_trade', 0):.4f}, "
                           f"总交易={best_metrics.get('total_trades', 0)}, "
                           f"胜率={best_metrics.get('win_rate', 0):.3f}, "
                           f"风险调整收益={best_metrics.get('risk_adjusted_return', 0):.4f}")

            if precision_constraint_value is not None:
                constraint_status = "[OK] 满足" if result.get('constraint_satisfied', False) else "[FAIL] 不满足"
                logger.info(f"find_optimal_threshold [{target_name}]: 精确率约束 >= {precision_constraint_value:.3f}: {constraint_status}")
                logger.info(f"find_optimal_threshold [{target_name}]: 满足约束的阈值数量: {valid_thresholds_count}/{len(thresholds)}")

            # 🎯 如果使用盈利相关方法，显示最小交易次数约束
            if method in ["simulated_profit", "risk_adjusted_return", "expected_profit"]:
                trades_constraint_status = "[OK] 满足" if best_metrics.get('total_trades', 0) >= min_trades else "[FAIL] 不满足"
                logger.info(f"find_optimal_threshold [{target_name}]: 最小交易次数约束 >= {min_trades}: {trades_constraint_status}")

        # 可选：保存详细的阈值分析结果
        if verbose and len(threshold_results) > 0:
            if precision_constraint_value is not None:
                # 对于有精确率约束的情况，优先显示满足约束的最佳阈值
                valid_results = [res for res in threshold_results if res['precision'] >= precision_constraint_value]
                if valid_results:
                    if method == "precision_constrained_recall":
                        sorted_results = sorted(valid_results, key=lambda x: x['recall'], reverse=True)[:5]
                        logger.info(f"find_optimal_threshold [{target_name}]: 满足精确率约束的前5个最佳阈值 (按召回率排序):")
                    else:
                        sorted_results = sorted(valid_results, key=lambda x: x['f1'], reverse=True)[:5]
                        logger.info(f"find_optimal_threshold [{target_name}]: 满足精确率约束的前5个最佳阈值 (按F1分数排序):")
                else:
                    sorted_results = sorted(threshold_results, key=lambda x: x['f1'], reverse=True)[:5]
                    logger.info(f"find_optimal_threshold [{target_name}]: 无阈值满足约束，显示F1分数最高的前5个阈值:")
            else:
                # 无精确率约束时，显示评分最高的阈值
                sorted_results = sorted(threshold_results, key=lambda x: x['score'], reverse=True)[:5]
                logger.info(f"find_optimal_threshold [{target_name}]: 前5个最佳阈值:")

            for i, res in enumerate(sorted_results, 1):
                constraint_status = ""
                if precision_constraint_value is not None:
                    constraint_status = " ✓" if res['precision'] >= precision_constraint_value else " ✗"
                logger.info(f"  {i}. 阈值={res['threshold']:.4f}, "
                           f"F1={res['f1']:.4f}, "
                           f"Precision={res['precision']:.4f}, "
                           f"Recall={res['recall']:.4f}{constraint_status}")

        return result

    except Exception as e:
        logger.error(f"find_optimal_threshold [{target_name}]: 寻找最优阈值时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return None


def save_threshold_to_model_metadata(model_path, optimal_threshold, target_name=""):
    """
    将最优阈值保存到模型元数据中

    Args:
        model_path (str): 模型文件路径
        optimal_threshold (float): 最优阈值
        target_name (str): 目标名称

    Returns:
        bool: 是否保存成功
    """
    try:
        if not os.path.exists(model_path):
            logger.error(f"save_threshold_to_model_metadata [{target_name}]: 模型文件不存在: {model_path}")
            return False

        # 构建元数据文件路径
        model_dir = os.path.dirname(model_path)
        model_filename = os.path.basename(model_path)
        model_name_without_ext = os.path.splitext(model_filename)[0]
        metadata_path = os.path.join(model_dir, f"{model_name_without_ext}_metadata.json")

        # 读取现有元数据或创建新的
        metadata = {}
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            except Exception as e_read:
                logger.warning(f"save_threshold_to_model_metadata [{target_name}]: 读取现有元数据失败: {e_read}")
                metadata = {}

        # 更新阈值信息
        metadata['optimal_decision_threshold'] = optimal_threshold
        metadata['threshold_updated_at'] = datetime.now().isoformat()
        metadata['target_name'] = target_name

        # 保存元数据
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        logger.info(f"save_threshold_to_model_metadata [{target_name}]: 已保存最优阈值 {optimal_threshold:.4f} 到 {metadata_path}")
        return True

    except Exception as e:
        logger.error(f"save_threshold_to_model_metadata [{target_name}]: 保存阈值元数据时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return False


def split_independent_validation_set(X, y, val_ratio=0.15, random_state=42):
    """
    从训练数据中分离出独立的验证集用于阈值优化

    Args:
        X (np.ndarray): 特征数据
        y (np.ndarray): 标签数据
        val_ratio (float): 验证集比例
        random_state (int): 随机种子

    Returns:
        tuple: (X_train, X_val_threshold, y_train, y_val_threshold)
    """
    try:
        from sklearn.model_selection import train_test_split

        if len(X) < 10:  # 数据太少，不分离
            logger.warning(f"split_independent_validation_set: 数据量太少 ({len(X)})，不分离独立验证集")
            return X, None, y, None

        # 确保验证集至少有5个样本
        min_val_samples = max(5, int(len(X) * 0.05))
        val_samples = int(len(X) * val_ratio)
        val_samples = max(min_val_samples, val_samples)

        if val_samples >= len(X) * 0.3:  # 验证集不能超过30%
            val_samples = int(len(X) * 0.2)

        actual_val_ratio = val_samples / len(X)

        X_train, X_val_threshold, y_train, y_val_threshold = train_test_split(
            X, y,
            test_size=actual_val_ratio,
            random_state=random_state,
            stratify=y if len(np.unique(y)) > 1 else None
        )

        logger.info(f"split_independent_validation_set: 分离独立验证集 - 训练集: {len(X_train)}, 阈值验证集: {len(X_val_threshold)}")
        return X_train, X_val_threshold, y_train, y_val_threshold

    except Exception as e:
        logger.error(f"split_independent_validation_set: 分离独立验证集失败: {e}")
        return X, None, y, None


def optimize_threshold_with_independent_validation(models, scaler, X_val_threshold, y_val_threshold,
                                                 feature_names, target_name, method="f1", verbose=True):
    """
    使用独立验证集进行阈值优化

    Args:
        models (list): 训练好的模型列表（单模型时为[model]，多折时为[model1, model2, ...]）
        scaler: 数据缩放器
        X_val_threshold (np.ndarray): 独立验证集特征
        y_val_threshold (np.ndarray): 独立验证集标签
        feature_names (list): 特征名称列表
        target_name (str): 目标名称
        method (str): 优化方法
        verbose (bool): 是否显示详细信息

    Returns:
        dict: 阈值优化结果
    """
    try:
        if X_val_threshold is None or y_val_threshold is None or len(X_val_threshold) == 0:
            logger.warning(f"optimize_threshold_with_independent_validation [{target_name}]: 无独立验证集，跳过阈值优化")
            return None

        if len(models) == 0:
            logger.error(f"optimize_threshold_with_independent_validation [{target_name}]: 模型列表为空")
            return None

        # 缩放验证集数据
        X_val_scaled = scaler.transform(X_val_threshold)
        X_val_df = pd.DataFrame(X_val_scaled, columns=feature_names)

        # 获取集成预测概率
        if len(models) == 1:
            # 单模型
            y_proba_val = models[0].predict_proba(X_val_df)[:, 1]
            model_type = "单模型"
        else:
            # 多模型集成
            all_probas = []
            for i, model in enumerate(models):
                if model is not None:
                    proba = model.predict_proba(X_val_df)[:, 1]
                    all_probas.append(proba)
                else:
                    logger.warning(f"optimize_threshold_with_independent_validation [{target_name}]: 模型 {i} 为None，跳过")

            if len(all_probas) == 0:
                logger.error(f"optimize_threshold_with_independent_validation [{target_name}]: 没有有效的模型")
                return None

            # 平均集成
            y_proba_val = np.mean(all_probas, axis=0)
            model_type = f"{len(all_probas)}折集成"

        if verbose:
            print(f"    使用独立验证集进行阈值优化 ({model_type})")
            print(f"    验证集大小: {len(X_val_threshold)}, 正类比例: {np.mean(y_val_threshold):.3f}")

        # 使用独立验证集进行阈值优化
        threshold_result = find_optimal_threshold(
            y_val_threshold,
            y_proba_val,
            target_name=target_name + "_independent_val",
            method=method,
            verbose=verbose
        )

        if threshold_result and verbose:
            print(f"    独立验证集阈值优化完成: {threshold_result['optimal_threshold']:.4f}")

        return threshold_result

    except Exception as e:
        logger.error(f"optimize_threshold_with_independent_validation [{target_name}]: 阈值优化失败: {e}")
        logger.debug(traceback.format_exc())
        return None


def save_ensemble_threshold_to_main_metadata(main_meta_path, optimal_threshold_ensemble, target_name=""):
    """
    将集成模型最优阈值保存到主模型元数据文件中

    Args:
        main_meta_path (str): 主模型元数据文件路径
        optimal_threshold_ensemble (float): 集成模型最优阈值
        target_name (str): 目标名称

    Returns:
        bool: 是否保存成功
    """
    try:
        # 读取现有主元数据或创建新的
        metadata = {}
        if os.path.exists(main_meta_path):
            try:
                with open(main_meta_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            except Exception as e_read:
                logger.warning(f"save_ensemble_threshold_to_main_metadata [{target_name}]: 读取现有主元数据失败: {e_read}")
                metadata = {}

        # 更新集成模型阈值信息
        metadata['optimal_decision_threshold_ensemble'] = optimal_threshold_ensemble
        metadata['ensemble_threshold_updated_at'] = datetime.now().isoformat()
        metadata['ensemble_target_name'] = target_name

        # 保存主元数据
        with open(main_meta_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        logger.info(f"save_ensemble_threshold_to_main_metadata [{target_name}]: 已保存集成模型最优阈值 {optimal_threshold_ensemble:.4f} 到 {main_meta_path}")
        return True

    except Exception as e:
        logger.error(f"save_ensemble_threshold_to_main_metadata [{target_name}]: 保存集成阈值到主元数据时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return False


def load_ensemble_threshold_from_main_metadata(main_meta_path, target_name="", default_threshold=0.5):
    """
    从主模型元数据文件中加载集成模型最优阈值

    Args:
        main_meta_path (str): 主模型元数据文件路径
        target_name (str): 目标名称
        default_threshold (float): 默认阈值

    Returns:
        float: 加载的集成阈值或默认阈值
    """
    try:
        if not os.path.exists(main_meta_path):
            logger.warning(f"load_ensemble_threshold_from_main_metadata [{target_name}]: 主元数据文件不存在: {main_meta_path}")
            return default_threshold

        # 读取主元数据
        with open(main_meta_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        # 获取集成模型阈值
        ensemble_threshold = metadata.get('optimal_decision_threshold_ensemble', default_threshold)

        if isinstance(ensemble_threshold, (int, float)) and 0 <= ensemble_threshold <= 1:
            logger.info(f"load_ensemble_threshold_from_main_metadata [{target_name}]: 成功加载集成模型最优阈值 {ensemble_threshold:.4f}")
            return float(ensemble_threshold)
        else:
            logger.warning(f"load_ensemble_threshold_from_main_metadata [{target_name}]: 无效的集成阈值 {ensemble_threshold}，使用默认值 {default_threshold}")
            return default_threshold

    except Exception as e:
        logger.error(f"load_ensemble_threshold_from_main_metadata [{target_name}]: 加载集成阈值时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return default_threshold


def load_threshold_from_model_metadata(model_path, target_name="", default_threshold=0.5):
    """
    从模型元数据中加载最优阈值

    Args:
        model_path (str): 模型文件路径
        target_name (str): 目标名称
        default_threshold (float): 默认阈值

    Returns:
        float: 加载的阈值或默认阈值
    """
    try:
        if not os.path.exists(model_path):
            logger.warning(f"load_threshold_from_model_metadata [{target_name}]: 模型文件不存在: {model_path}")
            return default_threshold

        # 构建元数据文件路径
        model_dir = os.path.dirname(model_path)
        model_filename = os.path.basename(model_path)
        model_name_without_ext = os.path.splitext(model_filename)[0]
        metadata_path = os.path.join(model_dir, f"{model_name_without_ext}_metadata.json")

        if not os.path.exists(metadata_path):
            logger.info(f"load_threshold_from_model_metadata [{target_name}]: 元数据文件不存在，使用默认阈值 {default_threshold}")
            return default_threshold

        # 读取元数据
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        # 获取阈值
        threshold = metadata.get('optimal_decision_threshold', default_threshold)

        if isinstance(threshold, (int, float)) and 0 <= threshold <= 1:
            logger.info(f"load_threshold_from_model_metadata [{target_name}]: 成功加载最优阈值 {threshold:.4f}")
            return float(threshold)
        else:
            logger.warning(f"load_threshold_from_model_metadata [{target_name}]: 无效的阈值 {threshold}，使用默认值 {default_threshold}")
            return default_threshold

    except Exception as e:
        logger.error(f"load_threshold_from_model_metadata [{target_name}]: 加载阈值元数据时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return default_threshold


def predict_with_optimal_threshold(model, X, optimal_threshold=0.5):
    """
    使用最优阈值进行预测

    Args:
        model: 训练好的模型
        X: 特征数据
        optimal_threshold (float): 最优阈值

    Returns:
        np.ndarray: 预测结果 (0或1)
    """
    try:
        # 获取预测概率
        y_proba = model.predict_proba(X)[:, 1]

        # 使用最优阈值进行预测
        y_pred = (y_proba >= optimal_threshold).astype(int)

        return y_pred

    except Exception as e:
        logger.error(f"predict_with_optimal_threshold: 使用最优阈值预测时发生错误: {e}")
        # 回退到默认预测
        return model.predict(X)


def evaluate_model_with_optimal_threshold(model, X_test, y_test, optimal_threshold=0.5, target_name="", verbose=True):
    """
    使用最优阈值评估模型性能

    Args:
        model: 训练好的模型
        X_test: 测试特征
        y_test: 测试标签
        optimal_threshold (float): 最优阈值
        target_name (str): 目标名称
        verbose (bool): 是否输出详细信息

    Returns:
        dict: 评估结果字典
    """
    try:
        from sklearn.metrics import (accuracy_score, classification_report,
                                   brier_score_loss, f1_score, precision_score, recall_score)

        # 使用最优阈值进行预测
        y_pred_optimal = predict_with_optimal_threshold(model, X_test, optimal_threshold)

        # 获取预测概率
        y_proba = model.predict_proba(X_test)[:, 1]

        # 计算评估指标
        results = {
            'optimal_threshold_used': optimal_threshold,
            'test_accuracy_optimal': accuracy_score(y_test, y_pred_optimal),
            'test_brier_optimal': brier_score_loss(y_test, y_proba),
            'test_f1_optimal': f1_score(y_test, y_pred_optimal),
            'test_precision_optimal': precision_score(y_test, y_pred_optimal, zero_division=0),
            'test_recall_optimal': recall_score(y_test, y_pred_optimal, zero_division=0),
            'test_classification_report_optimal': classification_report(
                y_test, y_pred_optimal, output_dict=True, zero_division=0,
                target_names=['下跌 (0)', '上涨 (1)']
            )
        }

        if verbose:
            print(f"    📊 使用最优阈值 {optimal_threshold:.4f} 的测试集性能:")
            print(f"       准确率: {results['test_accuracy_optimal']:.4f}")
            print(f"       F1分数: {results['test_f1_optimal']:.4f}")
            print(f"       精确率: {results['test_precision_optimal']:.4f}")
            print(f"       召回率: {results['test_recall_optimal']:.4f}")
            print(f"       Brier分数: {results['test_brier_optimal']:.4f}")

        return results

    except Exception as e:
        logger.error(f"evaluate_model_with_optimal_threshold [{target_name}]: 评估时发生错误: {e}")
        logger.debug(traceback.format_exc())
        return {}


def _calculate_binary_simulated_profit(y_true, y_proba, threshold, payout_ratio=0.85):
    """
    🎯 简化的二分类模拟交易盈利计算函数

    从 calculate_simulated_profit_meta 中提取核心逻辑，适配二分类场景

    Args:
        y_true (array-like): 真实标签 (0或1)
        y_proba (array-like): 正类的预测概率
        threshold (float): 决策阈值
        payout_ratio (float): 盈亏比 (胜利时的收益率)

    Returns:
        dict: 包含盈利指标的字典
    """
    try:
        y_true = np.array(y_true)
        y_proba = np.array(y_proba)

        if len(y_true) == 0 or len(y_proba) == 0:
            return {
                'expected_profit_per_trade': 0.0,
                'total_trades': 0,
                'win_rate': 0.0,
                'total_profit': 0.0,
                'risk_adjusted_return': 0.0,
                'trade_frequency': 0.0
            }

        # 生成预测信号 (概率 >= 阈值时做多)
        y_pred_signal = (y_proba >= threshold).astype(int)

        # 计算交易统计
        total_trades = np.sum(y_pred_signal)  # 做多信号的次数
        total_wins = 0
        total_profit = 0.0

        if total_trades > 0:
            # 计算每笔交易的盈亏
            for i in range(len(y_true)):
                if y_pred_signal[i] == 1:  # 有做多信号
                    if y_true[i] == 1:  # 预测正确 (实际上涨)
                        total_profit += payout_ratio  # 盈利
                        total_wins += 1
                    else:  # 预测错误 (实际下跌)
                        total_profit -= 1.0  # 亏损全部本金

        # 计算指标
        win_rate = total_wins / total_trades if total_trades > 0 else 0.0
        expected_profit_per_trade = total_profit / total_trades if total_trades > 0 else 0.0
        trade_frequency = total_trades / len(y_true) if len(y_true) > 0 else 0.0

        # 风险调整收益 (考虑交易频率)
        risk_adjusted_return = expected_profit_per_trade * np.sqrt(total_trades) if total_trades > 0 else 0.0

        return {
            'expected_profit_per_trade': expected_profit_per_trade,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_profit': total_profit,
            'risk_adjusted_return': risk_adjusted_return,
            'trade_frequency': trade_frequency
        }

    except Exception as e:
        logger.error(f"_calculate_binary_simulated_profit: 计算盈利指标时出错: {e}")
        return {
            'expected_profit_per_trade': 0.0,
            'total_trades': 0,
            'win_rate': 0.0,
            'total_profit': 0.0,
            'risk_adjusted_return': 0.0,
            'trade_frequency': 0.0
        }


def two_stage_feature_selection(X_df, y_array, target_config, target_name, device_type="cpu", verbose=True):
    """
    🎯 两阶段特征选择：LightGBM重要性初筛 + RFE精选

    第一阶段：使用LightGBM特征重要性进行初步筛选，快速移除明显无用的特征
    第二阶段：使用RFE递归特征消除进行精细选择，找到最优特征子集

    Args:
        X_df (pd.DataFrame): 特征数据
        y_array (np.ndarray): 目标变量
        target_config (dict): 目标配置
        target_name (str): 目标名称
        device_type (str): 设备类型
        verbose (bool): 是否显示详细信息

    Returns:
        tuple: (selected_features, selection_stats)
            - selected_features: 选择的特征列表
            - selection_stats: 选择统计信息
    """
    if verbose:
        logger.info(f"two_stage_feature_selection [{target_name}]: 开始两阶段特征选择")
        logger.info(f"two_stage_feature_selection [{target_name}]: 输入特征数={len(X_df.columns)}, 样本数={len(y_array)}")

    try:
        from sklearn.feature_selection import RFECV
        from sklearn.model_selection import StratifiedKFold
        from lightgbm import LGBMClassifier
        from sklearn.preprocessing import StandardScaler

        # 初始化统计信息
        selection_stats = {
            'stage1_method': 'lightgbm_importance',
            'stage2_method': 'rfe',
            'original_features': len(X_df.columns),
            'stage1_features': 0,
            'stage2_features': 0,
            'stage1_time': 0,
            'stage2_time': 0,
            'total_time': 0
        }

        import time
        start_time = time.time()

        # === 第一阶段：LightGBM重要性初筛 ===
        if verbose:
            print(f"    🔍 第一阶段: LightGBM重要性初筛...")

        stage1_start = time.time()

        # 获取重要性筛选配置
        prescreening_ratio = target_config.get('importance_prescreening_ratio', 0.6)
        importance_model_params = {
            'n_estimators': target_config.get('importance_model_n_estimators', 200),
            'learning_rate': target_config.get('learning_rate_initial_imp', 0.05),
            'num_leaves': target_config.get('num_leaves_initial_imp', 15),
            'max_depth': target_config.get('max_depth_initial_imp', 5),
            'reg_alpha': target_config.get('reg_alpha_initial_imp', 5.0),
            'reg_lambda': target_config.get('reg_lambda_initial_imp', 5.0),
            'colsample_bytree': target_config.get('colsample_bytree_initial_imp', 0.7),
            'subsample': target_config.get('subsample_initial_imp', 0.7),
            'min_child_samples': target_config.get('min_child_samples_initial_imp', 30),
            'random_state': 42,
            'verbose': -1,
            'device_type': device_type
        }

        # 数据预处理
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_df)
        X_scaled_df = pd.DataFrame(X_scaled, columns=X_df.columns, index=X_df.index)

        # 训练重要性评估模型
        importance_model = LGBMClassifier(**importance_model_params)
        importance_model.fit(X_scaled_df, y_array)

        # 获取特征重要性
        importances = importance_model.feature_importances_
        feature_importance_df = pd.DataFrame({
            'feature': X_df.columns,
            'importance': importances
        }).sort_values('importance', ascending=False)

        # 第一阶段筛选：保留前N%的特征
        n_features_stage1 = max(
            int(len(X_df.columns) * prescreening_ratio),
            target_config.get('rfe_min_features_to_select', 40)  # 确保不少于RFE最小特征数
        )

        stage1_features = feature_importance_df.head(n_features_stage1)['feature'].tolist()

        stage1_end = time.time()
        selection_stats['stage1_features'] = len(stage1_features)
        selection_stats['stage1_time'] = stage1_end - stage1_start

        if verbose:
            print(f"      ✅ 第一阶段完成: {len(X_df.columns)} → {len(stage1_features)} 特征")
            print(f"      筛选比例: {len(stage1_features)/len(X_df.columns):.1%}")
            print(f"      耗时: {stage1_end - stage1_start:.2f}秒")
            print(f"      重要性范围: {feature_importance_df['importance'].max():.4f} - {feature_importance_df['importance'].min():.4f}")

        # === 第二阶段：RFE精选 ===
        if verbose:
            print(f"    🎯 第二阶段: RFE递归特征消除...")

        stage2_start = time.time()

        # 获取RFE配置
        rfe_cv_folds = target_config.get('rfe_cv_folds', 3)
        rfe_step = target_config.get('rfe_step', 1)
        rfe_scoring = target_config.get('rfe_scoring', 'f1')
        rfe_min_features = target_config.get('rfe_min_features_to_select', 40)

        # 🎯 处理自定义盈利导向评分函数
        if rfe_scoring in ['binary_simulated_profit', 'binary_profit_precision_composite', 'binary_risk_adjusted_return']:
            # 创建自定义评分函数
            from sklearn.metrics import make_scorer

            def custom_profit_scorer(y_true, y_proba, **kwargs):
                """
                🎯 Critical Bug Fix: 自定义盈利导向评分函数

                Args:
                    y_true: 真实标签
                    y_proba: 预测概率 (需要概率输入，不是二分类预测)
                    **kwargs: 额外参数
                """
                try:
                    # 确保y_proba是概率值
                    if hasattr(y_proba, 'shape') and len(y_proba.shape) == 2:
                        # 如果是二维数组，取正类概率
                        y_proba = y_proba[:, 1]

                    # 将概率限制在合理范围内
                    y_proba = np.clip(y_proba, 0.01, 0.99)

                    if rfe_scoring == 'binary_simulated_profit':
                        profit_result = _calculate_binary_simulated_profit(y_true, y_proba, threshold=0.5)
                        return profit_result.get('expected_profit_per_trade', 0.0)
                    elif rfe_scoring == 'binary_risk_adjusted_return':
                        profit_result = _calculate_binary_simulated_profit(y_true, y_proba, threshold=0.5)
                        return profit_result.get('risk_adjusted_return', 0.0)
                    elif rfe_scoring == 'binary_profit_precision_composite':
                        from sklearn.metrics import precision_score
                        profit_result = _calculate_binary_simulated_profit(y_true, y_proba, threshold=0.5)
                        expected_profit = profit_result.get('expected_profit_per_trade', 0.0)
                        y_pred_binary = (y_proba >= 0.5).astype(int)
                        precision = precision_score(y_true, y_pred_binary, zero_division=0)
                        return 0.7 * expected_profit + 0.3 * precision
                    else:
                        return 0.0
                except Exception as e:
                    if verbose:
                        print(f"      ⚠️ 自定义评分函数计算失败: {e}")
                    return 0.0

            # 🎯 Critical Bug Fix: 设置 needs_proba=True
            rfe_scoring = make_scorer(custom_profit_scorer, greater_is_better=True, needs_proba=True)
            if verbose:
                print(f"      🎯 使用自定义盈利导向评分函数: {target_config.get('rfe_scoring')}")
        else:
            if verbose:
                print(f"      📊 使用标准评分函数: {rfe_scoring}")

        # RFE估计器参数
        rfe_estimator_params = {
            'n_estimators': target_config.get('rfe_estimator_n_estimators', 100),
            'learning_rate': target_config.get('rfe_estimator_learning_rate', 0.1),
            'num_leaves': target_config.get('rfe_estimator_num_leaves', 31),
            'random_state': 42,
            'verbose': -1,
            'device_type': device_type
        }

        # 准备第二阶段数据
        X_stage1 = X_scaled_df[stage1_features]

        # 创建RFE估计器
        rfe_estimator = LGBMClassifier(**rfe_estimator_params)

        # 创建交叉验证策略
        cv_strategy = StratifiedKFold(n_splits=rfe_cv_folds, shuffle=True, random_state=42)

        # 执行RFE
        rfe_selector = RFECV(
            estimator=rfe_estimator,
            step=rfe_step,
            cv=cv_strategy,
            scoring=rfe_scoring,
            min_features_to_select=rfe_min_features,
            n_jobs=1,  # LightGBM已经使用多线程
            verbose=0
        )

        rfe_selector.fit(X_stage1, y_array)

        # 获取最终选择的特征
        stage2_features = X_stage1.columns[rfe_selector.support_].tolist()

        stage2_end = time.time()
        selection_stats['stage2_features'] = len(stage2_features)
        selection_stats['stage2_time'] = stage2_end - stage2_start
        selection_stats['total_time'] = stage2_end - start_time

        # 添加RFE详细信息
        selection_stats['rfe_details'] = {
            'optimal_features': rfe_selector.n_features_,
            'cv_scores': rfe_selector.cv_results_,
            'ranking': rfe_selector.ranking_.tolist() if hasattr(rfe_selector, 'ranking_') else None
        }

        if verbose:
            print(f"      ✅ 第二阶段完成: {len(stage1_features)} → {len(stage2_features)} 特征")
            print(f"      最终压缩比: {len(stage2_features)/len(X_df.columns):.1%}")
            print(f"      RFE最优特征数: {rfe_selector.n_features_}")
            print(f"      耗时: {stage2_end - stage2_start:.2f}秒")
            print(f"    🎉 两阶段特征选择完成，总耗时: {stage2_end - start_time:.2f}秒")

        # 记录最终统计
        selection_stats['compression_ratio'] = len(stage2_features) / len(X_df.columns)
        selection_stats['features_removed'] = len(X_df.columns) - len(stage2_features)

        return stage2_features, selection_stats

    except Exception as e:
        logger.error(f"two_stage_feature_selection [{target_name}]: 特征选择失败: {e}")
        if verbose:
            print(f"    ❌ 两阶段特征选择失败: {e}")

        # 返回原始特征列表作为备选
        fallback_stats = {
            'stage1_method': 'failed',
            'stage2_method': 'failed',
            'original_features': len(X_df.columns),
            'stage1_features': len(X_df.columns),
            'stage2_features': len(X_df.columns),
            'error': str(e)
        }
        return list(X_df.columns), fallback_stats


def _add_market_state_adaptive_features(df_out, cfg, C, H, L, O, V_feat, interval_str):
    """
    🎯 市场状态自适应特征工程 - 核心优化建议2.1的实现

    让模型自主学习在不同市场状态下哪些特征更重要，从"特征堆砌"走向"特征智能"

    功能：
    1. 引入市场状态作为特征
    2. 创建交互特征（技术指标 × 市场状态）
    3. 强化特征选择的上下文感知

    Args:
        df_out: 输出DataFrame
        cfg: 配置字典
        C, H, L, O: 价格序列
        V_feat: 成交量序列
        interval_str: 时间间隔字符串
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 检查是否启用市场状态自适应特征
        if not _get_cfg('enable_market_state_adaptive', default_value=False):
            logger.debug("_add_market_state_adaptive_features: (%s) 市场状态自适应特征未启用", interval_str)
            return

        logger.info("_add_market_state_adaptive_features: (%s) 开始计算市场状态自适应特征", interval_str)

        # === 1. 市场状态识别 ===
        market_states = _identify_market_regimes(df_out, cfg, C, H, L, interval_str)

        # 将市场状态作为分类特征（独热编码）
        for state_name, state_values in market_states.items():
            df_out[f'market_state_{state_name}'] = state_values

        # === 2. 创建交互特征 ===
        _create_regime_aware_interaction_features(df_out, cfg, market_states, interval_str)

        # === 3. 市场确定性特征 ===
        _add_market_certainty_features(df_out, cfg, market_states, C, H, L, interval_str)

        logger.info("_add_market_state_adaptive_features: (%s) 市场状态自适应特征计算完成", interval_str)

    except Exception as e:
        logger.error("_add_market_state_adaptive_features: (%s) 计算市场状态自适应特征时出错: %s", interval_str, e)
        logger.debug(traceback.format_exc())


def _identify_market_regimes(df_out, cfg, C, H, L, interval_str):
    """
    识别市场状态/制度

    Returns:
        dict: 包含不同市场状态的字典，每个状态都是0/1的序列
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 配置参数
        atr_period = _get_cfg('market_state_atr_period', default_value=14)
        adx_period = _get_cfg('market_state_adx_period', default_value=14)
        ema_fast = _get_cfg('market_state_ema_fast', default_value=12)
        ema_slow = _get_cfg('market_state_ema_slow', default_value=26)

        # 高波动阈值
        high_vol_threshold = _get_cfg('market_state_high_vol_threshold', default_value=2.5)
        # 低趋势强度阈值
        low_trend_threshold = _get_cfg('market_state_low_trend_threshold', default_value=20)
        # 强趋势阈值
        strong_trend_threshold = _get_cfg('market_state_strong_trend_threshold', default_value=30)

        market_states = {}

        # 计算ATR百分比（波动率指标）
        if len(C) >= atr_period:
            tr = np.maximum(H - L, np.maximum(abs(H - C.shift(1)), abs(L - C.shift(1))))
            atr = tr.rolling(window=atr_period, min_periods=1).mean()
            atr_percent = (atr / C) * 100
        else:
            atr_percent = pd.Series([1.0] * len(C), index=C.index)

        # 计算ADX（趋势强度指标）
        if len(C) >= adx_period:
            # 简化的ADX计算
            plus_dm = np.maximum(H.diff(), 0)
            minus_dm = np.maximum(-L.diff(), 0)
            plus_dm[H.diff() <= L.diff()] = 0
            minus_dm[L.diff() <= H.diff()] = 0

            tr_smooth = tr.rolling(window=adx_period, min_periods=1).mean()
            plus_di = 100 * (plus_dm.rolling(window=adx_period, min_periods=1).mean() / tr_smooth)
            minus_di = 100 * (minus_dm.rolling(window=adx_period, min_periods=1).mean() / tr_smooth)

            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di + 1e-10)
            adx = dx.rolling(window=adx_period, min_periods=1).mean()
        else:
            adx = pd.Series([15.0] * len(C), index=C.index)

        # 计算EMA差值（趋势方向）
        if len(C) >= ema_slow:
            ema_fast_val = C.ewm(span=ema_fast, min_periods=1).mean()
            ema_slow_val = C.ewm(span=ema_slow, min_periods=1).mean()
            ema_diff_pct = ((ema_fast_val - ema_slow_val) / ema_slow_val) * 100
        else:
            ema_diff_pct = pd.Series([0.0] * len(C), index=C.index)

        # === 市场状态分类 ===

        # 1. 高波动盘整（最危险）
        high_vol_condition = atr_percent > high_vol_threshold
        low_trend_condition = adx < low_trend_threshold
        market_states['high_vol_sideways'] = (high_vol_condition & low_trend_condition).astype(int)

        # 2. 低波动盘整
        low_vol_condition = atr_percent < (high_vol_threshold * 0.4)  # 低波动阈值
        market_states['low_vol_sideways'] = (low_vol_condition & low_trend_condition).astype(int)

        # 3. 强上升趋势
        strong_trend_condition = adx > strong_trend_threshold
        uptrend_condition = ema_diff_pct > 0.5
        market_states['strong_uptrend'] = (strong_trend_condition & uptrend_condition).astype(int)

        # 4. 强下降趋势
        downtrend_condition = ema_diff_pct < -0.5
        market_states['strong_downtrend'] = (strong_trend_condition & downtrend_condition).astype(int)

        # 5. 正常趋势
        moderate_trend_condition = (adx >= low_trend_threshold) & (adx <= strong_trend_threshold)
        market_states['normal_trend'] = moderate_trend_condition.astype(int)

        # 6. 高确定性状态（强趋势 + 低波动）
        high_certainty_condition = strong_trend_condition & (atr_percent < high_vol_threshold)
        market_states['high_certainty'] = high_certainty_condition.astype(int)

        # 7. 低确定性状态（弱趋势 + 高波动）
        low_certainty_condition = low_trend_condition & high_vol_condition
        market_states['low_certainty'] = low_certainty_condition.astype(int)

        # 存储原始指标供后续使用
        df_out['market_atr_percent'] = atr_percent
        df_out['market_adx'] = adx
        df_out['market_ema_diff_pct'] = ema_diff_pct

        logger.debug("_identify_market_regimes: (%s) 识别了 %d 种市场状态", interval_str, len(market_states))

        return market_states

    except Exception as e:
        logger.error("_identify_market_regimes: (%s) 市场状态识别失败: %s", interval_str, e)
        # 返回默认状态
        default_states = {
            'high_vol_sideways': pd.Series([0] * len(C), index=C.index),
            'low_vol_sideways': pd.Series([0] * len(C), index=C.index),
            'strong_uptrend': pd.Series([0] * len(C), index=C.index),
            'strong_downtrend': pd.Series([0] * len(C), index=C.index),
            'normal_trend': pd.Series([1] * len(C), index=C.index),  # 默认为正常趋势
            'high_certainty': pd.Series([0] * len(C), index=C.index),
            'low_certainty': pd.Series([0] * len(C), index=C.index)
        }
        return default_states


def _create_regime_aware_interaction_features(df_out, cfg, market_states, interval_str):
    """
    创建市场状态感知的交互特征

    将关键技术指标与市场状态进行交互，让模型学会在不同市场环境下关注不同的特征
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 获取要进行交互的技术指标列表
        interaction_indicators = _get_cfg('market_state_interaction_indicators',
                                        default_value=['rsi', 'macd', 'macd_histogram', 'bb_position',
                                                     'williams_r', 'volume_ratio', 'close_pos_in_candle'])

        # 获取要进行交互的市场状态
        interaction_states = _get_cfg('market_state_interaction_states',
                                    default_value=['strong_uptrend', 'strong_downtrend', 'high_vol_sideways',
                                                 'high_certainty', 'low_certainty'])

        interaction_count = 0

        for indicator in interaction_indicators:
            if indicator in df_out.columns:
                for state_name in interaction_states:
                    if state_name in market_states:
                        # 创建交互特征：指标值 × 市场状态
                        interaction_feature_name = f'{indicator}_x_{state_name}'
                        df_out[interaction_feature_name] = df_out[indicator] * market_states[state_name]
                        interaction_count += 1

                        # 创建条件特征：仅在特定市场状态下的指标值
                        conditional_feature_name = f'{indicator}_when_{state_name}'
                        df_out[conditional_feature_name] = np.where(
                            market_states[state_name] == 1,
                            df_out[indicator],
                            0
                        )
                        interaction_count += 1
            else:
                logger.debug("_create_regime_aware_interaction_features: (%s) 指标 %s 不存在，跳过交互特征",
                           interval_str, indicator)

        # 创建市场状态组合特征
        _create_market_state_combinations(df_out, market_states, interval_str)

        logger.info("_create_regime_aware_interaction_features: (%s) 创建了 %d 个交互特征",
                   interval_str, interaction_count)

    except Exception as e:
        logger.error("_create_regime_aware_interaction_features: (%s) 创建交互特征失败: %s", interval_str, e)


def _create_market_state_combinations(df_out, market_states, interval_str):
    """创建市场状态组合特征"""
    try:
        # 趋势 + 波动率组合
        if 'strong_uptrend' in market_states and 'high_certainty' in market_states:
            df_out['bullish_high_certainty'] = (market_states['strong_uptrend'] &
                                              market_states['high_certainty']).astype(int)

        if 'strong_downtrend' in market_states and 'high_certainty' in market_states:
            df_out['bearish_high_certainty'] = (market_states['strong_downtrend'] &
                                              market_states['high_certainty']).astype(int)

        # 危险状态组合
        if 'high_vol_sideways' in market_states and 'low_certainty' in market_states:
            df_out['dangerous_market'] = (market_states['high_vol_sideways'] |
                                        market_states['low_certainty']).astype(int)

        # 理想交易环境
        if 'high_certainty' in market_states:
            df_out['ideal_trading_env'] = market_states['high_certainty']

        logger.debug("_create_market_state_combinations: (%s) 创建市场状态组合特征完成", interval_str)

    except Exception as e:
        logger.error("_create_market_state_combinations: (%s) 创建市场状态组合特征失败: %s", interval_str, e)


def _add_market_certainty_features(df_out, cfg, market_states, C, H, L, interval_str):
    """
    添加市场确定性特征

    这些特征帮助模型识别市场的"可预测性"程度，为动态风险管理提供依据
    """
    def _get_cfg(key, default_value=None):
        return cfg.get(key.lower(), default_value)

    try:
        # 配置参数
        certainty_window = _get_cfg('market_certainty_window', default_value=10)

        # 1. 趋势一致性评分
        if 'market_ema_diff_pct' in df_out.columns:
            # 计算趋势方向的一致性
            trend_direction = np.sign(df_out['market_ema_diff_pct'])
            trend_consistency = trend_direction.rolling(window=certainty_window, min_periods=1).apply(
                lambda x: abs(x.sum()) / len(x), raw=True
            )
            df_out['trend_consistency_score'] = trend_consistency

        # 2. 波动率稳定性评分
        if 'market_atr_percent' in df_out.columns:
            atr_stability = 1.0 / (1.0 + df_out['market_atr_percent'].rolling(
                window=certainty_window, min_periods=1).std())
            df_out['volatility_stability_score'] = atr_stability.fillna(0.5)

        # 3. 价格行为可预测性
        price_momentum = C.pct_change()
        momentum_consistency = price_momentum.rolling(window=certainty_window, min_periods=1).apply(
            lambda x: 1.0 - abs(x.std()) if len(x) > 1 else 0.5, raw=True
        )
        df_out['price_predictability_score'] = momentum_consistency.fillna(0.5)

        # 4. 综合确定性评分
        certainty_components = []
        if 'trend_consistency_score' in df_out.columns:
            certainty_components.append(df_out['trend_consistency_score'])
        if 'volatility_stability_score' in df_out.columns:
            certainty_components.append(df_out['volatility_stability_score'])
        if 'price_predictability_score' in df_out.columns:
            certainty_components.append(df_out['price_predictability_score'])

        if certainty_components:
            df_out['market_certainty_composite'] = np.mean(certainty_components, axis=0)
        else:
            df_out['market_certainty_composite'] = 0.5

        # 5. 确定性分级
        certainty_score = df_out['market_certainty_composite']
        df_out['certainty_level_high'] = (certainty_score > 0.7).astype(int)
        df_out['certainty_level_medium'] = ((certainty_score >= 0.4) & (certainty_score <= 0.7)).astype(int)
        df_out['certainty_level_low'] = (certainty_score < 0.4).astype(int)

        # 6. 市场状态持续性
        for state_name, state_values in market_states.items():
            if isinstance(state_values, pd.Series):
                # 计算状态持续时间
                state_persistence = state_values.rolling(window=certainty_window, min_periods=1).sum()
                df_out[f'{state_name}_persistence'] = state_persistence / certainty_window

        logger.info("_add_market_certainty_features: (%s) 市场确定性特征计算完成", interval_str)

    except Exception as e:
        logger.error("_add_market_certainty_features: (%s) 计算市场确定性特征失败: %s", interval_str, e)
        logger.debug(traceback.format_exc())

