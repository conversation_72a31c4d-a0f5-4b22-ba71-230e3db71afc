#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件读取逻辑

验证纯加载模式下能正确恢复预训练模型的特征列表、超参数、校准器等配置
"""

import sys
import os
import json
import joblib
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config
import src.core.prediction as prediction
import src.core.data_utils as data_utils

def create_comprehensive_pretrained_model():
    """创建包含完整配置的预训练模型"""
    print("📦 创建包含完整配置的预训练模型...")
    
    # 创建测试目录
    test_model_dir = "test_config_loading_models"
    os.makedirs(test_model_dir, exist_ok=True)
    
    # 1. 创建特征列表
    mock_features = [
        'close_sma_10', 'close_sma_20', 'close_ema_12', 'close_ema_26',
        'rsi_14', 'macd_signal', 'bb_upper', 'bb_lower', 'bb_width',
        'volume_sma_20', 'volume_ratio', 'atr_14', 'adx_14',
        'market_state_high_vol_sideways', 'market_state_strong_uptrend',
        'market_state_extreme_volatility', 'market_state_panic_selling',
        'rsi_when_high_vol_sideways', 'macd_x_strong_uptrend',
        'dangerous_market', 'ideal_trading_env'
    ]
    
    print(f"  特征列表: {len(mock_features)} 个特征")
    
    # 2. 创建模型和缩放器
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler
    from sklearn.calibration import CalibratedClassifierCV
    
    # 生成模拟训练数据
    np.random.seed(42)
    X_mock = np.random.randn(1000, len(mock_features))
    y_mock = np.random.randint(0, 2, 1000)
    
    # 创建基础模型
    base_model = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42
    )
    base_model.fit(X_mock, y_mock)
    
    # 创建校准模型
    calibrated_model = CalibratedClassifierCV(base_model, method='isotonic', cv=3)
    calibrated_model.fit(X_mock, y_mock)
    
    # 创建缩放器
    scaler = StandardScaler()
    scaler.fit(X_mock)
    
    # 3. 保存模型文件 (包括校准版本)
    model_files_info = [
        {"filename": "model_BTC_15m_UP_30m_fold0.joblib", "model": base_model, "type": "base"},
        {"filename": "model_BTC_15m_UP_30m_fold0_calibrated.joblib", "model": calibrated_model, "type": "calibrated"},
        {"filename": "model_BTC_15m_UP_30m_fold1.joblib", "model": base_model, "type": "base"},
        {"filename": "model_BTC_15m_UP_30m_fold1_calibrated.joblib", "model": calibrated_model, "type": "calibrated"},
    ]
    
    for model_info in model_files_info:
        model_path = os.path.join(test_model_dir, model_info["filename"])
        joblib.dump(model_info["model"], model_path)
        print(f"  ✅ 创建{model_info['type']}模型: {model_info['filename']}")
    
    # 4. 保存缩放器
    scaler_file = "scaler_BTC_15m_UP_30m.joblib"
    scaler_path = os.path.join(test_model_dir, scaler_file)
    joblib.dump(scaler, scaler_path)
    print(f"  ✅ 创建缩放器: {scaler_file}")
    
    # 5. 保存特征列表
    feature_files = [
        "final_selected_features_BTC_15m_UP_30m.json",
        "final_selected_features_two_stage_BTC_15m_UP_30m.json"
    ]
    
    for feature_file in feature_files:
        feature_path = os.path.join(test_model_dir, feature_file)
        with open(feature_path, 'w') as f:
            json.dump(mock_features, f, indent=2)
        print(f"  ✅ 创建特征文件: {feature_file}")
    
    # 6. 创建详细的元数据文件
    fold_artifacts = []
    for i in range(2):  # 2个fold
        fold_artifact = {
            "fold_index": i,
            "model_filename": f"model_BTC_15m_UP_30m_fold{i}.joblib",
            "calibrated_model_filename": f"model_BTC_15m_UP_30m_fold{i}_calibrated.joblib",
            "optimal_threshold": 0.6 + i * 0.01,
            "validation_metrics": {
                "accuracy": 0.75 + i * 0.02,
                "precision": 0.73 + i * 0.01,
                "recall": 0.71 + i * 0.015,
                "f1_score": 0.72 + i * 0.01,
                "auc_roc": 0.78 + i * 0.01,
                "brier_score": 0.22 - i * 0.01
            }
        }
        fold_artifacts.append(fold_artifact)
    
    # 主元数据文件
    main_metadata = {
        "target_name": "BTC_15m_UP",
        "prediction_minutes": 30,
        "interval": "15m",
        "model_type": "RandomForestClassifier",
        "feature_count": len(mock_features),
        "training_date": datetime.now().isoformat(),
        "fold_count": 2,
        "ensemble_threshold": 0.6234,
        "scaler_filename": scaler_file,
        "feature_list_filename": feature_files[0],
        "fold_model_artifacts": fold_artifacts,
        "model_parameters": {
            "n_estimators": 100,
            "max_depth": 10,
            "min_samples_split": 5,
            "min_samples_leaf": 2,
            "random_state": 42
        },
        "training_config": {
            "rfe_enable": False,
            "importance_thresholding_enable": False,
            "optuna_enable": False,
            "enable_probability_calibration": True,
            "scaler_type": "standard"
        }
    }
    
    metadata_file = "model_meta_BTC_15m_UP_30m.json"
    metadata_path = os.path.join(test_model_dir, metadata_file)
    with open(metadata_path, 'w') as f:
        json.dump(main_metadata, f, indent=2)
    print(f"  ✅ 创建主元数据: {metadata_file}")
    
    # 7. 创建阈值优化结果文件
    threshold_results = {
        "ensemble_optimal_threshold": 0.6234,
        "fold_thresholds": [artifact["optimal_threshold"] for artifact in fold_artifacts],
        "threshold_optimization_method": "f1_score",
        "validation_scores": {
            "ensemble_f1": 0.7456,
            "ensemble_precision": 0.7234,
            "ensemble_recall": 0.7123
        }
    }
    
    threshold_file = "threshold_optimization_results_BTC_15m_UP_30m.json"
    threshold_path = os.path.join(test_model_dir, threshold_file)
    with open(threshold_path, 'w') as f:
        json.dump(threshold_results, f, indent=2)
    print(f"  ✅ 创建阈值结果: {threshold_file}")
    
    print(f"📦 完整配置模型创建完成: {test_model_dir}")
    return test_model_dir, main_metadata, mock_features

def test_model_loading_with_prediction():
    """测试使用预测模块加载模型"""
    print("\n🔧 测试使用预测模块加载模型...")
    
    # 创建测试配置
    test_config = {
        'name': 'BTC_15m_UP',
        'symbol': 'BTCUSDT',
        'interval': '15m',
        'prediction_periods': [30],
        'prediction_minutes_display': 30,  # 添加缺失的参数
        'model_save_dir': 'test_config_loading_models',
        'enable_probability_calibration': True,
        'scaler_type': 'standard',
        'rfe_enable': False,
        'importance_thresholding_enable': False,
        'optuna_enable': False
    }
    
    try:
        # 尝试加载模型工件
        print("  尝试加载模型工件...")
        
        # 使用预测模块的加载函数
        loaded_models, scaler, model_type_str, load_success, model_meta, fold_thresholds = \
            prediction._load_model_artifacts(test_config, test_config['name'])
        
        if load_success:
            print(f"  ✅ 模型加载成功")
            print(f"    模型类型: {model_type_str}")
            print(f"    加载的模型数量: {len(loaded_models) if loaded_models else 0}")
            print(f"    缩放器类型: {type(scaler).__name__ if scaler else 'None'}")
            print(f"    阈值数量: {len(fold_thresholds) if fold_thresholds else 0}")
            
            if model_meta:
                print(f"    元数据特征数: {model_meta.get('feature_count', 'N/A')}")
                print(f"    训练日期: {model_meta.get('training_date', 'N/A')[:10] if model_meta.get('training_date') else 'N/A'}")
            
            return True
        else:
            print(f"  ❌ 模型加载失败: {model_type_str}")
            return False
            
    except Exception as e:
        print(f"  ❌ 模型加载过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_list_recovery(test_model_dir, expected_features):
    """测试特征列表恢复"""
    print(f"\n📋 测试特征列表恢复...")
    
    # 测试加载不同的特征文件
    feature_files = [
        "final_selected_features_BTC_15m_UP_30m.json",
        "final_selected_features_two_stage_BTC_15m_UP_30m.json"
    ]
    
    recovery_success = True
    
    for feature_file in feature_files:
        feature_path = os.path.join(test_model_dir, feature_file)
        
        if os.path.exists(feature_path):
            try:
                with open(feature_path, 'r') as f:
                    loaded_features = json.load(f)
                
                print(f"  ✅ 成功加载 {feature_file}")
                print(f"    特征数量: {len(loaded_features)}")
                print(f"    特征匹配: {'✅' if loaded_features == expected_features else '❌'}")
                
                if loaded_features != expected_features:
                    print(f"    预期: {len(expected_features)} 个特征")
                    print(f"    实际: {len(loaded_features)} 个特征")
                    recovery_success = False
                    
            except Exception as e:
                print(f"  ❌ 加载 {feature_file} 失败: {e}")
                recovery_success = False
        else:
            print(f"  ❌ 特征文件不存在: {feature_file}")
            recovery_success = False
    
    return recovery_success

def test_hyperparameter_recovery(test_model_dir):
    """测试超参数恢复"""
    print(f"\n⚙️  测试超参数恢复...")
    
    metadata_file = "model_meta_BTC_15m_UP_30m.json"
    metadata_path = os.path.join(test_model_dir, metadata_file)
    
    if os.path.exists(metadata_path):
        try:
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            print(f"  ✅ 成功加载元数据文件")
            
            # 检查模型参数
            model_params = metadata.get('model_parameters', {})
            print(f"  模型参数:")
            for param, value in model_params.items():
                print(f"    {param}: {value}")
            
            # 检查训练配置
            training_config = metadata.get('training_config', {})
            print(f"  训练配置:")
            for config_key, config_value in training_config.items():
                print(f"    {config_key}: {config_value}")
            
            # 验证纯加载模式配置
            is_pure_loading = (
                not training_config.get('rfe_enable', False) and
                not training_config.get('importance_thresholding_enable', False) and
                not training_config.get('optuna_enable', False)
            )
            
            print(f"  配置验证: {'✅ 纯加载模式' if is_pure_loading else '❌ 非纯加载模式'}")
            
            return len(model_params) > 0 and is_pure_loading
            
        except Exception as e:
            print(f"  ❌ 加载元数据失败: {e}")
            return False
    else:
        print(f"  ❌ 元数据文件不存在: {metadata_path}")
        return False

def test_calibrator_recovery(test_model_dir):
    """测试校准器恢复"""
    print(f"\n🎯 测试校准器恢复...")
    
    # 检查校准模型文件
    calibrated_files = [f for f in os.listdir(test_model_dir) if 'calibrated' in f and f.endswith('.joblib')]
    
    print(f"  发现校准模型文件: {len(calibrated_files)} 个")
    
    if calibrated_files:
        try:
            # 尝试加载一个校准模型
            calibrated_path = os.path.join(test_model_dir, calibrated_files[0])
            calibrated_model = joblib.load(calibrated_path)
            
            print(f"  ✅ 成功加载校准模型: {type(calibrated_model).__name__}")
            
            # 检查是否是CalibratedClassifierCV
            from sklearn.calibration import CalibratedClassifierCV
            is_calibrated = isinstance(calibrated_model, CalibratedClassifierCV)
            print(f"  校准器类型验证: {'✅ CalibratedClassifierCV' if is_calibrated else '❌ 非校准模型'}")
            
            return is_calibrated
            
        except Exception as e:
            print(f"  ❌ 加载校准模型失败: {e}")
            return False
    else:
        print(f"  ❌ 未找到校准模型文件")
        return False

def cleanup_test_files(test_model_dir):
    """清理测试文件"""
    print(f"\n🧹 清理测试文件...")
    
    if os.path.exists(test_model_dir):
        import shutil
        shutil.rmtree(test_model_dir)
        print(f"  ✅ 已删除测试目录: {test_model_dir}")

def main():
    """主函数"""
    print("🚀 配置文件读取逻辑测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 创建包含完整配置的预训练模型
    test_model_dir, metadata, expected_features = create_comprehensive_pretrained_model()
    
    # 2. 测试模型加载
    model_loading_result = test_model_loading_with_prediction()
    test_results.append(("模型加载", model_loading_result))
    
    # 3. 测试特征列表恢复
    feature_recovery_result = test_feature_list_recovery(test_model_dir, expected_features)
    test_results.append(("特征列表恢复", feature_recovery_result))
    
    # 4. 测试超参数恢复
    hyperparameter_result = test_hyperparameter_recovery(test_model_dir)
    test_results.append(("超参数恢复", hyperparameter_result))
    
    # 5. 测试校准器恢复
    calibrator_result = test_calibrator_recovery(test_model_dir)
    test_results.append(("校准器恢复", calibrator_result))
    
    # 6. 清理测试文件
    cleanup_test_files(test_model_dir)
    
    # 总结结果
    print("\n" + "=" * 60)
    print("🎯 配置文件读取逻辑测试结果:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n总体结果: {'🎉 所有测试通过' if all_passed else '💥 部分测试失败'}")
    
    if all_passed:
        print("\n🎯 配置文件读取逻辑验证成功！")
        print("  ✅ 模型加载逻辑正确")
        print("  ✅ 特征列表恢复正确")
        print("  ✅ 超参数恢复正确")
        print("  ✅ 校准器恢复正确")
        print("\n💡 纯加载模式已完全就绪:")
        print("  - 系统能正确识别纯加载模式配置")
        print("  - 预训练模型文件能被正确加载")
        print("  - 特征列表、超参数、校准器等配置能被完整恢复")
        print("  - 跳过训练和优化步骤，直接进入预测模式")
    else:
        print("\n💥 配置文件读取逻辑测试失败！")
        print("  请检查相关的加载逻辑")

if __name__ == "__main__":
    main()
