#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试纯加载模式

验证当rfe_enable、importance_thresholding_enable、optuna_enable都为False时，
系统是否正确进入纯加载模式并从trained_models_btc_15m文件夹加载预训练模型
"""

import sys
import os
import json
import joblib
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import config

def create_mock_pretrained_models():
    """创建模拟的预训练模型文件用于测试"""
    print("📦 创建模拟预训练模型文件...")
    
    # 创建测试目录
    test_model_dir = "test_trained_models_btc_15m"
    os.makedirs(test_model_dir, exist_ok=True)
    
    # 1. 创建模拟的模型文件
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler
    
    # 创建简单的模型
    mock_model = RandomForestClassifier(n_estimators=10, random_state=42)
    mock_features = [f'feature_{i:03d}' for i in range(20)]
    
    # 生成一些模拟训练数据来拟合模型
    X_mock = np.random.randn(100, 20)
    y_mock = np.random.randint(0, 2, 100)
    mock_model.fit(X_mock, y_mock)
    
    # 保存模型文件
    model_files = [
        "model_BTC_15m_UP_30m_fold0.joblib",
        "model_BTC_15m_UP_30m_fold1.joblib", 
        "model_BTC_15m_UP_30m_fold2.joblib",
        "model_BTC_15m_UP_30m_fold3.joblib",
        "model_BTC_15m_UP_30m_fold4.joblib"
    ]
    
    for model_file in model_files:
        model_path = os.path.join(test_model_dir, model_file)
        joblib.dump(mock_model, model_path)
        print(f"  ✅ 创建模型文件: {model_file}")
    
    # 2. 创建缩放器文件
    mock_scaler = StandardScaler()
    mock_scaler.fit(X_mock)
    scaler_file = "scaler_BTC_15m_UP_30m.joblib"
    scaler_path = os.path.join(test_model_dir, scaler_file)
    joblib.dump(mock_scaler, scaler_path)
    print(f"  ✅ 创建缩放器文件: {scaler_file}")
    
    # 3. 创建特征文件
    feature_files = [
        "final_selected_features_BTC_15m_UP_30m.json",
        "final_selected_features_two_stage_BTC_15m_UP_30m.json"
    ]
    
    for feature_file in feature_files:
        feature_path = os.path.join(test_model_dir, feature_file)
        with open(feature_path, 'w') as f:
            json.dump(mock_features, f, indent=2)
        print(f"  ✅ 创建特征文件: {feature_file}")
    
    # 4. 创建元数据文件
    mock_metadata = {
        "target_name": "BTC_15m_UP",
        "prediction_minutes": 30,
        "model_type": "LGBMClassifier",
        "feature_count": len(mock_features),
        "training_date": datetime.now().isoformat(),
        "fold_count": 5,
        "ensemble_threshold": 0.6234,
        "fold_thresholds": [0.6123, 0.6234, 0.6345, 0.6456, 0.6567]
    }
    
    metadata_file = "model_meta_BTC_15m_UP_30m.json"
    metadata_path = os.path.join(test_model_dir, metadata_file)
    with open(metadata_path, 'w') as f:
        json.dump(mock_metadata, f, indent=2)
    print(f"  ✅ 创建元数据文件: {metadata_file}")
    
    print(f"📦 模拟预训练模型创建完成: {test_model_dir}")
    return test_model_dir

def test_pure_loading_mode_detection():
    """测试纯加载模式检测逻辑"""
    print("\n🎯 测试纯加载模式检测逻辑...")
    
    # 测试用例1: 纯加载模式 (所有参数都为False)
    pure_loading_config = {
        'rfe_enable': False,
        'importance_thresholding_enable': False,
        'optuna_enable': False
    }
    
    is_pure_loading = (
        not pure_loading_config.get('rfe_enable', False) and
        not pure_loading_config.get('importance_thresholding_enable', False) and
        not pure_loading_config.get('optuna_enable', False)
    )
    
    print(f"  测试用例1 - 纯加载模式:")
    print(f"    rfe_enable: {pure_loading_config['rfe_enable']}")
    print(f"    importance_thresholding_enable: {pure_loading_config['importance_thresholding_enable']}")
    print(f"    optuna_enable: {pure_loading_config['optuna_enable']}")
    print(f"    检测结果: {'✅ 纯加载模式' if is_pure_loading else '❌ 非纯加载模式'}")
    
    # 测试用例2: 非纯加载模式 (至少一个参数为True)
    mixed_config = {
        'rfe_enable': False,
        'importance_thresholding_enable': True,  # 这个为True
        'optuna_enable': False
    }
    
    is_mixed_loading = (
        not mixed_config.get('rfe_enable', False) and
        not mixed_config.get('importance_thresholding_enable', False) and
        not mixed_config.get('optuna_enable', False)
    )
    
    print(f"\n  测试用例2 - 混合模式:")
    print(f"    rfe_enable: {mixed_config['rfe_enable']}")
    print(f"    importance_thresholding_enable: {mixed_config['importance_thresholding_enable']}")
    print(f"    optuna_enable: {mixed_config['optuna_enable']}")
    print(f"    检测结果: {'✅ 纯加载模式' if is_mixed_loading else '❌ 非纯加载模式'}")
    
    return is_pure_loading and not is_mixed_loading

def test_pretrained_model_detection(test_model_dir):
    """测试预训练模型文件检测逻辑"""
    print(f"\n🔍 测试预训练模型文件检测逻辑...")
    print(f"  测试目录: {test_model_dir}")
    
    if not os.path.exists(test_model_dir):
        print(f"  ❌ 测试目录不存在")
        return False
    
    # 检查关键文件是否存在
    model_files = [f for f in os.listdir(test_model_dir) if f.endswith('.joblib') and 'model_' in f]
    scaler_files = [f for f in os.listdir(test_model_dir) if f.endswith('.joblib') and 'scaler_' in f]
    feature_files = [f for f in os.listdir(test_model_dir) if f.endswith('.json') and 'features' in f]
    metadata_files = [f for f in os.listdir(test_model_dir) if f.endswith('.json') and 'meta' in f]
    
    print(f"  📊 文件检测结果:")
    print(f"    模型文件: {len(model_files)} 个 {model_files[:3] if model_files else '[]'}")
    print(f"    缩放器文件: {len(scaler_files)} 个 {scaler_files}")
    print(f"    特征文件: {len(feature_files)} 个 {feature_files}")
    print(f"    元数据文件: {len(metadata_files)} 个 {metadata_files}")
    
    # 检查文件完整性
    files_complete = model_files and scaler_files and feature_files
    
    if files_complete:
        print(f"  ✅ 预训练模型文件完整，可以进入纯加载模式")
        
        # 尝试加载一个模型文件验证
        try:
            test_model_path = os.path.join(test_model_dir, model_files[0])
            test_model = joblib.load(test_model_path)
            print(f"  ✅ 模型文件加载测试成功: {type(test_model).__name__}")
        except Exception as e:
            print(f"  ❌ 模型文件加载测试失败: {e}")
            return False
        
        # 尝试加载缩放器文件验证
        try:
            test_scaler_path = os.path.join(test_model_dir, scaler_files[0])
            test_scaler = joblib.load(test_scaler_path)
            print(f"  ✅ 缩放器文件加载测试成功: {type(test_scaler).__name__}")
        except Exception as e:
            print(f"  ❌ 缩放器文件加载测试失败: {e}")
            return False
        
        # 尝试加载特征文件验证
        try:
            test_feature_path = os.path.join(test_model_dir, feature_files[0])
            with open(test_feature_path, 'r') as f:
                test_features = json.load(f)
            print(f"  ✅ 特征文件加载测试成功: {len(test_features)} 个特征")
        except Exception as e:
            print(f"  ❌ 特征文件加载测试失败: {e}")
            return False
        
        return True
    else:
        print(f"  ❌ 预训练模型文件不完整，需要执行训练流程")
        return False

def test_config_loading_logic():
    """测试配置加载逻辑"""
    print(f"\n⚙️  测试配置加载逻辑...")
    
    # 检查当前配置中的相关参数
    if hasattr(config, 'PREDICTION_TARGETS') and config.PREDICTION_TARGETS:
        target_config = config.PREDICTION_TARGETS[0]  # 使用第一个目标配置
        
        print(f"  当前配置参数:")
        print(f"    rfe_enable: {target_config.get('rfe_enable', 'Not Set')}")
        print(f"    importance_thresholding_enable: {target_config.get('importance_thresholding_enable', 'Not Set')}")
        print(f"    optuna_enable: {target_config.get('optuna_enable', 'Not Set')}")
        print(f"    model_save_dir: {target_config.get('model_save_dir', 'Not Set')}")
        
        # 检查是否为纯加载模式
        is_pure_loading = (
            not target_config.get('rfe_enable', False) and
            not target_config.get('importance_thresholding_enable', False) and
            not target_config.get('optuna_enable', False)
        )
        
        print(f"  配置检测结果: {'✅ 纯加载模式' if is_pure_loading else '❌ 非纯加载模式'}")
        
        # 检查模型目录
        model_dir = target_config.get('model_save_dir')
        if model_dir and os.path.exists(model_dir):
            print(f"  ✅ 模型目录存在: {model_dir}")
            
            # 检查目录中的文件
            files = os.listdir(model_dir)
            model_files = [f for f in files if f.endswith('.joblib') and 'model_' in f]
            print(f"  模型文件数量: {len(model_files)}")
            
            return is_pure_loading and len(model_files) > 0
        else:
            print(f"  ❌ 模型目录不存在或未配置: {model_dir}")
            return False
    else:
        print(f"  ❌ 未找到预测目标配置")
        return False

def cleanup_test_files(test_model_dir):
    """清理测试文件"""
    print(f"\n🧹 清理测试文件...")
    
    if os.path.exists(test_model_dir):
        import shutil
        shutil.rmtree(test_model_dir)
        print(f"  ✅ 已删除测试目录: {test_model_dir}")

def main():
    """主函数"""
    print("🚀 纯加载模式测试")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试纯加载模式检测逻辑
    detection_result = test_pure_loading_mode_detection()
    test_results.append(("纯加载模式检测", detection_result))
    
    # 2. 创建模拟预训练模型
    test_model_dir = create_mock_pretrained_models()
    
    # 3. 测试预训练模型文件检测
    file_detection_result = test_pretrained_model_detection(test_model_dir)
    test_results.append(("预训练模型文件检测", file_detection_result))
    
    # 4. 测试配置加载逻辑
    config_result = test_config_loading_logic()
    test_results.append(("配置加载逻辑", config_result))
    
    # 5. 清理测试文件
    cleanup_test_files(test_model_dir)
    
    # 总结结果
    print("\n" + "=" * 60)
    print("🎯 纯加载模式测试结果:")
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n总体结果: {'🎉 所有测试通过' if all_passed else '💥 部分测试失败'}")
    
    if all_passed:
        print("\n🎯 纯加载模式逻辑验证成功！")
        print("  ✅ 纯加载模式检测逻辑正确")
        print("  ✅ 预训练模型文件检测正确")
        print("  ✅ 配置加载逻辑正确")
        print("\n💡 建议:")
        print("  - 确保trained_models_btc_15m目录包含完整的预训练模型文件")
        print("  - 验证配置文件中三个参数都设置为False")
        print("  - 测试实际的预测流程以确保模型加载正常")
    else:
        print("\n💥 纯加载模式测试失败！")
        print("  请检查相关逻辑和配置")

if __name__ == "__main__":
    main()
